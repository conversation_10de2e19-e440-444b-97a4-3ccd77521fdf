# Environment Configuration Template
# Copy this file to .env and modify values as needed

# Application Configuration
SPRING_PROFILES_ACTIVE=docker,prod
INSTANCE_ID=app1
SERVER_PORT=8080
MANAGEMENT_SERVER_PORT=9090

# Logging Configuration
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_EXAMPLE=DEBUG

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# MQTT Configuration
MQTT_BROKER_URL=tcp://mosquitto:1883
MQTT_USERNAME=
MQTT_PASSWORD=

# Security Configuration
JWT_SECRET=your-jwt-secret-key-change-in-production-min-32-chars
ENCRYPTION_KEY=your-encryption-key-must-be-32-characters-long

# Cluster Configuration
CLUSTER_ENABLED=true

# Performance Configuration
JAVA_OPTS=-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+UseStringDeduplication -XX:MaxGCPauseMillis=200 -Djava.security.egd=file:/dev/./urandom

# Monitoring Configuration
PROMETHEUS_ENABLED=true
GRAFANA_ADMIN_PASSWORD=admin123

# Network Configuration
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024

# Database Configuration (if using external database)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=mqtt_websocket
# DB_USERNAME=app_user
# DB_PASSWORD=secure_password

# External Services (if applicable)
# EXTERNAL_API_URL=https://api.example.com
# EXTERNAL_API_KEY=your-api-key

# SSL Configuration (for production)
# SSL_ENABLED=true
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30

# Alert Configuration
# ALERT_EMAIL=<EMAIL>
# ALERT_WEBHOOK_URL=https://hooks.slack.com/services/xxx/yyy/zzz