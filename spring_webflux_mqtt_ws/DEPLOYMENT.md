# MQTT-WebSocket 应用部署指南

## 概述

本文档描述了基于 Spring Boot + Spring Integration 的 MQTT-WebSocket 应用的容器化部署流程，包括开发、测试和生产环境的部署配置。

## 系统要求

### 最低硬件要求
- **CPU**: 2 核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 1Gbps 网络连接

### 推荐硬件要求
- **CPU**: 4+ 核心
- **内存**: 8GB+ RAM
- **存储**: 50GB+ SSD
- **网络**: 10Gbps 网络连接

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Java 21+ (用于本地开发)
- Maven 3.9+ (用于本地构建)

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd spring-integration-mqtt-websocket
```

### 2. 环境准备
```bash
# 创建必要的目录
mkdir -p logs/app1 logs/app2 logs/aggregated logs/buffer
mkdir -p config/ssl config/grafana/dashboards config/grafana/datasources

# 设置权限 (Linux/macOS)
chmod +x scripts/health-check.sh
```

### 3. 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app1 app2
```

### 4. 验证部署
```bash
# 健康检查
curl http://localhost:9090/actuator/health

# WebSocket 测试 (需要 websocat)
echo '{"type":"ping"}' | websocat ws://localhost:8080/websocket

# API 测试
curl http://localhost:8080/api/health
```

## 服务架构

### 核心服务

| 服务名 | 端口 | 描述 |
|--------|------|------|
| app1 | 8080, 9090 | 应用实例1 (主服务端口, 管理端口) |
| app2 | 8081, 9091 | 应用实例2 (主服务端口, 管理端口) |
| nginx | 80, 443 | 负载均衡器和反向代理 |
| redis | 6379 | 集群状态管理和缓存 |
| mosquitto | 1883, 9001 | MQTT 消息代理 |

### 监控服务

| 服务名 | 端口 | 描述 |
|--------|------|------|
| prometheus | 9092 | 指标收集和存储 |
| grafana | 3000 | 监控仪表板 |
| fluentd | 24224 | 日志聚合 |

## 配置管理

### 环境变量配置

创建 `.env` 文件进行环境特定配置：

```bash
# 应用配置
SPRING_PROFILES_ACTIVE=docker,prod
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_EXAMPLE=DEBUG

# 数据库配置
REDIS_HOST=redis
REDIS_PORT=6379

# MQTT 配置
MQTT_BROKER_URL=tcp://mosquitto:1883
MQTT_USERNAME=
MQTT_PASSWORD=

# 安全配置
JWT_SECRET=your-production-secret-key-change-this
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# 集群配置
CLUSTER_ENABLED=true
```

### 外部化配置

配置文件位于 `config/` 目录：

- `application-docker.yml` - Docker 环境配置
- `redis.conf` - Redis 配置
- `mosquitto.conf` - MQTT 代理配置
- `nginx.conf` - 负载均衡器配置
- `prometheus.yml` - 监控配置
- `fluentd.conf` - 日志聚合配置

## 部署环境

### 开发环境

```bash
# 启动开发环境 (包含调试端口)
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 开发环境特性
# - 热重载支持
# - 调试端口开放
# - 详细日志输出
# - 开发工具集成
```

### 测试环境

```bash
# 启动测试环境
docker-compose -f docker-compose.yml -f docker-compose.test.yml up -d

# 运行集成测试
docker-compose exec app1 mvn test -Dspring.profiles.active=test
```

### 生产环境

```bash
# 生产环境部署
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 生产环境特性
# - 资源限制
# - 安全加固
# - 性能优化
# - 监控告警
```

## 健康检查和监控

### 健康检查端点

| 端点 | 描述 |
|------|------|
| `/actuator/health` | 整体健康状态 |
| `/actuator/health/liveness` | 存活性检查 |
| `/actuator/health/readiness` | 就绪性检查 |
| `/actuator/metrics` | 应用指标 |
| `/actuator/prometheus` | Prometheus 格式指标 |

### 自动化健康检查

```bash
# 使用提供的健康检查脚本
./scripts/health-check.sh

# 检查特定组件
./scripts/health-check.sh app    # 应用健康
./scripts/health-check.sh ready  # 就绪性检查
./scripts/health-check.sh live   # 存活性检查
./scripts/health-check.sh deps   # 依赖检查
```

### 监控仪表板

访问 Grafana 仪表板：
- URL: http://localhost:3000
- 用户名: admin
- 密码: admin123

关键监控指标：
- 应用响应时间和吞吐量
- WebSocket 连接数
- MQTT 消息处理速率
- Redis 连接池状态
- JVM 内存和 GC 指标
- 系统资源使用率

## 扩容和负载均衡

### 水平扩容

```bash
# 扩展应用实例
docker-compose up -d --scale app1=2 --scale app2=2

# 动态添加实例
docker-compose run -d --name app3 \
  -e INSTANCE_ID=app3 \
  -e SERVER_PORT=8080 \
  -e MANAGEMENT_SERVER_PORT=9090 \
  app1
```

### 负载均衡配置

Nginx 配置支持多种负载均衡策略：
- `least_conn` - 最少连接数
- `ip_hash` - IP 哈希
- `round_robin` - 轮询 (默认)

### 故障转移

系统支持自动故障转移：
- 健康检查失败自动摘除实例
- Redis 集群状态同步
- 会话粘性支持
- 优雅关闭机制

## 安全配置

### SSL/TLS 配置

```bash
# 生成自签名证书 (仅用于测试)
mkdir -p config/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout config/ssl/key.pem \
  -out config/ssl/cert.pem \
  -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"

# 启用 HTTPS
# 取消注释 nginx.conf 中的 HTTPS 服务器块
```

### 安全最佳实践

1. **密钥管理**
   - 使用环境变量存储敏感信息
   - 定期轮换加密密钥
   - 使用 Docker Secrets (Swarm 模式)

2. **网络安全**
   - 限制容器间通信
   - 使用防火墙规则
   - 启用 TLS 加密

3. **访问控制**
   - 非 root 用户运行容器
   - 最小权限原则
   - 定期安全扫描

## 备份和恢复

### 数据备份

```bash
# Redis 数据备份
docker-compose exec redis redis-cli BGSAVE
docker cp mqtt-websocket-redis:/data/dump.rdb ./backup/

# 应用日志备份
tar -czf logs-backup-$(date +%Y%m%d).tar.gz logs/

# 配置文件备份
tar -czf config-backup-$(date +%Y%m%d).tar.gz config/
```

### 数据恢复

```bash
# 恢复 Redis 数据
docker-compose stop redis
docker cp ./backup/dump.rdb mqtt-websocket-redis:/data/
docker-compose start redis

# 恢复配置文件
tar -xzf config-backup-YYYYMMDD.tar.gz
```

## 故障排除

### 常见问题

1. **应用启动失败**
   ```bash
   # 检查日志
   docker-compose logs app1
   
   # 检查依赖服务
   docker-compose ps
   
   # 验证配置
   docker-compose config
   ```

2. **WebSocket 连接失败**
   ```bash
   # 检查 Nginx 配置
   docker-compose exec nginx nginx -t
   
   # 检查防火墙设置
   netstat -tlnp | grep :80
   ```

3. **MQTT 连接问题**
   ```bash
   # 测试 MQTT 连接
   docker-compose exec mosquitto mosquitto_pub -h localhost -t test -m "hello"
   
   # 检查 MQTT 日志
   docker-compose logs mosquitto
   ```

4. **Redis 连接问题**
   ```bash
   # 测试 Redis 连接
   docker-compose exec redis redis-cli ping
   
   # 检查连接数
   docker-compose exec redis redis-cli info clients
   ```

### 性能调优

1. **JVM 调优**
   ```bash
   # 在 docker-compose.yml 中调整 JAVA_OPTS
   JAVA_OPTS: "-Xmx2g -Xms1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
   ```

2. **Nginx 调优**
   ```nginx
   # 在 nginx.conf 中调整
   worker_processes auto;
   worker_connections 4096;
   keepalive_timeout 65;
   ```

3. **Redis 调优**
   ```bash
   # 在 redis.conf 中调整
   maxmemory 512mb
   maxmemory-policy allkeys-lru
   ```

## 运维指南

### 日常维护

1. **日志轮转**
   ```bash
   # 清理旧日志
   find logs/ -name "*.log" -mtime +30 -delete
   
   # 压缩日志
   gzip logs/app*/*.log
   ```

2. **监控检查**
   ```bash
   # 检查系统资源
   docker stats
   
   # 检查磁盘使用
   df -h
   
   # 检查网络连接
   netstat -an | grep ESTABLISHED | wc -l
   ```

3. **定期备份**
   ```bash
   # 创建定时任务
   crontab -e
   # 添加: 0 2 * * * /path/to/backup-script.sh
   ```

### 升级部署

```bash
# 1. 备份当前版本
docker-compose down
cp -r . ../backup-$(date +%Y%m%d)

# 2. 拉取新版本
git pull origin main

# 3. 重新构建镜像
docker-compose build --no-cache

# 4. 滚动更新
docker-compose up -d --force-recreate

# 5. 验证部署
./scripts/health-check.sh
```

### 回滚策略

```bash
# 快速回滚到上一个版本
docker-compose down
git checkout HEAD~1
docker-compose up -d

# 或使用备份
rm -rf current-deployment
mv ../backup-YYYYMMDD current-deployment
cd current-deployment
docker-compose up -d
```

## 联系信息

如有问题或需要支持，请联系：
- 开发团队: <EMAIL>
- 运维团队: <EMAIL>
- 紧急联系: <EMAIL>

## 更新日志

- v1.0.0 - 初始版本发布
- v1.1.0 - 添加监控和日志聚合
- v1.2.0 - 增强安全配置和故障转移