# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
DEPLOYMENT.md
*.md

# IDE files
.idea/
.vscode/
*.iml
*.ipr
*.iws

# OS files
.DS_Store
Thumbs.db

# Build artifacts
target/
!target/*.jar
build/
out/

# Logs
logs/
*.log

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Configuration (will be mounted as volumes)
config/

# Scripts
scripts/

# Test files
src/test/

# Maven wrapper (if not needed)
.mvn/
mvnw
mvnw.cmd

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Node modules (if any)
node_modules/
npm-debug.log*

# Environment files
.env
.env.local
.env.*.local

# Backup files
*.bak
*.backup