# Mosquitto MQTT Broker Configuration

# General settings
user mosquitto
max_inflight_messages 20
max_queued_messages 1000
message_size_limit 0
allow_zero_length_clientid true
persistent_client_expiration 2m

# Default listener
listener 1883
protocol mqtt
socket_domain ipv4

# WebSocket listener
listener 9001
protocol websockets
socket_domain ipv4

# Logging
log_dest file /mosquitto/log/mosquitto.log
log_dest stdout
log_type error
log_type warning
log_type notice
log_type information
log_type debug
log_timestamp true
log_timestamp_format %Y-%m-%dT%H:%M:%S

# Security
allow_anonymous true
# password_file /mosquitto/config/passwd
# acl_file /mosquitto/config/acl

# Persistence
persistence true
persistence_location /mosquitto/data/
persistence_file mosquitto.db
autosave_interval 1800
autosave_on_changes false

# Connection limits
max_connections -1
max_keepalive 65535

# Message handling
max_packet_size 0
upgrade_outgoing_qos false
max_inflight_bytes 0
max_queued_bytes 0

# Bridge configuration (if needed for clustering)
# connection bridge-01
# address redis:1883
# topic sensor/# out 0
# topic device/# in 0

# Websockets specific
websockets_log_level 0
websockets_headers_size 1024

# Performance tuning
sys_interval 10
store_clean_interval 10
queue_qos0_messages false
retain_available true
set_tcp_nodelay true

# Client session settings
persistent_client_expiration 1h