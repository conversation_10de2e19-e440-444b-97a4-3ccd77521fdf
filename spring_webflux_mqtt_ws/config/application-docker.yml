# Docker environment specific configuration
spring:
  profiles:
    active: docker
  
  # Redis configuration for cluster state
  data:
    redis:
      host: ${REDIS_HOST:redis}
      port: ${REDIS_PORT:6379}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms

  # WebSocket configuration
  websocket:
    allowed-origins: "*"
    endpoint: "/websocket"
    max-session-idle-timeout: 300000
    max-text-message-buffer-size: 32768
    max-binary-message-buffer-size: 32768

# Server configuration
server:
  port: ${SERVER_PORT:8080}
  shutdown: graceful
  netty:
    connection-timeout: 20s
    h2c-max-content-length: 0B

# Management endpoints
management:
  server:
    port: ${MANAGEMENT_SERVER_PORT:9090}
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      slo:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms

# Application specific configuration
app:
  instance:
    id: ${INSTANCE_ID:default}
  
  # MQTT configuration
  mqtt:
    broker-url: ${MQTT_BROKER_URL:tcp://localhost:1883}
    client-id: ${app.instance.id}-mqtt-client
    username: ${MQTT_USERNAME:}
    password: ${MQTT_PASSWORD:}
    connection-timeout: 30
    keep-alive-interval: 60
    subscribe-topics:
      - "sensor/+/data"
      - "device/+/status"
      - "system/broadcast"
    publish-topics:
      - "response/+/data"
      - "system/notifications"

  # Cluster configuration
  cluster:
    enabled: ${CLUSTER_ENABLED:true}
    instance-id: ${app.instance.id}
    heartbeat-interval: 30s
    health-check-interval: 10s
    failover-timeout: 60s

  # Security configuration
  security:
    encryption:
      algorithm: AES
      key-size: 256
      key-rotation-interval: 24h
    jwt:
      secret: ${JWT_SECRET:your-secret-key-change-in-production}
      expiration: 3600

  # Performance configuration
  performance:
    websocket:
      max-connections: 10000
      connection-pool-size: 100
    mqtt:
      max-in-flight: 1000
      message-buffer-size: 10000
    redis:
      connection-pool-size: 20

# Logging configuration
logging:
  level:
    root: ${LOGGING_LEVEL_ROOT:INFO}
    com.example.mqtt.websocket: ${LOGGING_LEVEL_COM_EXAMPLE:DEBUG}
    org.springframework.integration: INFO
    org.springframework.web.socket: INFO
    io.lettuce.core: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    name: /app/logs/application.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 1GB

# Graceful shutdown configuration
spring.lifecycle.timeout-per-shutdown-phase: ${SPRING_LIFECYCLE_TIMEOUT_PER_SHUTDOWN_PHASE:30s}