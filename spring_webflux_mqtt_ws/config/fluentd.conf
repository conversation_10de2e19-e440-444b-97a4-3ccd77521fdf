# Fluentd configuration for MQTT-WebSocket application logs

<source>
  @type forward
  port 24224
  bind 0.0.0.0
</source>

# Application logs from containers
<source>
  @type tail
  path /var/log/containers/app1/application.log
  pos_file /var/log/fluentd-app1.log.pos
  tag app.app1
  format json
  time_key timestamp
  time_format %Y-%m-%d %H:%M:%S.%L
</source>

<source>
  @type tail
  path /var/log/containers/app2/application.log
  pos_file /var/log/fluentd-app2.log.pos
  tag app.app2
  format json
  time_key timestamp
  time_format %Y-%m-%d %H:%M:%S.%L
</source>

# Parse application logs
<filter app.**>
  @type parser
  key_name message
  reserve_data true
  <parse>
    @type regexp
    expression /^(?<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) \[(?<thread>[^\]]+)\] (?<level>\w+)\s+\[(?<trace_id>[^,]*),(?<span_id>[^\]]*)\] (?<logger>[^\s]+) - (?<message>.*)$/
    time_key timestamp
    time_format %Y-%m-%d %H:%M:%S.%L
  </parse>
</filter>

# Add common fields
<filter app.**>
  @type record_transformer
  <record>
    service mqtt-websocket
    environment docker
    instance_id ${tag_parts[1]}
  </record>
</filter>

# Error log filtering
<filter app.**>
  @type grep
  <regexp>
    key level
    pattern ^(ERROR|WARN)$
  </regexp>
</filter>

# Security event filtering
<filter app.**>
  @type grep
  <regexp>
    key message
    pattern (authentication|authorization|security|encryption|decrypt)
  </regexp>
</filter>

# Performance metrics filtering
<filter app.**>
  @type grep
  <regexp>
    key message
    pattern (performance|latency|throughput|memory|cpu)
  </regexp>
</filter>

# Output to stdout for debugging
<match app.**>
  @type stdout
  <format>
    @type json
  </format>
</match>

# Output to file for persistence
<match app.**>
  @type file
  path /var/log/containers/aggregated/app
  append true
  time_slice_format %Y%m%d%H
  time_slice_wait 10m
  time_format %Y-%m-%dT%H:%M:%S%z
  <format>
    @type json
  </format>
  <buffer>
    @type file
    path /var/log/containers/buffer/app
    flush_mode interval
    flush_interval 30s
    chunk_limit_size 64m
    queue_limit_length 128
    retry_max_interval 30
    retry_forever true
  </buffer>
</match>

# System metrics collection
<source>
  @type monitor_agent
  bind 0.0.0.0
  port 24220
</source>

# Health check endpoint
<source>
  @type http
  port 9880
  bind 0.0.0.0
</source>