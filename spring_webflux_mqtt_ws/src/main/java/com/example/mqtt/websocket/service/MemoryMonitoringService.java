package com.example.mqtt.websocket.service;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 内存使用监控服务
 * 监控 JVM 内存使用情况和 GC 性能
 */
@Service
public class MemoryMonitoringService implements HealthIndicator {

    private static final Logger logger = LoggerFactory.getLogger(MemoryMonitoringService.class);

    private final MemoryMXBean memoryMXBean;
    private final List<GarbageCollectorMXBean> gcMXBeans;
    private final MeterRegistry meterRegistry;
    
    // 内存使用阈值 (百分比)
    private static final double MEMORY_WARNING_THRESHOLD = 0.8;
    private static final double MEMORY_CRITICAL_THRESHOLD = 0.9;
    
    // GC 性能指标
    private final AtomicLong totalGcTime = new AtomicLong(0);
    private final AtomicLong totalGcCount = new AtomicLong(0);
    private long lastGcTime = 0;
    private long lastGcCount = 0;

    @Autowired
    public MemoryMonitoringService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.memoryMXBean = ManagementFactory.getMemoryMXBean();
        this.gcMXBeans = ManagementFactory.getGarbageCollectorMXBeans();
    }

    @PostConstruct
    public void initializeMetrics() {
        // 注册内存使用指标
        Gauge.builder("jvm.memory.heap.used.ratio", this, MemoryMonitoringService::getHeapUsageRatio)
            .description("Heap memory usage ratio")
            .register(meterRegistry);

        Gauge.builder("jvm.memory.non_heap.used.ratio", this, MemoryMonitoringService::getNonHeapUsageRatio)
            .description("Non-heap memory usage ratio")
            .register(meterRegistry);

        Gauge.builder("jvm.memory.heap.available", this, MemoryMonitoringService::getAvailableHeapMemory)
            .description("Available heap memory in bytes")
            .register(meterRegistry);

        // 注册 GC 指标
        Gauge.builder("jvm.gc.total.time", this, service -> service.totalGcTime.get())
            .description("Total GC time in milliseconds")
            .register(meterRegistry);

        Gauge.builder("jvm.gc.total.count", this, service -> service.totalGcCount.get())
            .description("Total GC count")
            .register(meterRegistry);

        Gauge.builder("jvm.gc.average.time", this, MemoryMonitoringService::getAverageGcTime)
            .description("Average GC time per collection")
            .register(meterRegistry);
    }

    /**
     * 定期监控内存使用情况
     */
    @Scheduled(fixedRate = 30000) // 每30秒执行一次
    public void monitorMemoryUsage() {
        MemoryUsage heapUsage = memoryMXBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryMXBean.getNonHeapMemoryUsage();
        
        double heapUsageRatio = getHeapUsageRatio();
        double nonHeapUsageRatio = getNonHeapUsageRatio();
        
        // 记录内存使用情况
        logger.debug("Memory usage - Heap: {:.2f}% ({} MB / {} MB), Non-Heap: {:.2f}% ({} MB / {} MB)",
            heapUsageRatio * 100,
            heapUsage.getUsed() / 1024 / 1024,
            heapUsage.getMax() / 1024 / 1024,
            nonHeapUsageRatio * 100,
            nonHeapUsage.getUsed() / 1024 / 1024,
            nonHeapUsage.getMax() / 1024 / 1024);
        
        // 检查内存使用阈值
        if (heapUsageRatio > MEMORY_CRITICAL_THRESHOLD) {
            logger.error("CRITICAL: Heap memory usage is critically high: {:.2f}%", heapUsageRatio * 100);
        } else if (heapUsageRatio > MEMORY_WARNING_THRESHOLD) {
            logger.warn("WARNING: Heap memory usage is high: {:.2f}%", heapUsageRatio * 100);
        }
        
        // 更新 GC 统计
        updateGcStatistics();
    }

    /**
     * 更新 GC 统计信息
     */
    private void updateGcStatistics() {
        long currentGcTime = 0;
        long currentGcCount = 0;
        
        for (GarbageCollectorMXBean gcBean : gcMXBeans) {
            currentGcTime += gcBean.getCollectionTime();
            currentGcCount += gcBean.getCollectionCount();
        }
        
        totalGcTime.set(currentGcTime);
        totalGcCount.set(currentGcCount);
        
        // 计算增量
        long gcTimeDelta = currentGcTime - lastGcTime;
        long gcCountDelta = currentGcCount - lastGcCount;
        
        if (gcCountDelta > 0) {
            logger.debug("GC activity - Collections: {}, Time: {} ms, Average: {:.2f} ms",
                gcCountDelta, gcTimeDelta, (double) gcTimeDelta / gcCountDelta);
        }
        
        lastGcTime = currentGcTime;
        lastGcCount = currentGcCount;
    }

    /**
     * 获取堆内存使用比例
     */
    public double getHeapUsageRatio() {
        MemoryUsage heapUsage = memoryMXBean.getHeapMemoryUsage();
        return heapUsage.getMax() > 0 ? (double) heapUsage.getUsed() / heapUsage.getMax() : 0.0;
    }

    /**
     * 获取非堆内存使用比例
     */
    public double getNonHeapUsageRatio() {
        MemoryUsage nonHeapUsage = memoryMXBean.getNonHeapMemoryUsage();
        return nonHeapUsage.getMax() > 0 ? (double) nonHeapUsage.getUsed() / nonHeapUsage.getMax() : 0.0;
    }

    /**
     * 获取可用堆内存
     */
    public long getAvailableHeapMemory() {
        MemoryUsage heapUsage = memoryMXBean.getHeapMemoryUsage();
        return heapUsage.getMax() - heapUsage.getUsed();
    }

    /**
     * 获取平均 GC 时间
     */
    public double getAverageGcTime() {
        long count = totalGcCount.get();
        return count > 0 ? (double) totalGcTime.get() / count : 0.0;
    }

    /**
     * 获取内存统计信息
     */
    public MemoryStats getMemoryStats() {
        MemoryUsage heapUsage = memoryMXBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryMXBean.getNonHeapMemoryUsage();
        
        return new MemoryStats(
            heapUsage.getUsed(),
            heapUsage.getMax(),
            nonHeapUsage.getUsed(),
            nonHeapUsage.getMax(),
            totalGcTime.get(),
            totalGcCount.get(),
            getAverageGcTime()
        );
    }

    /**
     * 触发垃圾回收
     */
    public void triggerGc() {
        logger.info("Triggering garbage collection");
        System.gc();
    }

    /**
     * 健康检查实现
     */
    @Override
    public Health health() {
        double heapUsageRatio = getHeapUsageRatio();
        MemoryStats stats = getMemoryStats();
        
        Health.Builder builder = new Health.Builder();
        
        if (heapUsageRatio > MEMORY_CRITICAL_THRESHOLD) {
            builder.down()
                .withDetail("status", "CRITICAL")
                .withDetail("reason", "Heap memory usage is critically high");
        } else if (heapUsageRatio > MEMORY_WARNING_THRESHOLD) {
            builder.up()
                .withDetail("status", "WARNING")
                .withDetail("reason", "Heap memory usage is high");
        } else {
            builder.up()
                .withDetail("status", "HEALTHY");
        }
        
        return builder
            .withDetail("heapUsageRatio", String.format("%.2f%%", heapUsageRatio * 100))
            .withDetail("heapUsed", stats.getHeapUsed() / 1024 / 1024 + " MB")
            .withDetail("heapMax", stats.getHeapMax() / 1024 / 1024 + " MB")
            .withDetail("nonHeapUsed", stats.getNonHeapUsed() / 1024 / 1024 + " MB")
            .withDetail("totalGcTime", stats.getTotalGcTime() + " ms")
            .withDetail("totalGcCount", stats.getTotalGcCount())
            .withDetail("averageGcTime", String.format("%.2f ms", stats.getAverageGcTime()))
            .build();
    }

    /**
     * 内存统计信息
     */
    public static class MemoryStats {
        private final long heapUsed;
        private final long heapMax;
        private final long nonHeapUsed;
        private final long nonHeapMax;
        private final long totalGcTime;
        private final long totalGcCount;
        private final double averageGcTime;

        public MemoryStats(long heapUsed, long heapMax, long nonHeapUsed, long nonHeapMax,
                          long totalGcTime, long totalGcCount, double averageGcTime) {
            this.heapUsed = heapUsed;
            this.heapMax = heapMax;
            this.nonHeapUsed = nonHeapUsed;
            this.nonHeapMax = nonHeapMax;
            this.totalGcTime = totalGcTime;
            this.totalGcCount = totalGcCount;
            this.averageGcTime = averageGcTime;
        }

        public long getHeapUsed() { return heapUsed; }
        public long getHeapMax() { return heapMax; }
        public long getNonHeapUsed() { return nonHeapUsed; }
        public long getNonHeapMax() { return nonHeapMax; }
        public long getTotalGcTime() { return totalGcTime; }
        public long getTotalGcCount() { return totalGcCount; }
        public double getAverageGcTime() { return averageGcTime; }
        
        public double getHeapUsageRatio() {
            return heapMax > 0 ? (double) heapUsed / heapMax : 0.0;
        }
        
        public double getNonHeapUsageRatio() {
            return nonHeapMax > 0 ? (double) nonHeapUsed / nonHeapMax : 0.0;
        }
    }
}