package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.annotation.Filter;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 消息过滤器
 * 提供基于主题、内容、时间等条件的消息过滤功能
 */
@Component
public class MessageFilter {

    private static final Logger logger = LoggerFactory.getLogger(MessageFilter.class);
    
    // 消息去重缓存 - 存储最近处理的消息ID
    private final Map<String, Long> messageDeduplicationCache = new ConcurrentHashMap<>();
    
    // 缓存清理间隔（毫秒）
    private static final long CACHE_CLEANUP_INTERVAL = 300000; // 5分钟
    
    // 消息过期时间（毫秒）
    private static final long MESSAGE_EXPIRY_TIME = 600000; // 10分钟
    
    // 最大消息大小（字节）
    private static final int MAX_MESSAGE_SIZE = 10 * 1024 * 1024; // 10MB
    
    // 黑名单主题模式
    private final Set<Pattern> blacklistedTopicPatterns = ConcurrentHashMap.newKeySet();
    
    // 白名单主题模式
    private final Set<Pattern> whitelistedTopicPatterns = ConcurrentHashMap.newKeySet();
    
    // 上次缓存清理时间
    private volatile long lastCacheCleanup = System.currentTimeMillis();

    public MessageFilter() {
        initializeFilterRules();
    }

    /**
     * 基于主题的消息过滤
     * 
     * @param message 消息内容
     * @param topic MQTT 主题
     * @return 是否通过过滤
     */
    @Filter(inputChannel = "mqttInputChannel", outputChannel = "messageFilterChannel")
    public boolean filterByTopic(@Payload byte[] message, 
                               @Header(MqttHeaders.RECEIVED_TOPIC) String topic) {
        
        try {
            // 清理过期缓存
            cleanupCacheIfNeeded();
            
            // 基本验证
            if (!isValidMessage(message, topic)) {
                return false;
            }
            
            // 主题黑名单检查
            if (isTopicBlacklisted(topic)) {
                logger.debug("Message filtered out - blacklisted topic: {}", topic);
                return false;
            }
            
            // 主题白名单检查（如果配置了白名单）
            if (!whitelistedTopicPatterns.isEmpty() && !isTopicWhitelisted(topic)) {
                logger.debug("Message filtered out - not in whitelist topic: {}", topic);
                return false;
            }
            
            // 消息大小检查
            if (message.length > MAX_MESSAGE_SIZE) {
                logger.warn("Message filtered out - too large: {} bytes from topic: {}", 
                           message.length, topic);
                return false;
            }
            
            logger.debug("Message passed topic filter from topic: {}", topic);
            return true;
            
        } catch (Exception e) {
            logger.error("Error in topic filter for topic: {}", topic, e);
            return false;
        }
    }

    /**
     * 基于消息内容的过滤
     * 
     * @param baseMessage BaseMessage 对象
     * @return 是否通过过滤
     */
    @Filter(inputChannel = "messageFilterChannel", outputChannel = "messageTransformChannel")
    public boolean filterByContent(@Payload BaseMessage baseMessage) {
        
        try {
            // 消息去重检查
            if (isDuplicateMessage(baseMessage)) {
                logger.debug("Message filtered out - duplicate message ID: {}", baseMessage.getId());
                return false;
            }
            
            // 消息时效性检查
            if (isMessageExpired(baseMessage)) {
                logger.debug("Message filtered out - expired message ID: {}", baseMessage.getId());
                return false;
            }
            
            // 消息类型过滤
            if (isMessageTypeFiltered(baseMessage.getType())) {
                logger.debug("Message filtered out - filtered message type: {}", baseMessage.getType());
                return false;
            }
            
            // 消息来源过滤
            if (isMessageSourceFiltered(baseMessage.getSource())) {
                logger.debug("Message filtered out - filtered message source: {}", baseMessage.getSource());
                return false;
            }
            
            // 内容关键词过滤
            if (containsFilteredKeywords(baseMessage)) {
                logger.debug("Message filtered out - contains filtered keywords, ID: {}", baseMessage.getId());
                return false;
            }
            
            // 记录消息ID用于去重
            recordMessageForDeduplication(baseMessage);
            
            logger.debug("Message passed content filter, ID: {}", baseMessage.getId());
            return true;
            
        } catch (Exception e) {
            logger.error("Error in content filter for message ID: {}", baseMessage.getId(), e);
            return false;
        }
    }

    /**
     * 条件路由过滤器 - 根据消息属性决定路由目标
     * 
     * @param message 完整消息对象
     * @return 路由目标通道名称，null 表示过滤掉
     */
    public String conditionalRouting(Message<BaseMessage> message) {
        try {
            BaseMessage baseMessage = message.getPayload();
            String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
            
            // 高优先级消息直接路由到 WebSocket
            if (isHighPriorityMessage(baseMessage, topic)) {
                return "webSocketChannel";
            }
            
            // 系统消息路由到系统通道
            if (isSystemMessage(baseMessage, topic)) {
                return "systemChannel";
            }
            
            // API 相关消息路由到 HTTP API 通道
            if (isApiMessage(baseMessage, topic)) {
                return "httpApiChannel";
            }
            
            // 批处理消息路由到默认处理通道
            if (isBatchMessage(baseMessage, topic)) {
                return "defaultProcessingChannel";
            }
            
            // 默认路由
            return "mqttRoutingChannel";
            
        } catch (Exception e) {
            logger.error("Error in conditional routing: {}", e.getMessage());
            return null; // 过滤掉有问题的消息
        }
    }

    /**
     * 动态添加主题黑名单模式
     * 
     * @param pattern 主题模式
     */
    public void addBlacklistedTopicPattern(String pattern) {
        blacklistedTopicPatterns.add(Pattern.compile(pattern));
        logger.info("Added blacklisted topic pattern: {}", pattern);
    }

    /**
     * 动态添加主题白名单模式
     * 
     * @param pattern 主题模式
     */
    public void addWhitelistedTopicPattern(String pattern) {
        whitelistedTopicPatterns.add(Pattern.compile(pattern));
        logger.info("Added whitelisted topic pattern: {}", pattern);
    }

    /**
     * 移除主题黑名单模式
     * 
     * @param pattern 主题模式
     */
    public void removeBlacklistedTopicPattern(String pattern) {
        blacklistedTopicPatterns.removeIf(p -> p.pattern().equals(pattern));
        logger.info("Removed blacklisted topic pattern: {}", pattern);
    }

    /**
     * 移除主题白名单模式
     * 
     * @param pattern 主题模式
     */
    public void removeWhitelistedTopicPattern(String pattern) {
        whitelistedTopicPatterns.removeIf(p -> p.pattern().equals(pattern));
        logger.info("Removed whitelisted topic pattern: {}", pattern);
    }

    /**
     * 初始化过滤规则
     */
    private void initializeFilterRules() {
        // 默认黑名单主题
        addBlacklistedTopicPattern("^\\$SYS/.*"); // MQTT 系统主题
        addBlacklistedTopicPattern(".*test.*"); // 测试主题
        addBlacklistedTopicPattern(".*debug.*"); // 调试主题
        
        // 可以从配置文件加载更多规则
        logger.info("Initialized message filter rules");
    }

    /**
     * 基本消息验证
     */
    private boolean isValidMessage(byte[] message, String topic) {
        return message != null && 
               message.length > 0 && 
               StringUtils.hasText(topic);
    }

    /**
     * 检查主题是否在黑名单中
     */
    private boolean isTopicBlacklisted(String topic) {
        return blacklistedTopicPatterns.stream()
                .anyMatch(pattern -> pattern.matcher(topic).matches());
    }

    /**
     * 检查主题是否在白名单中
     */
    private boolean isTopicWhitelisted(String topic) {
        return whitelistedTopicPatterns.stream()
                .anyMatch(pattern -> pattern.matcher(topic).matches());
    }

    /**
     * 检查是否为重复消息
     */
    private boolean isDuplicateMessage(BaseMessage message) {
        String messageId = message.getId();
        if (!StringUtils.hasText(messageId)) {
            return false; // 没有ID的消息不进行去重检查
        }
        
        Long lastSeen = messageDeduplicationCache.get(messageId);
        return lastSeen != null && 
               (System.currentTimeMillis() - lastSeen) < MESSAGE_EXPIRY_TIME;
    }

    /**
     * 检查消息是否过期
     */
    private boolean isMessageExpired(BaseMessage message) {
        long messageTime = message.getTimestamp();
        if (messageTime <= 0) {
            return false; // 没有时间戳的消息不检查过期
        }
        
        long currentTime = Instant.now().toEpochMilli();
        return (currentTime - messageTime) > MESSAGE_EXPIRY_TIME;
    }

    /**
     * 检查消息类型是否被过滤
     */
    private boolean isMessageTypeFiltered(String messageType) {
        // 可以配置需要过滤的消息类型
        return "spam".equals(messageType) || 
               "advertisement".equals(messageType);
    }

    /**
     * 检查消息来源是否被过滤
     */
    private boolean isMessageSourceFiltered(String messageSource) {
        // 可以配置需要过滤的消息来源
        return "blocked-source".equals(messageSource);
    }

    /**
     * 检查消息是否包含过滤关键词
     */
    private boolean containsFilteredKeywords(BaseMessage message) {
        String payloadText = message.getPayload().toStringUtf8().toLowerCase();
        
        // 定义过滤关键词
        String[] filteredKeywords = {"spam", "advertisement", "malware", "virus"};
        
        for (String keyword : filteredKeywords) {
            if (payloadText.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 记录消息用于去重
     */
    private void recordMessageForDeduplication(BaseMessage message) {
        String messageId = message.getId();
        if (StringUtils.hasText(messageId)) {
            messageDeduplicationCache.put(messageId, System.currentTimeMillis());
        }
    }

    /**
     * 检查是否为高优先级消息
     */
    private boolean isHighPriorityMessage(BaseMessage message, String topic) {
        return topic != null && (
            topic.startsWith("urgent/") ||
            topic.startsWith("alert/") ||
            message.getHeadersOrDefault("priority", "").equals("high")
        );
    }

    /**
     * 检查是否为系统消息
     */
    private boolean isSystemMessage(BaseMessage message, String topic) {
        return topic != null && (
            topic.startsWith("system/") ||
            topic.startsWith("admin/") ||
            "system".equals(message.getType())
        );
    }

    /**
     * 检查是否为 API 消息
     */
    private boolean isApiMessage(BaseMessage message, String topic) {
        return topic != null && (
            topic.startsWith("api/") ||
            topic.startsWith("rest/") ||
            "api".equals(message.getType())
        );
    }

    /**
     * 检查是否为批处理消息
     */
    private boolean isBatchMessage(BaseMessage message, String topic) {
        return topic != null && (
            topic.startsWith("batch/") ||
            "batch".equals(message.getType()) ||
            message.getHeadersOrDefault("processing", "").equals("batch")
        );
    }

    /**
     * 清理过期缓存
     */
    private void cleanupCacheIfNeeded() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCacheCleanup > CACHE_CLEANUP_INTERVAL) {
            cleanupExpiredCacheEntries(currentTime);
            lastCacheCleanup = currentTime;
        }
    }

    /**
     * 清理过期的缓存条目
     */
    private void cleanupExpiredCacheEntries(long currentTime) {
        messageDeduplicationCache.entrySet().removeIf(entry -> 
            (currentTime - entry.getValue()) > MESSAGE_EXPIRY_TIME);
        
        logger.debug("Cleaned up expired cache entries, remaining: {}", 
                    messageDeduplicationCache.size());
    }
}