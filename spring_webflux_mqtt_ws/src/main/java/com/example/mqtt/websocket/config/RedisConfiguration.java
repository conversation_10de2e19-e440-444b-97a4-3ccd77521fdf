package com.example.mqtt.websocket.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.SocketOptions;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ReactiveRedisMessageListenerContainer;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.List;

/**
 * Redis 连接池和性能优化配置
 */
@Configuration
@ConfigurationProperties(prefix = "app.redis")
public class RedisConfiguration {

    private String host = "localhost";
    private int port = 6379;
    private String password;
    private int database = 0;
    private List<String> clusterNodes;
    private boolean clusterEnabled = false;
    
    // 连接池配置
    private int maxActive = 20;
    private int maxIdle = 10;
    private int minIdle = 5;
    private Duration maxWait = Duration.ofSeconds(10);
    private Duration timeBetweenEvictionRuns = Duration.ofSeconds(30);
    private Duration minEvictableIdleTime = Duration.ofMinutes(5);
    
    // 连接超时配置
    private Duration connectTimeout = Duration.ofSeconds(10);
    private Duration commandTimeout = Duration.ofSeconds(5);
    private Duration shutdownTimeout = Duration.ofSeconds(5);
    
    // 集群配置
    private Duration topologyRefreshPeriod = Duration.ofMinutes(5);
    private boolean enablePeriodicRefresh = true;
    private boolean enableAdaptiveRefresh = true;

    /**
     * 配置 Redis 客户端资源
     */
    @Bean(destroyMethod = "shutdown")
    public ClientResources clientResources() {
        return DefaultClientResources.builder()
            .ioThreadPoolSize(Runtime.getRuntime().availableProcessors())
            .computationThreadPoolSize(Runtime.getRuntime().availableProcessors())
            .build();
    }

    /**
     * 配置连接池
     */
    @Bean
    public GenericObjectPoolConfig<Object> redisPoolConfig() {
        GenericObjectPoolConfig<Object> config = new GenericObjectPoolConfig<>();
        config.setMaxTotal(maxActive);
        config.setMaxIdle(maxIdle);
        config.setMinIdle(minIdle);
        config.setMaxWait(maxWait);
        config.setTimeBetweenEvictionRuns(timeBetweenEvictionRuns);
        config.setMinEvictableIdleTime(minEvictableIdleTime);
        config.setTestOnBorrow(true);
        config.setTestOnReturn(true);
        config.setTestWhileIdle(true);
        config.setBlockWhenExhausted(true);
        return config;
    }

    /**
     * 配置 Redis 连接工厂
     */
    @Bean
    public RedisConnectionFactory redisConnectionFactory(ClientResources clientResources,
                                                        GenericObjectPoolConfig<Object> poolConfig) {
        
        LettucePoolingClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
            .poolConfig(poolConfig)
            .clientResources(clientResources)
            .commandTimeout(commandTimeout)
            .shutdownTimeout(shutdownTimeout)
            .clientOptions(createClientOptions())
            .build();

        if (clusterEnabled && clusterNodes != null && !clusterNodes.isEmpty()) {
            // 集群模式配置
            RedisClusterConfiguration clusterConfiguration = new RedisClusterConfiguration(clusterNodes);
            if (password != null) {
                clusterConfiguration.setPassword(password);
            }
            return new LettuceConnectionFactory(clusterConfiguration, clientConfig);
        } else {
            // 单机模式配置
            RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration();
            standaloneConfig.setHostName(host);
            standaloneConfig.setPort(port);
            standaloneConfig.setDatabase(database);
            if (password != null) {
                standaloneConfig.setPassword(password);
            }
            return new LettuceConnectionFactory(standaloneConfig, clientConfig);
        }
    }

    /**
     * 创建客户端选项
     */
    private ClientOptions createClientOptions() {
        SocketOptions socketOptions = SocketOptions.builder()
            .connectTimeout(connectTimeout)
            .keepAlive(true)
            .tcpNoDelay(true)
            .build();

        TimeoutOptions timeoutOptions = TimeoutOptions.builder()
            .fixedTimeout(commandTimeout)
            .build();

        if (clusterEnabled) {
            ClusterTopologyRefreshOptions topologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
                .enablePeriodicRefresh(enablePeriodicRefresh)
                .refreshPeriod(topologyRefreshPeriod)
                .build();

            return ClusterClientOptions.builder()
                .socketOptions(socketOptions)
                .timeoutOptions(timeoutOptions)
                .topologyRefreshOptions(topologyRefreshOptions)
                .autoReconnect(true)
                .pingBeforeActivateConnection(true)
                .build();
        } else {
            return ClientOptions.builder()
                .socketOptions(socketOptions)
                .timeoutOptions(timeoutOptions)
                .autoReconnect(true)
                .pingBeforeActivateConnection(true)
                .build();
        }
    }

    /**
     * 配置 RedisTemplate
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 设置序列化器
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
        
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        
        template.setDefaultSerializer(jsonSerializer);
        template.afterPropertiesSet();
        
        return template;
    }

    /**
     * 配置响应式 RedisTemplate
     */
    @Bean
    public ReactiveRedisTemplate<String, Object> reactiveRedisTemplate(RedisConnectionFactory connectionFactory) {
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
        
        // 创建响应式连接工厂
        org.springframework.data.redis.connection.ReactiveRedisConnectionFactory reactiveConnectionFactory = 
            (org.springframework.data.redis.connection.ReactiveRedisConnectionFactory) connectionFactory;
        
        return new ReactiveRedisTemplate<String, Object>(reactiveConnectionFactory, 
            org.springframework.data.redis.serializer.RedisSerializationContext
                .<String, Object>newSerializationContext(jsonSerializer)
                .key(stringSerializer)
                .hashKey(stringSerializer)
                .value(jsonSerializer)
                .hashValue(jsonSerializer)
                .build());
    }

    /**
     * 响应式 Redis 消息监听容器
     */
    @Bean
    public ReactiveRedisMessageListenerContainer reactiveRedisMessageListenerContainer(RedisConnectionFactory connectionFactory) {
        org.springframework.data.redis.connection.ReactiveRedisConnectionFactory reactiveConnectionFactory =
                (org.springframework.data.redis.connection.ReactiveRedisConnectionFactory) connectionFactory;
        return new ReactiveRedisMessageListenerContainer(reactiveConnectionFactory);
    }

    // Getters and Setters
    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }

    public int getPort() { return port; }
    public void setPort(int port) { this.port = port; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public int getDatabase() { return database; }
    public void setDatabase(int database) { this.database = database; }

    public List<String> getClusterNodes() { return clusterNodes; }
    public void setClusterNodes(List<String> clusterNodes) { this.clusterNodes = clusterNodes; }

    public boolean isClusterEnabled() { return clusterEnabled; }
    public void setClusterEnabled(boolean clusterEnabled) { this.clusterEnabled = clusterEnabled; }

    public int getMaxActive() { return maxActive; }
    public void setMaxActive(int maxActive) { this.maxActive = maxActive; }

    public int getMaxIdle() { return maxIdle; }
    public void setMaxIdle(int maxIdle) { this.maxIdle = maxIdle; }

    public int getMinIdle() { return minIdle; }
    public void setMinIdle(int minIdle) { this.minIdle = minIdle; }

    public Duration getMaxWait() { return maxWait; }
    public void setMaxWait(Duration maxWait) { this.maxWait = maxWait; }

    public Duration getTimeBetweenEvictionRuns() { return timeBetweenEvictionRuns; }
    public void setTimeBetweenEvictionRuns(Duration timeBetweenEvictionRuns) { 
        this.timeBetweenEvictionRuns = timeBetweenEvictionRuns; 
    }

    public Duration getMinEvictableIdleTime() { return minEvictableIdleTime; }
    public void setMinEvictableIdleTime(Duration minEvictableIdleTime) { 
        this.minEvictableIdleTime = minEvictableIdleTime; 
    }

    public Duration getConnectTimeout() { return connectTimeout; }
    public void setConnectTimeout(Duration connectTimeout) { this.connectTimeout = connectTimeout; }

    public Duration getCommandTimeout() { return commandTimeout; }
    public void setCommandTimeout(Duration commandTimeout) { this.commandTimeout = commandTimeout; }

    public Duration getShutdownTimeout() { return shutdownTimeout; }
    public void setShutdownTimeout(Duration shutdownTimeout) { this.shutdownTimeout = shutdownTimeout; }

    public Duration getTopologyRefreshPeriod() { return topologyRefreshPeriod; }
    public void setTopologyRefreshPeriod(Duration topologyRefreshPeriod) { 
        this.topologyRefreshPeriod = topologyRefreshPeriod; 
    }

    public boolean isEnablePeriodicRefresh() { return enablePeriodicRefresh; }
    public void setEnablePeriodicRefresh(boolean enablePeriodicRefresh) { 
        this.enablePeriodicRefresh = enablePeriodicRefresh; 
    }

    public boolean isEnableAdaptiveRefresh() { return enableAdaptiveRefresh; }
    public void setEnableAdaptiveRefresh(boolean enableAdaptiveRefresh) { 
        this.enableAdaptiveRefresh = enableAdaptiveRefresh; 
    }
}
