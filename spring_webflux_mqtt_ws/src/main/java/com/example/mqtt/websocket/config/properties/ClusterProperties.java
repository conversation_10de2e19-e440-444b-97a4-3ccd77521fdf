package com.example.mqtt.websocket.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 集群配置属性
 */
@Component
@ConfigurationProperties(prefix = "app.cluster")
public class ClusterProperties {
    
    private String instanceId;
    private long healthCheckInterval = 30000L; // 30 seconds
    private long failureDetectionTimeout = 60000L; // 60 seconds
    private String loadBalancerStrategy = "round-robin";

    // Getters and Setters
    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public long getHealthCheckInterval() {
        return healthCheckInterval;
    }

    public void setHealthCheckInterval(long healthCheckInterval) {
        this.healthCheckInterval = healthCheckInterval;
    }

    public long getFailureDetectionTimeout() {
        return failureDetectionTimeout;
    }

    public void setFailureDetectionTimeout(long failureDetectionTimeout) {
        this.failureDetectionTimeout = failureDetectionTimeout;
    }

    public String getLoadBalancerStrategy() {
        return loadBalancerStrategy;
    }

    public void setLoadBalancerStrategy(String loadBalancerStrategy) {
        this.loadBalancerStrategy = loadBalancerStrategy;
    }
}