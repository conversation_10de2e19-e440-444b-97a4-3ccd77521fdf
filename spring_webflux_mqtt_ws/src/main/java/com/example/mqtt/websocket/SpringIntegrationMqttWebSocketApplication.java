package com.example.mqtt.websocket;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring Boot 应用程序主类
 * 整合 MQTT、WebSocket、Protobuf 和加密功能的综合消息处理平台
 */
@SpringBootApplication(exclude = { WebSocketServletAutoConfiguration.class })
@IntegrationComponentScan
@EnableScheduling
public class SpringIntegrationMqttWebSocketApplication {

    public static void main(String[] args) {
        SpringApplication.run(SpringIntegrationMqttWebSocketApplication.class, args);
    }
}
