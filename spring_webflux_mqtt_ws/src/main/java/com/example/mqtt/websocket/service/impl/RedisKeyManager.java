package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.properties.EncryptionProperties;
import com.example.mqtt.websocket.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 基于 Redis 的密钥管理器实现
 */
@Service
public class RedisKeyManager implements KeyManager {
    
    private static final Logger logger = LoggerFactory.getLogger(RedisKeyManager.class);
    private static final String KEY_PREFIX = "encryption:keys:";
    private static final String KEY_METADATA_PREFIX = "encryption:metadata:";
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    
    private final ReactiveRedisTemplate<String, String> redisTemplate;
    private final EncryptionProperties encryptionProperties;
    private final ConcurrentHashMap<String, SecretKey> keyCache;
    private final SecureRandom secureRandom;
    
    public RedisKeyManager(ReactiveRedisTemplate<String, String> redisTemplate, 
                          EncryptionProperties encryptionProperties) {
        this.redisTemplate = redisTemplate;
        this.encryptionProperties = encryptionProperties;
        this.keyCache = new ConcurrentHashMap<>();
        this.secureRandom = new SecureRandom();
    }
    
    @Override
    public SecretKey getKey(String keyId) throws KeyNotFoundException {
        // 首先检查缓存
        SecretKey cachedKey = keyCache.get(keyId);
        if (cachedKey != null) {
            return cachedKey;
        }
        
        // 从 Redis 获取密钥
        String redisKey = KEY_PREFIX + keyId;
        String encodedKey = redisTemplate.opsForValue()
            .get(redisKey)
            .block(Duration.ofSeconds(5));
            
        if (encodedKey == null) {
            throw new KeyNotFoundException(keyId);
        }
        
        try {
            byte[] keyBytes = Base64.getDecoder().decode(encodedKey);
            SecretKey secretKey = new SecretKeySpec(keyBytes, encryptionProperties.getAlgorithm());
            
            // 缓存密钥
            keyCache.put(keyId, secretKey);
            
            logger.debug("Retrieved key from Redis: {}", keyId);
            return secretKey;
        } catch (Exception e) {
            throw new KeyNotFoundException(keyId, e);
        }
    }
    
    @Override
    public SecretKey generateKey(String keyId) throws KeyGenerationException {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(encryptionProperties.getAlgorithm());
            keyGenerator.init(encryptionProperties.getKeyLength());
            SecretKey secretKey = keyGenerator.generateKey();
            
            // 存储到 Redis
            String encodedKey = Base64.getEncoder().encodeToString(secretKey.getEncoded());
            String redisKey = KEY_PREFIX + keyId;
            
            redisTemplate.opsForValue()
                .set(redisKey, encodedKey)
                .block(Duration.ofSeconds(5));
            
            // 存储元数据
            String metadataKey = KEY_METADATA_PREFIX + keyId;
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            redisTemplate.opsForValue()
                .set(metadataKey, timestamp)
                .block(Duration.ofSeconds(5));
            
            // 缓存密钥
            keyCache.put(keyId, secretKey);
            
            logger.info("Generated new key: {}", keyId);
            return secretKey;
        } catch (NoSuchAlgorithmException e) {
            throw new KeyGenerationException("Invalid algorithm: " + encryptionProperties.getAlgorithm(), e);
        } catch (Exception e) {
            throw new KeyGenerationException("Failed to generate key: " + keyId, e);
        }
    }
    
    @Override
    public SecretKey rotateKey(String keyId) throws KeyRotationException {
        try {
            logger.info("Rotating key: {}", keyId);
            
            // 删除旧密钥
            if (keyExists(keyId)) {
                deleteKey(keyId);
            }
            
            // 生成新密钥
            return generateKey(keyId);
        } catch (Exception e) {
            throw new KeyRotationException(keyId, e);
        }
    }
    
    @Override
    public void deleteKey(String keyId) throws KeyDeletionException {
        try {
            String redisKey = KEY_PREFIX + keyId;
            String metadataKey = KEY_METADATA_PREFIX + keyId;
            
            // 从 Redis 删除
            redisTemplate.delete(redisKey).block(Duration.ofSeconds(5));
            redisTemplate.delete(metadataKey).block(Duration.ofSeconds(5));
            
            // 从缓存删除
            keyCache.remove(keyId);
            
            logger.info("Deleted key: {}", keyId);
        } catch (Exception e) {
            throw new KeyDeletionException(keyId, e);
        }
    }
    
    @Override
    public Set<String> getAllKeyIds() {
        try {
            return redisTemplate.keys(KEY_PREFIX + "*")
                .map(key -> key.substring(KEY_PREFIX.length()))
                .collect(Collectors.toSet())
                .block(Duration.ofSeconds(10));
        } catch (Exception e) {
            logger.error("Failed to get all key IDs", e);
            return Set.of();
        }
    }
    
    @Override
    public boolean keyExists(String keyId) {
        try {
            String redisKey = KEY_PREFIX + keyId;
            Boolean exists = redisTemplate.hasKey(redisKey).block(Duration.ofSeconds(5));
            return Boolean.TRUE.equals(exists);
        } catch (Exception e) {
            logger.error("Failed to check key existence: {}", keyId, e);
            return false;
        }
    }
    
    @Override
    public LocalDateTime getKeyCreationTime(String keyId) throws KeyNotFoundException {
        try {
            String metadataKey = KEY_METADATA_PREFIX + keyId;
            String timestamp = redisTemplate.opsForValue()
                .get(metadataKey)
                .block(Duration.ofSeconds(5));
                
            if (timestamp == null) {
                throw new KeyNotFoundException(keyId);
            }
            
            return LocalDateTime.parse(timestamp, TIMESTAMP_FORMAT);
        } catch (Exception e) {
            throw new KeyNotFoundException(keyId, e);
        }
    }
    
    @Override
    public boolean shouldRotateKey(String keyId) {
        try {
            LocalDateTime creationTime = getKeyCreationTime(keyId);
            LocalDateTime now = LocalDateTime.now();
            long ageInMillis = Duration.between(creationTime, now).toMillis();
            
            return ageInMillis >= encryptionProperties.getKeyRotationInterval();
        } catch (Exception e) {
            logger.warn("Failed to check key rotation requirement for key: {}", keyId, e);
            return false;
        }
    }
    
    @Override
    public void cleanupExpiredKeys() {
        logger.info("Starting cleanup of expired keys");
        
        Set<String> allKeyIds = getAllKeyIds();
        int cleanedCount = 0;
        
        for (String keyId : allKeyIds) {
            try {
                if (shouldRotateKey(keyId)) {
                    deleteKey(keyId);
                    cleanedCount++;
                    logger.debug("Cleaned up expired key: {}", keyId);
                }
            } catch (Exception e) {
                logger.error("Failed to cleanup key: {}", keyId, e);
            }
        }
        
        logger.info("Cleanup completed. Removed {} expired keys", cleanedCount);
    }
    
    @Override
    public String generateKeyId() {
        // 生成基于时间戳和随机数的密钥ID
        long timestamp = System.currentTimeMillis();
        int randomSuffix = secureRandom.nextInt(10000);
        String keyId = String.format("key_%d_%04d", timestamp, randomSuffix);
        
        logger.debug("Generated new key ID: {}", keyId);
        return keyId;
    }
}