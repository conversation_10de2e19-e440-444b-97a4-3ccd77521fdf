package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.service.AlertService;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 异常处理配置
 * 启用异常处理相关的配置属性和调度功能
 */
@Configuration
@EnableConfigurationProperties({
    AlertService.AlertProperties.class,
    RetryConfiguration.RetryProperties.class
})
@EnableScheduling
public class ExceptionHandlingConfiguration {
    // 配置类，主要用于启用配置属性
}