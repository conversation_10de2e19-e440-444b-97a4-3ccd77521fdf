package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.properties.RedisProperties;
import com.example.mqtt.websocket.service.ClusterStateManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.ReactiveRedisMessageListenerContainer;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * 基于 Redis 的集群状态管理实现
 */
@Service
public class RedisClusterStateManager implements ClusterStateManager {

    private static final Logger logger = LoggerFactory.getLogger(RedisClusterStateManager.class);

    private final ReactiveRedisTemplate<String, Object> redisTemplate;
    private final ReactiveRedisMessageListenerContainer messageListenerContainer;
    private final RedisProperties redisProperties;
    private final ObjectMapper objectMapper;

    public RedisClusterStateManager(
            ReactiveRedisTemplate<String, Object> redisTemplate,
            ReactiveRedisMessageListenerContainer messageListenerContainer,
            RedisProperties redisProperties,
            ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.messageListenerContainer = messageListenerContainer;
        this.redisProperties = redisProperties;
        this.objectMapper = objectMapper;
    }

    @Override
    public Mono<Void> registerInstance(String instanceId, InstanceInfo instanceInfo) {
        String key = getInstanceKey(instanceId);
        
        return redisTemplate.opsForValue()
                .set(key, instanceInfo, redisProperties.cluster().instanceExpiration())
                .doOnSuccess(result -> logger.info("Instance {} registered successfully", instanceId))
                .doOnError(error -> logger.error("Failed to register instance {}", instanceId, error))
                .then();
    }

    @Override
    public Mono<Void> unregisterInstance(String instanceId) {
        String key = getInstanceKey(instanceId);
        
        return redisTemplate.delete(key)
                .doOnSuccess(result -> logger.info("Instance {} unregistered successfully", instanceId))
                .doOnError(error -> logger.error("Failed to unregister instance {}", instanceId, error))
                .then();
    }

    @Override
    public Mono<Void> updateHeartbeat(String instanceId) {
        String key = getInstanceKey(instanceId);
        
        return redisTemplate.opsForValue()
                .get(key)
                .cast(InstanceInfo.class)
                .flatMap(instanceInfo -> {
                    InstanceInfo updatedInfo = new InstanceInfo(
                            instanceInfo.instanceId(),
                            instanceInfo.host(),
                            instanceInfo.port(),
                            instanceInfo.startTime(),
                            Instant.now(),
                            instanceInfo.metadata()
                    );
                    
                    return redisTemplate.opsForValue()
                            .set(key, updatedInfo, redisProperties.cluster().instanceExpiration());
                })
                .doOnSuccess(result -> logger.debug("Heartbeat updated for instance {}", instanceId))
                .doOnError(error -> logger.error("Failed to update heartbeat for instance {}", instanceId, error))
                .then();
    }

    @Override
    public Mono<Set<String>> getActiveInstances() {
        String pattern = redisProperties.cluster().instanceKeyPrefix() + "*";
        
        return redisTemplate.keys(pattern)
                .map(key -> key.substring(redisProperties.cluster().instanceKeyPrefix().length()))
                .collect(java.util.stream.Collectors.toSet())
                .doOnSuccess(instances -> logger.debug("Found {} active instances", instances.size()))
                .doOnError(error -> logger.error("Failed to get active instances", error));
    }

    @Override
    public Mono<InstanceInfo> getInstanceInfo(String instanceId) {
        String key = getInstanceKey(instanceId);
        
        return redisTemplate.opsForValue()
                .get(key)
                .cast(InstanceInfo.class)
                .doOnSuccess(info -> logger.debug("Retrieved info for instance {}", instanceId))
                .doOnError(error -> logger.error("Failed to get info for instance {}", instanceId, error));
    }

    @Override
    public Mono<Map<String, InstanceInfo>> getAllInstancesInfo() {
        String pattern = redisProperties.cluster().instanceKeyPrefix() + "*";
        
        return redisTemplate.keys(pattern)
                .flatMap(key -> {
                    String instanceId = key.substring(redisProperties.cluster().instanceKeyPrefix().length());
                    return redisTemplate.opsForValue()
                            .get(key)
                            .cast(InstanceInfo.class)
                            .map(info -> Map.entry(instanceId, info));
                })
                .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                .doOnSuccess(instances -> logger.debug("Retrieved info for {} instances", instances.size()))
                .doOnError(error -> logger.error("Failed to get all instances info", error));
    }

    @Override
    public Mono<Void> broadcastToCluster(Object message) {
        ClusterMessage clusterMessage = new ClusterMessage(
                UUID.randomUUID().toString(),
                getCurrentInstanceId(),
                message.getClass().getSimpleName(),
                message,
                Instant.now()
        );

        return Mono.fromCallable(() -> {
                    try {
                        return objectMapper.writeValueAsString(clusterMessage);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException("Failed to serialize cluster message", e);
                    }
                })
                .flatMap(serializedMessage -> 
                    redisTemplate.convertAndSend(redisProperties.cluster().messageChannel(), serializedMessage))
                .doOnSuccess(result -> logger.debug("Broadcasted message to cluster: {}", clusterMessage.messageType()))
                .doOnError(error -> logger.error("Failed to broadcast message to cluster", error))
                .then();
    }

    @Override
    public Flux<ClusterMessage> listenClusterMessages() {
        ChannelTopic topic = new ChannelTopic(redisProperties.cluster().messageChannel());
        
        return messageListenerContainer
                .receive(topic)
                .map(message -> {
                    try {
                        return objectMapper.readValue(message.getMessage(), ClusterMessage.class);
                    } catch (JsonProcessingException e) {
                        logger.error("Failed to deserialize cluster message", e);
                        return null;
                    }
                })
                .filter(clusterMessage -> clusterMessage != null)
                .filter(clusterMessage -> !clusterMessage.sourceInstanceId().equals(getCurrentInstanceId()))
                .doOnNext(clusterMessage -> logger.debug("Received cluster message: {}", clusterMessage.messageType()))
                .doOnError(error -> logger.error("Error listening to cluster messages", error));
    }

    @Override
    public Mono<Boolean> isInstanceHealthy(String instanceId) {
        return getInstanceInfo(instanceId)
                .map(instanceInfo -> {
                    Duration timeSinceLastHeartbeat = Duration.between(
                            instanceInfo.lastHeartbeat(), 
                            Instant.now()
                    );
                    return timeSinceLastHeartbeat.compareTo(redisProperties.cluster().instanceExpiration()) < 0;
                })
                .defaultIfEmpty(false)
                .doOnSuccess(healthy -> logger.debug("Instance {} health check: {}", instanceId, healthy))
                .doOnError(error -> logger.error("Failed to check health for instance {}", instanceId, error));
    }

    @Override
    public Mono<Long> cleanupExpiredInstances() {
        String pattern = redisProperties.cluster().instanceKeyPrefix() + "*";
        
        return redisTemplate.keys(pattern)
                .flatMap(key -> {
                    String instanceId = key.substring(redisProperties.cluster().instanceKeyPrefix().length());
                    return isInstanceHealthy(instanceId)
                            .flatMap(healthy -> {
                                if (!healthy) {
                                    return redisTemplate.delete(key)
                                            .doOnSuccess(result -> 
                                                logger.info("Cleaned up expired instance: {}", instanceId));
                                } else {
                                    return Mono.just(0L);
                                }
                            });
                })
                .reduce(0L, Long::sum)
                .doOnSuccess(count -> logger.info("Cleaned up {} expired instances", count))
                .doOnError(error -> logger.error("Failed to cleanup expired instances", error));
    }

    /**
     * 获取实例在 Redis 中的键名
     */
    private String getInstanceKey(String instanceId) {
        return redisProperties.cluster().instanceKeyPrefix() + instanceId;
    }

    /**
     * 获取当前实例ID（简化实现，实际应用中可能需要更复杂的逻辑）
     */
    private String getCurrentInstanceId() {
        // 这里简化实现，实际应用中应该从配置或环境变量获取
        return System.getProperty("instance.id", "default-instance");
    }
}