package com.example.mqtt.websocket.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket客户端处理类
 * 负责处理WebSocket连接和消息
 */
@Slf4j
@Component
public class WebSocketClientHandler extends TextWebSocketHandler {

    // 存储WebSocket会话
    private static final ConcurrentHashMap<String, WebSocketSession> SESSION_MAP = new ConcurrentHashMap<>();

    // 当前会话
    private WebSocketSession currentSession;

    /**
     * 连接建立后调用
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        log.info("WebSocket连接已建立: {}", session.getId());
        SESSION_MAP.put(session.getId(), session);
        this.currentSession = session;
    }

    /**
     * 接收文本消息
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) {
        String payload = message.getPayload();
        log.info("收到消息: {}", payload);

        // 这里可以添加消息处理逻辑
    }

    /**
     * 连接关闭后调用
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        log.info("WebSocket连接已关闭: {}, 状态: {}", session.getId(), status);
        SESSION_MAP.remove(session.getId());
        if (this.currentSession == session) {
            this.currentSession = null;
        }
    }

    /**
     * 接收二进制消息
     */
    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
        ByteBuffer payload = message.getPayload();
        log.info("收到二进制消息: {} 字节", payload.remaining());
        
        // 这里可以添加二进制消息处理逻辑
        // 例如：将二进制数据转换为对象、图片处理等
    }

    /**
     * 连接错误处理
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket传输错误: {}", exception.getMessage(), exception);
    }

    /**
     * 发送文本消息
     * @param message 要发送的文本消息
     * @return 是否发送成功
     */
    public boolean sendMessage(String message) {
        if (currentSession != null && currentSession.isOpen()) {
            try {
                currentSession.sendMessage(new TextMessage(message));
                return true;
            } catch (IOException e) {
                log.error("发送文本消息失败: {}", e.getMessage(), e);
            }
        } else {
            log.warn("WebSocket连接未建立或已关闭，无法发送文本消息");
        }
        return false;
    }
    
    /**
     * 发送二进制消息
     * @param data 要发送的二进制数据
     * @return 是否发送成功
     */
    public boolean sendBinaryMessage(byte[] data) {
        if (currentSession != null && currentSession.isOpen()) {
            try {
                ByteBuffer buffer = ByteBuffer.wrap(data);
                currentSession.sendMessage(new BinaryMessage(buffer));
                log.info("发送二进制消息: {} 字节", data.length);
                return true;
            } catch (IOException e) {
                log.error("发送二进制消息失败: {}", e.getMessage(), e);
            }
        } else {
            log.warn("WebSocket连接未建立或已关闭，无法发送二进制消息");
        }
        return false;
    }

    /**
     * 获取当前会话
     * @return 当前WebSocket会话
     */
    public WebSocketSession getCurrentSession() {
        return currentSession;
    }
}
