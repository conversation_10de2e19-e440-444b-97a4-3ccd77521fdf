package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.MqttMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.Transformer;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

/**
 * 消息转换器
 * 支持 Protobuf、J<PERSON>N 和文本格式之间的转换
 */
@Component
public class MessageConverter {

    private static final Logger logger = LoggerFactory.getLogger(MessageConverter.class);
    
    private final ObjectMapper objectMapper;
    private final JsonFormat.Printer jsonPrinter;
    private final JsonFormat.Parser jsonParser;

    @Autowired
    public MessageConverter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.jsonPrinter = JsonFormat.printer()
                .includingDefaultValueFields()
                .preservingProtoFieldNames();
        this.jsonParser = JsonFormat.parser()
                .ignoringUnknownFields();
    }

    /**
     * 将原始字节数组转换为 BaseMessage
     * 
     * @param payload 原始消息数据
     * @param contentType 内容类型
     * @return BaseMessage 对象
     */
    @Transformer(inputChannel = "messageTransformChannel", outputChannel = "mqttRoutingChannel")
    public BaseMessage transformToBaseMessage(@Payload byte[] payload, 
                                            @Header(value = "contentType", required = false) String contentType) {
        
        try {
            // 根据内容类型选择转换策略
            if ("application/x-protobuf".equals(contentType)) {
                return convertProtobufToBaseMessage(payload);
            } else if ("application/json".equals(contentType)) {
                return convertJsonToBaseMessage(payload);
            } else {
                // 自动检测格式
                return autoDetectAndConvert(payload);
            }
        } catch (Exception e) {
            logger.error("Failed to transform message: {}", e.getMessage());
            return createErrorMessage(payload, e);
        }
    }

    /**
     * 将 BaseMessage 转换为 JSON 字符串
     * 
     * @param baseMessage BaseMessage 对象
     * @return JSON 字符串
     */
    public String convertToJson(BaseMessage baseMessage) {
        try {
            return jsonPrinter.print(baseMessage);
        } catch (Exception e) {
            logger.error("Failed to convert BaseMessage to JSON: {}", e.getMessage());
            throw new MessageConversionException("JSON conversion failed", e);
        }
    }

    /**
     * 将 BaseMessage 转换为 Protobuf 字节数组
     * 
     * @param baseMessage BaseMessage 对象
     * @return Protobuf 字节数组
     */
    public byte[] convertToProtobuf(BaseMessage baseMessage) {
        try {
            return baseMessage.toByteArray();
        } catch (Exception e) {
            logger.error("Failed to convert BaseMessage to Protobuf: {}", e.getMessage());
            throw new MessageConversionException("Protobuf conversion failed", e);
        }
    }

    /**
     * 将 JSON 字符串转换为 BaseMessage
     * 
     * @param json JSON 字符串
     * @return BaseMessage 对象
     */
    public BaseMessage convertFromJson(String json) {
        try {
            BaseMessage.Builder builder = BaseMessage.newBuilder();
            jsonParser.merge(json, builder);
            return builder.build();
        } catch (Exception e) {
            logger.error("Failed to convert JSON to BaseMessage: {}", e.getMessage());
            throw new MessageConversionException("JSON parsing failed", e);
        }
    }

    /**
     * 将 Protobuf 字节数组转换为 BaseMessage
     * 
     * @param data Protobuf 字节数组
     * @return BaseMessage 对象
     */
    public BaseMessage convertFromProtobuf(byte[] data) {
        try {
            return BaseMessage.parseFrom(data);
        } catch (InvalidProtocolBufferException e) {
            logger.error("Failed to parse Protobuf data: {}", e.getMessage());
            throw new MessageConversionException("Protobuf parsing failed", e);
        }
    }

    /**
     * 将 BaseMessage 转换为 MqttMessage
     * 
     * @param baseMessage BaseMessage 对象
     * @param topic MQTT 主题
     * @param qos QoS 等级
     * @param retained 是否保留
     * @return MqttMessage 对象
     */
    public MqttMessage convertToMqttMessage(BaseMessage baseMessage, String topic, int qos, boolean retained) {
        return MqttMessage.newBuilder()
                .setTopic(topic)
                .setQos(qos)
                .setRetained(retained)
                .setMessage(baseMessage)
                .build();
    }

    /**
     * 将 BaseMessage 转换为 WebSocketMessage
     * 
     * @param baseMessage BaseMessage 对象
     * @param sessionId WebSocket 会话ID
     * @param userId 用户ID
     * @param messageType 消息类型
     * @return WebSocketMessage 对象
     */
    public WebSocketMessage convertToWebSocketMessage(BaseMessage baseMessage, 
                                                    String sessionId, 
                                                    String userId, 
                                                    WebSocketMessage.MessageType messageType) {
        return WebSocketMessage.newBuilder()
                .setSessionId(sessionId)
                .setUserId(userId)
                .setType(messageType)
                .setMessage(baseMessage)
                .build();
    }

    /**
     * 自动检测消息格式并转换
     */
    private BaseMessage autoDetectAndConvert(byte[] payload) {
        // 尝试 Protobuf 格式
        try {
            return BaseMessage.parseFrom(payload);
        } catch (InvalidProtocolBufferException e) {
            logger.debug("Not a valid Protobuf message, trying JSON");
        }

        // 尝试 JSON 格式
        try {
            return convertJsonToBaseMessage(payload);
        } catch (Exception e) {
            logger.debug("Not a valid JSON message, treating as text");
        }

        // 作为文本处理
        return convertTextToBaseMessage(payload);
    }

    /**
     * 将 Protobuf 字节数组转换为 BaseMessage
     */
    private BaseMessage convertProtobufToBaseMessage(byte[] payload) throws InvalidProtocolBufferException {
        // 首先尝试解析为 MqttMessage
        try {
            MqttMessage mqttMessage = MqttMessage.parseFrom(payload);
            return mqttMessage.getMessage();
        } catch (InvalidProtocolBufferException e) {
            // 直接解析为 BaseMessage
            return BaseMessage.parseFrom(payload);
        }
    }

    /**
     * 将 JSON 字节数组转换为 BaseMessage
     */
    private BaseMessage convertJsonToBaseMessage(byte[] payload) throws Exception {
        String jsonString = new String(payload);
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        
        BaseMessage.Builder builder = BaseMessage.newBuilder();
        
        // 设置基本字段
        if (jsonNode.has("id")) {
            builder.setId(jsonNode.get("id").asText());
        } else {
            builder.setId(UUID.randomUUID().toString());
        }
        
        if (jsonNode.has("timestamp")) {
            builder.setTimestamp(jsonNode.get("timestamp").asLong());
        } else {
            builder.setTimestamp(Instant.now().toEpochMilli());
        }
        
        if (jsonNode.has("type")) {
            builder.setType(jsonNode.get("type").asText());
        } else {
            builder.setType("json");
        }
        
        if (jsonNode.has("source")) {
            builder.setSource(jsonNode.get("source").asText());
        } else {
            builder.setSource("mqtt");
        }
        
        // 设置头信息
        if (jsonNode.has("headers") && jsonNode.get("headers").isObject()) {
            JsonNode headersNode = jsonNode.get("headers");
            headersNode.fieldNames().forEachRemaining(fieldName -> {
                builder.putHeaders(fieldName, headersNode.get(fieldName).asText());
            });
        }
        
        // 设置载荷
        if (jsonNode.has("payload")) {
            JsonNode payloadNode = jsonNode.get("payload");
            if (payloadNode.isTextual()) {
                builder.setPayload(com.google.protobuf.ByteString.copyFromUtf8(payloadNode.asText()));
            } else {
                builder.setPayload(com.google.protobuf.ByteString.copyFromUtf8(payloadNode.toString()));
            }
        } else {
            // 整个 JSON 作为载荷
            builder.setPayload(com.google.protobuf.ByteString.copyFrom(payload));
        }
        
        // 设置加密标志
        if (jsonNode.has("encrypted")) {
            builder.setEncrypted(jsonNode.get("encrypted").asBoolean());
        }
        
        // 设置签名
        if (jsonNode.has("signature")) {
            builder.setSignature(jsonNode.get("signature").asText());
        }
        
        return builder.build();
    }

    /**
     * 将文本字节数组转换为 BaseMessage
     */
    private BaseMessage convertTextToBaseMessage(byte[] payload) {
        return BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("text")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom(payload))
                .setEncrypted(false)
                .build();
    }

    /**
     * 创建错误消息
     */
    private BaseMessage createErrorMessage(byte[] originalPayload, Exception error) {
        return BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("error")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom(originalPayload))
                .setEncrypted(false)
                .putHeaders("error", error.getMessage())
                .putHeaders("errorType", error.getClass().getSimpleName())
                .build();
    }

    /**
     * 消息转换异常
     */
    public static class MessageConversionException extends RuntimeException {
        public MessageConversionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}