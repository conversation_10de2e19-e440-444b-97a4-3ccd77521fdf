package com.example.mqtt.websocket.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Min;

/**
 * 故障转移配置属性
 */
@ConfigurationProperties(prefix = "app.failover")
@Validated
public class FailoverProperties {

    /**
     * 健康检查间隔（秒）
     */
    @Min(1)
    private int healthCheckInterval = 30;

    /**
     * 健康检查超时（秒）
     */
    @Min(1)
    private int healthCheckTimeout = 5;

    /**
     * 最大重试次数
     */
    @Min(1)
    private int maxRetries = 3;

    /**
     * 故障阈值
     */
    @Min(1)
    private int failureThreshold = 3;

    /**
     * 恢复阈值
     */
    @Min(1)
    private int recoveryThreshold = 2;

    /**
     * 是否启用自动故障转移
     */
    private boolean autoFailoverEnabled = true;

    /**
     * 是否启用自动恢复
     */
    private boolean autoRecoveryEnabled = true;

    /**
     * 故障检测启动延迟（秒）
     */
    @Min(0)
    private int startupDelay = 60;

    /**
     * 是否在启动时自动开始故障检测
     */
    private boolean autoStartDetection = true;

    /**
     * 故障转移通知配置
     */
    private NotificationConfig notification = new NotificationConfig();

    // Getters and Setters
    public int getHealthCheckInterval() {
        return healthCheckInterval;
    }

    public void setHealthCheckInterval(int healthCheckInterval) {
        this.healthCheckInterval = healthCheckInterval;
    }

    public int getHealthCheckTimeout() {
        return healthCheckTimeout;
    }

    public void setHealthCheckTimeout(int healthCheckTimeout) {
        this.healthCheckTimeout = healthCheckTimeout;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    public int getFailureThreshold() {
        return failureThreshold;
    }

    public void setFailureThreshold(int failureThreshold) {
        this.failureThreshold = failureThreshold;
    }

    public int getRecoveryThreshold() {
        return recoveryThreshold;
    }

    public void setRecoveryThreshold(int recoveryThreshold) {
        this.recoveryThreshold = recoveryThreshold;
    }

    public boolean isAutoFailoverEnabled() {
        return autoFailoverEnabled;
    }

    public void setAutoFailoverEnabled(boolean autoFailoverEnabled) {
        this.autoFailoverEnabled = autoFailoverEnabled;
    }

    public boolean isAutoRecoveryEnabled() {
        return autoRecoveryEnabled;
    }

    public void setAutoRecoveryEnabled(boolean autoRecoveryEnabled) {
        this.autoRecoveryEnabled = autoRecoveryEnabled;
    }

    public int getStartupDelay() {
        return startupDelay;
    }

    public void setStartupDelay(int startupDelay) {
        this.startupDelay = startupDelay;
    }

    public boolean isAutoStartDetection() {
        return autoStartDetection;
    }

    public void setAutoStartDetection(boolean autoStartDetection) {
        this.autoStartDetection = autoStartDetection;
    }

    public NotificationConfig getNotification() {
        return notification;
    }

    public void setNotification(NotificationConfig notification) {
        this.notification = notification;
    }

    /**
     * 通知配置
     */
    public static class NotificationConfig {
        private boolean enabled = true;
        private String webhookUrl;
        private String emailRecipients;
        private boolean slackEnabled = false;
        private String slackChannel;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getWebhookUrl() {
            return webhookUrl;
        }

        public void setWebhookUrl(String webhookUrl) {
            this.webhookUrl = webhookUrl;
        }

        public String getEmailRecipients() {
            return emailRecipients;
        }

        public void setEmailRecipients(String emailRecipients) {
            this.emailRecipients = emailRecipients;
        }

        public boolean isSlackEnabled() {
            return slackEnabled;
        }

        public void setSlackEnabled(boolean slackEnabled) {
            this.slackEnabled = slackEnabled;
        }

        public String getSlackChannel() {
            return slackChannel;
        }

        public void setSlackChannel(String slackChannel) {
            this.slackChannel = slackChannel;
        }
    }
}