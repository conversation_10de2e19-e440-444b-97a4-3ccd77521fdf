package com.example.mqtt.websocket.service;

import javax.crypto.SecretKey;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 密钥管理接口，支持密钥生成、存储和轮换
 */
public interface KeyManager {
    
    /**
     * 获取指定ID的密钥
     * 
     * @param keyId 密钥ID
     * @return 密钥对象
     * @throws KeyNotFoundException 密钥不存在时抛出
     */
    SecretKey getKey(String keyId) throws KeyNotFoundException;
    
    /**
     * 生成新的密钥
     * 
     * @param keyId 密钥ID
     * @return 生成的密钥
     * @throws KeyGenerationException 密钥生成失败时抛出
     */
    SecretKey generateKey(String keyId) throws KeyGenerationException;
    
    /**
     * 轮换指定的密钥
     * 
     * @param keyId 要轮换的密钥ID
     * @return 新生成的密钥
     * @throws KeyRotationException 密钥轮换失败时抛出
     */
    SecretKey rotateKey(String keyId) throws KeyRotationException;
    
    /**
     * 删除指定的密钥
     * 
     * @param keyId 要删除的密钥ID
     * @throws KeyDeletionException 密钥删除失败时抛出
     */
    void deleteKey(String keyId) throws KeyDeletionException;
    
    /**
     * 获取所有密钥ID
     * 
     * @return 所有密钥ID的集合
     */
    Set<String> getAllKeyIds();
    
    /**
     * 检查密钥是否存在
     * 
     * @param keyId 密钥ID
     * @return 如果密钥存在返回true，否则返回false
     */
    boolean keyExists(String keyId);
    
    /**
     * 获取密钥的创建时间
     * 
     * @param keyId 密钥ID
     * @return 密钥创建时间
     * @throws KeyNotFoundException 密钥不存在时抛出
     */
    LocalDateTime getKeyCreationTime(String keyId) throws KeyNotFoundException;
    
    /**
     * 检查密钥是否需要轮换
     * 
     * @param keyId 密钥ID
     * @return 如果需要轮换返回true，否则返回false
     */
    boolean shouldRotateKey(String keyId);
    
    /**
     * 清理过期的密钥
     */
    void cleanupExpiredKeys();
    
    /**
     * 生成新的密钥ID
     * 
     * @return 新的密钥ID
     */
    String generateKeyId();
}