package com.example.mqtt.websocket.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 配置管理 REST 控制器
 * 提供系统配置的查询和管理接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/config")
@Validated
public class ConfigurationController {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationController.class);
    
    @Value("${app.mqtt.broker-url}")
    private String mqttBrokerUrl;
    
    @Value("${app.mqtt.subscribe-topics}")
    private List<String> mqttSubscribeTopics;
    
    @Value("${app.websocket.endpoint}")
    private String websocketEndpoint;
    
    @Value("${app.cluster.instance-id}")
    private String instanceId;
    
    /**
     * 获取 MQTT 配置信息
     * 
     * @return MQTT 配置
     */
    @GetMapping("/mqtt")
    public Mono<ResponseEntity<MqttConfig>> getMqttConfig() {
        logger.info("Fetching MQTT configuration");
        
        return Mono.fromCallable(() -> {
            MqttConfig config = new MqttConfig();
            config.setBrokerUrl(mqttBrokerUrl);
            config.setSubscribeTopics(mqttSubscribeTopics);
            config.setConnected(true); // 实际实现中应该检查连接状态
            
            return ResponseEntity.ok(config);
        })
        .doOnSuccess(config -> logger.info("MQTT configuration retrieved successfully"))
        .onErrorResume(error -> {
            logger.error("Failed to get MQTT configuration: error={}", error.getMessage());
            return Mono.just(ResponseEntity.internalServerError().build());
        });
    }
    
    /**
     * 获取 WebSocket 配置信息
     * 
     * @return WebSocket 配置
     */
    @GetMapping("/websocket")
    public Mono<ResponseEntity<WebSocketConfig>> getWebSocketConfig() {
        logger.info("Fetching WebSocket configuration");
        
        return Mono.fromCallable(() -> {
            WebSocketConfig config = new WebSocketConfig();
            config.setEndpoint(websocketEndpoint);
            config.setActiveConnections(0); // 实际实现中应该从 SessionManager 获取
            
            return ResponseEntity.ok(config);
        })
        .doOnSuccess(config -> logger.info("WebSocket configuration retrieved successfully"))
        .onErrorResume(error -> {
            logger.error("Failed to get WebSocket configuration: error={}", error.getMessage());
            return Mono.just(ResponseEntity.internalServerError().build());
        });
    }
    
    /**
     * 获取集群配置信息
     * 
     * @return 集群配置
     */
    @GetMapping("/cluster")
    public Mono<ResponseEntity<ClusterConfig>> getClusterConfig() {
        logger.info("Fetching cluster configuration");
        
        return Mono.fromCallable(() -> {
            ClusterConfig config = new ClusterConfig();
            config.setInstanceId(instanceId);
            config.setActiveInstances(List.of(instanceId)); // 实际实现中应该从集群管理器获取
            config.setHealthy(true);
            
            return ResponseEntity.ok(config);
        })
        .doOnSuccess(config -> logger.info("Cluster configuration retrieved successfully"))
        .onErrorResume(error -> {
            logger.error("Failed to get cluster configuration: error={}", error.getMessage());
            return Mono.just(ResponseEntity.internalServerError().build());
        });
    }
    
    /**
     * 获取系统信息
     * 
     * @return 系统信息
     */
    @GetMapping("/system")
    public Mono<ResponseEntity<SystemInfo>> getSystemInfo() {
        logger.info("Fetching system information");
        
        return Mono.fromCallable(() -> {
            SystemInfo info = new SystemInfo();
            info.setInstanceId(instanceId);
            info.setUptime(System.currentTimeMillis()); // 简化实现
            info.setJavaVersion(System.getProperty("java.version"));
            info.setSpringBootVersion("2.7.18"); // 从 pom.xml 获取
            
            // JVM 信息
            Runtime runtime = Runtime.getRuntime();
            info.setTotalMemory(runtime.totalMemory());
            info.setFreeMemory(runtime.freeMemory());
            info.setMaxMemory(runtime.maxMemory());
            info.setUsedMemory(runtime.totalMemory() - runtime.freeMemory());
            
            return ResponseEntity.ok(info);
        })
        .doOnSuccess(info -> logger.info("System information retrieved successfully"))
        .onErrorResume(error -> {
            logger.error("Failed to get system information: error={}", error.getMessage());
            return Mono.just(ResponseEntity.internalServerError().build());
        });
    }
    
    /**
     * 更新 MQTT 订阅主题
     * 
     * @param request 更新请求
     * @return 更新结果
     */
    @PutMapping("/mqtt/topics")
    public Mono<ResponseEntity<Map<String, Object>>> updateMqttTopics(
            @Valid @RequestBody UpdateTopicsRequest request) {
        
        logger.info("Updating MQTT subscribe topics: topics={}", request.getTopics());
        
        return Mono.fromCallable(() -> {
            // 实际实现中应该更新 MQTT 订阅
            // mqttGateway.updateSubscriptions(request.getTopics());
            
            Map<String, Object> response = Map.of(
                "message", "Topics updated successfully",
                "topics", request.getTopics(),
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(response);
        })
        .doOnSuccess(response -> logger.info("MQTT topics updated successfully"))
        .onErrorResume(error -> {
            logger.error("Failed to update MQTT topics: error={}", error.getMessage());
            Map<String, Object> errorResponse = Map.of(
                "error", "Failed to update topics: " + error.getMessage(),
                "timestamp", System.currentTimeMillis()
            );
            return Mono.just(ResponseEntity.internalServerError().body(errorResponse));
        });
    }
    
    // DTO 类
    public static class MqttConfig {
        private String brokerUrl;
        private List<String> subscribeTopics;
        private boolean connected;
        
        // Getters and setters
        public String getBrokerUrl() { return brokerUrl; }
        public void setBrokerUrl(String brokerUrl) { this.brokerUrl = brokerUrl; }
        
        public List<String> getSubscribeTopics() { return subscribeTopics; }
        public void setSubscribeTopics(List<String> subscribeTopics) { this.subscribeTopics = subscribeTopics; }
        
        public boolean isConnected() { return connected; }
        public void setConnected(boolean connected) { this.connected = connected; }
    }
    
    public static class WebSocketConfig {
        private String endpoint;
        private int activeConnections;
        
        // Getters and setters
        public String getEndpoint() { return endpoint; }
        public void setEndpoint(String endpoint) { this.endpoint = endpoint; }
        
        public int getActiveConnections() { return activeConnections; }
        public void setActiveConnections(int activeConnections) { this.activeConnections = activeConnections; }
    }
    
    public static class ClusterConfig {
        private String instanceId;
        private List<String> activeInstances;
        private boolean healthy;
        
        // Getters and setters
        public String getInstanceId() { return instanceId; }
        public void setInstanceId(String instanceId) { this.instanceId = instanceId; }
        
        public List<String> getActiveInstances() { return activeInstances; }
        public void setActiveInstances(List<String> activeInstances) { this.activeInstances = activeInstances; }
        
        public boolean isHealthy() { return healthy; }
        public void setHealthy(boolean healthy) { this.healthy = healthy; }
    }
    
    public static class SystemInfo {
        private String instanceId;
        private long uptime;
        private String javaVersion;
        private String springBootVersion;
        private long totalMemory;
        private long freeMemory;
        private long maxMemory;
        private long usedMemory;
        
        // Getters and setters
        public String getInstanceId() { return instanceId; }
        public void setInstanceId(String instanceId) { this.instanceId = instanceId; }
        
        public long getUptime() { return uptime; }
        public void setUptime(long uptime) { this.uptime = uptime; }
        
        public String getJavaVersion() { return javaVersion; }
        public void setJavaVersion(String javaVersion) { this.javaVersion = javaVersion; }
        
        public String getSpringBootVersion() { return springBootVersion; }
        public void setSpringBootVersion(String springBootVersion) { this.springBootVersion = springBootVersion; }
        
        public long getTotalMemory() { return totalMemory; }
        public void setTotalMemory(long totalMemory) { this.totalMemory = totalMemory; }
        
        public long getFreeMemory() { return freeMemory; }
        public void setFreeMemory(long freeMemory) { this.freeMemory = freeMemory; }
        
        public long getMaxMemory() { return maxMemory; }
        public void setMaxMemory(long maxMemory) { this.maxMemory = maxMemory; }
        
        public long getUsedMemory() { return usedMemory; }
        public void setUsedMemory(long usedMemory) { this.usedMemory = usedMemory; }
    }
    
    public static class UpdateTopicsRequest {
        @NotNull(message = "Topics list is required")
        private List<@NotBlank String> topics;
        
        // Getters and setters
        public List<String> getTopics() { return topics; }
        public void setTopics(List<String> topics) { this.topics = topics; }
    }
}