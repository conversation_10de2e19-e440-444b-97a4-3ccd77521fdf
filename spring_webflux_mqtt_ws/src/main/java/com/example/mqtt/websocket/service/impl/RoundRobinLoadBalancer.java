package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 轮询负载均衡器实现
 */
public class RoundRobinLoadBalancer implements LoadBalancer {

    private static final Logger logger = LoggerFactory.getLogger(RoundRobinLoadBalancer.class);

    private final ClusterStateManager clusterStateManager;
    private final AtomicInteger roundRobinCounter = new AtomicInteger(0);
    private final Map<String, Boolean> instanceHealthMap = new ConcurrentHashMap<>();
    private final Map<String, Integer> instanceWeightMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> requestCountMap = new ConcurrentHashMap<>();
    private final AtomicLong totalRequests = new AtomicLong(0);

    public RoundRobinLoadBalancer(ClusterStateManager clusterStateManager) {
        this.clusterStateManager = clusterStateManager;
    }

    @Override
    public Mono<String> selectInstance(String key) {
        return selectInstance();
    }

    @Override
    public Mono<String> selectInstance() {
        return getHealthyInstances()
            .flatMap(instances -> {
                if (instances.isEmpty()) {
                    logger.warn("No healthy instances available for load balancing");
                    return Mono.empty();
                }

                // 使用轮询算法选择实例
                int index = Math.abs(roundRobinCounter.getAndIncrement()) % instances.size();
                String selectedInstance = instances.get(index);
                
                // 更新统计信息
                requestCountMap.computeIfAbsent(selectedInstance, k -> new AtomicLong(0)).incrementAndGet();
                totalRequests.incrementAndGet();
                
                logger.debug("Selected instance: {} (index: {}, total instances: {})", 
                    selectedInstance, index, instances.size());
                
                return Mono.just(selectedInstance);
            })
            .doOnError(error -> logger.error("Error selecting instance", error));
    }

    @Override
    public Mono<Void> updateInstanceHealth(String instanceId, boolean healthy) {
        return Mono.fromRunnable(() -> {
            boolean previousHealth = instanceHealthMap.getOrDefault(instanceId, true);
            instanceHealthMap.put(instanceId, healthy);
            
            if (previousHealth != healthy) {
                logger.info("Instance {} health status changed: {} -> {}", 
                    instanceId, previousHealth, healthy);
            }
        });
    }

    @Override
    public Mono<List<String>> getHealthyInstances() {
        return clusterStateManager.getActiveInstances()
            .map(activeInstances -> {
                List<String> healthyInstances = new ArrayList<>();
                for (String instanceId : activeInstances) {
                    if (instanceHealthMap.getOrDefault(instanceId, true)) {
                        healthyInstances.add(instanceId);
                    }
                }
                return healthyInstances;
            })
            .doOnNext(instances -> logger.debug("Found {} healthy instances: {}", 
                instances.size(), instances));
    }

    @Override
    public Mono<Integer> getInstanceWeight(String instanceId) {
        return Mono.just(instanceWeightMap.getOrDefault(instanceId, 1));
    }

    @Override
    public Mono<Void> setInstanceWeight(String instanceId, int weight) {
        return Mono.fromRunnable(() -> {
            if (weight <= 0) {
                throw new IllegalArgumentException("Instance weight must be positive");
            }
            instanceWeightMap.put(instanceId, weight);
            logger.info("Set weight for instance {}: {}", instanceId, weight);
        });
    }

    @Override
    public Mono<LoadBalancerStats> getStats() {
        return Mono.fromCallable(() -> {
            Map<String, Long> requestCounts = new HashMap<>();
            requestCountMap.forEach((instanceId, count) -> 
                requestCounts.put(instanceId, count.get()));

            return new LoadBalancerStats(
                requestCounts,
                new HashMap<>(instanceHealthMap),
                new HashMap<>(instanceWeightMap),
                totalRequests.get(),
                Strategy.ROUND_ROBIN
            );
        });
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        requestCountMap.clear();
        totalRequests.set(0);
        logger.info("Load balancer statistics reset");
    }

    /**
     * 获取下一个轮询索引
     */
    private int getNextRoundRobinIndex(int instanceCount) {
        return Math.abs(roundRobinCounter.getAndIncrement()) % instanceCount;
    }
}
