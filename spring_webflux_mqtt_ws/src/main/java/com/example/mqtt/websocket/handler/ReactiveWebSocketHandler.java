package com.example.mqtt.websocket.handler;

import com.example.mqtt.websocket.service.MessageBridgeService;
import com.example.mqtt.websocket.service.MessageConverter;
import com.example.mqtt.websocket.service.WebSocketSessionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

/**
 * 响应式 WebSocket 处理器
 * 处理 WebSocket 连接和消息，并与 MQTT 进行桥接
 * 
 * <AUTHOR>
 */
@Component
public class ReactiveWebSocketHandler extends TextWebSocketHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(ReactiveWebSocketHandler.class);
    
    private final WebSocketSessionManager sessionManager;
    private final MessageBridgeService messageBridgeService;
    private final MessageConverter messageConverter;
    private final MessageChannel webSocketInboundChannel;
    
    @Autowired
    public ReactiveWebSocketHandler(
            WebSocketSessionManager sessionManager,
            MessageBridgeService messageBridgeService,
            MessageConverter messageConverter,
            MessageChannel webSocketInboundChannel) {
        this.sessionManager = sessionManager;
        this.messageBridgeService = messageBridgeService;
        this.messageConverter = messageConverter;
        this.webSocketInboundChannel = webSocketInboundChannel;
    }
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        String userId = extractUserId(session);
        
        logger.info("WebSocket connection established: sessionId={}, userId={}, remoteAddress={}", 
                   sessionId, userId, session.getRemoteAddress());
        
        // 注册会话
        sessionManager.registerSession(session, userId);
        
        // 发送连接确认消息
        sendConnectionConfirmation(session);
        
        super.afterConnectionEstablished(session);
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String sessionId = session.getId();
        String userId = sessionManager.getUserIdBySession(sessionId);
        
        logger.info("WebSocket connection closed: sessionId={}, userId={}, status={}", 
                   sessionId, userId, status);
        
        // 注销会话
        sessionManager.unregisterSession(sessionId);
        
        super.afterConnectionClosed(session, status);
    }
    
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload();
        
        logger.debug("Received WebSocket text message: sessionId={}, payloadSize={}", 
                    sessionId, payload.length());
        
        try {
            // 验证消息格式
            if (!messageConverter.isValidMessageFormat(payload)) {
                logger.warn("Invalid message format received: sessionId={}", sessionId);
                sendErrorMessage(session, "Invalid message format", null);
                return;
            }
            
            // 发送消息到 Spring Integration 通道进行处理
            org.springframework.messaging.Message<String> integrationMessage = 
                MessageBuilder.withPayload(payload)
                    .setHeader("sessionId", sessionId)
                    .setHeader("timestamp", System.currentTimeMillis())
                    .build();
            
            webSocketInboundChannel.send(integrationMessage);
            
            logger.debug("Forwarded WebSocket message to integration channel: sessionId={}", sessionId);
            
        } catch (Exception e) {
            logger.error("Error handling WebSocket message: sessionId={}, error={}", sessionId, e.getMessage());
            sendErrorMessage(session, "Message processing failed", null);
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        logger.error("WebSocket transport error: sessionId={}, error={}", sessionId, exception.getMessage());
        
        // 清理会话
        sessionManager.unregisterSession(sessionId);
        
        super.handleTransportError(session, exception);
    }
    
    /**
     * 从会话中提取用户ID
     * 
     * @param session WebSocket 会话
     * @return 用户ID，如果未找到返回null
     */
    private String extractUserId(WebSocketSession session) {
        // 从查询参数中提取用户ID
        String query = session.getUri().getQuery();
        if (query != null) {
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2 && "userId".equals(keyValue[0])) {
                    return keyValue[1];
                }
            }
        }
        
        // 从会话属性中提取用户ID
        Object userIdAttr = session.getAttributes().get("userId");
        if (userIdAttr != null) {
            return userIdAttr.toString();
        }
        
        return null;
    }
    
    /**
     * 发送连接确认消息
     * 
     * @param session WebSocket 会话
     */
    private void sendConnectionConfirmation(WebSocketSession session) {
        try {
            String confirmationMessage = String.format(
                "{\"type\":\"connection_confirmed\",\"sessionId\":\"%s\",\"timestamp\":%d,\"message\":\"WebSocket connection established successfully\"}", 
                session.getId(), System.currentTimeMillis()
            );
            
            session.sendMessage(new TextMessage(confirmationMessage));
            logger.debug("Sent connection confirmation: sessionId={}", session.getId());
            
        } catch (Exception e) {
            logger.error("Failed to send connection confirmation: sessionId={}, error={}", 
                        session.getId(), e.getMessage());
        }
    }
    
    /**
     * 发送错误消息
     * 
     * @param session WebSocket 会话
     * @param errorMessage 错误信息
     * @param originalMessageId 原始消息ID
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage, String originalMessageId) {
        try {
            String errorResponse = String.format(
                "{\"type\":\"error\",\"sessionId\":\"%s\",\"timestamp\":%d,\"error\":\"%s\",\"originalMessageId\":\"%s\"}", 
                session.getId(), System.currentTimeMillis(), errorMessage, 
                originalMessageId != null ? originalMessageId : "unknown"
            );
            
            session.sendMessage(new TextMessage(errorResponse));
            logger.debug("Sent error message: sessionId={}, error={}", session.getId(), errorMessage);
            
        } catch (Exception e) {
            logger.error("Failed to send error message: sessionId={}, error={}", 
                        session.getId(), e.getMessage());
        }
    }
    
    /**
     * 检查会话是否支持子协议
     * 
     * @param session WebSocket 会话
     * @param protocol 协议名称
     * @return 是否支持
     */
    private boolean supportsProtocol(WebSocketSession session, String protocol) {
        return session.getAcceptedProtocol() != null && 
               session.getAcceptedProtocol().equals(protocol);
    }
    
    /**
     * 获取会话统计信息
     * 
     * @param session WebSocket 会话
     * @return 统计信息JSON
     */
    private String getSessionStats(WebSocketSession session) {
        return String.format(
            "{\"sessionId\":\"%s\",\"isOpen\":%b,\"textMessageSizeLimit\":%d,\"binaryMessageSizeLimit\":%d,\"remoteAddress\":\"%s\"}", 
            session.getId(), session.isOpen(), session.getTextMessageSizeLimit(), 
            session.getBinaryMessageSizeLimit(), session.getRemoteAddress()
        );
    }
}