package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.config.properties.EncryptionProperties;
import com.example.mqtt.websocket.service.EncryptionService;
import com.example.mqtt.websocket.service.KeyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 加密服务配置类
 */
@Configuration
@EnableScheduling
@EnableConfigurationProperties(EncryptionProperties.class)
public class EncryptionConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(EncryptionConfig.class);
    
    private final KeyManager keyManager;
    
    public EncryptionConfig(KeyManager keyManager) {
        this.keyManager = keyManager;
    }
    
    /**
     * 定期清理过期密钥的调度任务
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1 hour
    public void cleanupExpiredKeys() {
        try {
            logger.info("Starting scheduled key cleanup");
            keyManager.cleanupExpiredKeys();
        } catch (Exception e) {
            logger.error("Failed to cleanup expired keys", e);
        }
    }
    
    /**
     * 定期检查密钥轮换的调度任务
     * 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30 minutes
    public void checkKeyRotation() {
        try {
            logger.debug("Checking for keys that need rotation");
            
            for (String keyId : keyManager.getAllKeyIds()) {
                if (keyManager.shouldRotateKey(keyId)) {
                    logger.info("Key {} needs rotation", keyId);
                    // 这里可以发送通知或触发自动轮换
                    // 实际的轮换操作应该由管理员或自动化系统触发
                }
            }
        } catch (Exception e) {
            logger.error("Failed to check key rotation requirements", e);
        }
    }
}