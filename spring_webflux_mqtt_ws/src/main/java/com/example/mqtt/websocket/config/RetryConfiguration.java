package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.exception.MqttConnectionException;
import com.example.mqtt.websocket.exception.TemporaryEncryptionException;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryListener;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 重试机制配置
 * 为 MQTT 连接和加密操作提供重试支持
 */
@Configuration
@EnableRetry
public class RetryConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(RetryConfiguration.class);
    
    /**
     * MQTT 操作重试模板
     * 使用指数退避策略，最大重试3次
     */
    @Bean("mqttRetryTemplate")
    public RetryTemplate mqttRetryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        
        // 配置重试策略
        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(MqttException.class, true);
        retryableExceptions.put(MqttConnectionException.class, true);
        
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(3, retryableExceptions);
        retryTemplate.setRetryPolicy(retryPolicy);
        
        // 配置指数退避策略
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000); // 1秒
        backOffPolicy.setMultiplier(2.0); // 每次翻倍
        backOffPolicy.setMaxInterval(10000); // 最大10秒
        retryTemplate.setBackOffPolicy(backOffPolicy);
        
        // 添加重试监听器
        retryTemplate.registerListener(new MqttRetryListener());
        
        return retryTemplate;
    }
    
    /**
     * 加密操作重试模板
     * 使用固定间隔策略，最大重试2次
     */
    @Bean("encryptionRetryTemplate")
    public RetryTemplate encryptionRetryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        
        // 配置重试策略 - 只对临时加密异常重试
        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(TemporaryEncryptionException.class, true);
        
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(2, retryableExceptions);
        retryTemplate.setRetryPolicy(retryPolicy);
        
        // 配置固定间隔退避策略
        FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
        backOffPolicy.setBackOffPeriod(500); // 500毫秒
        retryTemplate.setBackOffPolicy(backOffPolicy);
        
        // 添加重试监听器
        retryTemplate.registerListener(new EncryptionRetryListener());
        
        return retryTemplate;
    }
    
    /**
     * 通用重试模板
     * 用于其他需要重试的操作
     */
    @Bean("generalRetryTemplate")
    public RetryTemplate generalRetryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        
        // 配置重试策略
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(2);
        retryTemplate.setRetryPolicy(retryPolicy);
        
        // 配置固定间隔退避策略
        FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
        backOffPolicy.setBackOffPeriod(1000); // 1秒
        retryTemplate.setBackOffPolicy(backOffPolicy);
        
        return retryTemplate;
    }
    
    /**
     * MQTT 重试监听器
     */
    private static class MqttRetryListener implements RetryListener {
        
        @Override
        public <T, E extends Throwable> boolean open(RetryContext context, RetryCallback<T, E> callback) {
            logger.debug("Starting MQTT retry operation");
            return true;
        }
        
        @Override
        public <T, E extends Throwable> void onError(RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
            logger.warn("MQTT retry attempt {} failed: {}", 
                context.getRetryCount(), throwable.getMessage());
        }
        
        @Override
        public <T, E extends Throwable> void close(RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
            if (throwable != null) {
                logger.error("MQTT retry operation failed after {} attempts", 
                    context.getRetryCount(), throwable);
            } else {
                logger.info("MQTT retry operation succeeded after {} attempts", 
                    context.getRetryCount());
            }
        }
    }
    
    /**
     * 加密重试监听器
     */
    private static class EncryptionRetryListener implements RetryListener {
        
        @Override
        public <T, E extends Throwable> boolean open(RetryContext context, RetryCallback<T, E> callback) {
            logger.debug("Starting encryption retry operation");
            return true;
        }
        
        @Override
        public <T, E extends Throwable> void onError(RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
            logger.warn("Encryption retry attempt {} failed: {}", 
                context.getRetryCount(), throwable.getMessage());
        }
        
        @Override
        public <T, E extends Throwable> void close(RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
            if (throwable != null) {
                logger.error("Encryption retry operation failed after {} attempts", 
                    context.getRetryCount(), throwable);
            } else {
                logger.info("Encryption retry operation succeeded after {} attempts", 
                    context.getRetryCount());
            }
        }
    }
    
    /**
     * 重试配置属性
     */
    @ConfigurationProperties(prefix = "app.retry")
    public static class RetryProperties {
        
        private Mqtt mqtt = new Mqtt();
        private Encryption encryption = new Encryption();
        
        public Mqtt getMqtt() { return mqtt; }
        public void setMqtt(Mqtt mqtt) { this.mqtt = mqtt; }
        
        public Encryption getEncryption() { return encryption; }
        public void setEncryption(Encryption encryption) { this.encryption = encryption; }
        
        public static class Mqtt {
            private int maxAttempts = 3;
            private long initialInterval = 1000;
            private double multiplier = 2.0;
            private long maxInterval = 10000;
            
            public int getMaxAttempts() { return maxAttempts; }
            public void setMaxAttempts(int maxAttempts) { this.maxAttempts = maxAttempts; }
            
            public long getInitialInterval() { return initialInterval; }
            public void setInitialInterval(long initialInterval) { this.initialInterval = initialInterval; }
            
            public double getMultiplier() { return multiplier; }
            public void setMultiplier(double multiplier) { this.multiplier = multiplier; }
            
            public long getMaxInterval() { return maxInterval; }
            public void setMaxInterval(long maxInterval) { this.maxInterval = maxInterval; }
        }
        
        public static class Encryption {
            private int maxAttempts = 2;
            private long backOffPeriod = 500;
            
            public int getMaxAttempts() { return maxAttempts; }
            public void setMaxAttempts(int maxAttempts) { this.maxAttempts = maxAttempts; }
            
            public long getBackOffPeriod() { return backOffPeriod; }
            public void setBackOffPeriod(long backOffPeriod) { this.backOffPeriod = backOffPeriod; }
        }
    }
}