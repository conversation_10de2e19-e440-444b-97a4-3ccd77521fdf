package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;
import reactor.core.publisher.Mono;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * WebSocket 会话管理器
 * 负责管理 WebSocket 连接的生命周期和消息发送
 * 
 * <AUTHOR>
 */
@Service
public class WebSocketSessionManager {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketSessionManager.class);
    
    // 会话ID -> WebSocket会话映射
    private final ConcurrentMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    // 用户ID -> 会话ID集合映射（一个用户可能有多个会话）
    private final ConcurrentMap<String, Set<String>> userSessions = new ConcurrentHashMap<>();
    
    private final MessageConverter messageConverter;
    
    public WebSocketSessionManager(MessageConverter messageConverter) {
        this.messageConverter = messageConverter;
    }
    
    /**
     * 注册新的 WebSocket 会话
     * 
     * @param session WebSocket 会话
     * @param userId 用户ID（可选）
     */
    public void registerSession(WebSocketSession session, String userId) {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        
        if (userId != null && !userId.isEmpty()) {
            userSessions.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet()).add(sessionId);
            logger.info("Registered WebSocket session: sessionId={}, userId={}", sessionId, userId);
        } else {
            logger.info("Registered WebSocket session: sessionId={}", sessionId);
        }
    }
    
    /**
     * 注销 WebSocket 会话
     * 
     * @param sessionId 会话ID
     */
    public void unregisterSession(String sessionId) {
        WebSocketSession session = sessions.remove(sessionId);
        if (session != null) {
            // 从用户会话映射中移除
            userSessions.values().forEach(sessionSet -> sessionSet.remove(sessionId));
            
            // 清理空的用户映射
            userSessions.entrySet().removeIf(entry -> entry.getValue().isEmpty());
            
            logger.info("Unregistered WebSocket session: sessionId={}", sessionId);
        }
    }
    
    /**
     * 向指定会话发送消息
     * 
     * @param sessionId 会话ID
     * @param message WebSocket 消息
     * @return 发送结果
     */
    public Mono<Void> sendToSession(String sessionId, WebSocketMessage message) {
        return Mono.fromRunnable(() -> {
            WebSocketSession session = sessions.get(sessionId);
            if (session != null && session.isOpen()) {
                try {
                    String messageData = messageConverter.convertToJson(message);
                    session.sendMessage(new org.springframework.web.socket.TextMessage(messageData));
                    logger.debug("Sent message to session: sessionId={}, messageId={}", 
                               sessionId, message.getMessage().getId());
                } catch (Exception e) {
                    logger.error("Failed to send message to session: sessionId={}, error={}", 
                               sessionId, e.getMessage());
                    throw new RuntimeException("Failed to send message", e);
                }
            } else {
                logger.warn("Session not found or closed: sessionId={}", sessionId);
            }
        });
    }
    
    /**
     * 向指定用户的所有会话发送消息
     * 
     * @param userId 用户ID
     * @param message WebSocket 消息
     * @return 发送结果
     */
    public Mono<Void> sendToUser(String userId, WebSocketMessage message) {
        return Mono.fromRunnable(() -> {
            Set<String> sessionIds = userSessions.get(userId);
            if (sessionIds != null && !sessionIds.isEmpty()) {
                int sentCount = 0;
                for (String sessionId : sessionIds) {
                    WebSocketSession session = sessions.get(sessionId);
                    if (session != null && session.isOpen()) {
                        try {
                            String messageData = messageConverter.convertToJson(message);
                            session.sendMessage(new org.springframework.web.socket.TextMessage(messageData));
                            sentCount++;
                        } catch (Exception e) {
                            logger.error("Failed to send message to user session: userId={}, sessionId={}, error={}", 
                                       userId, sessionId, e.getMessage());
                        }
                    }
                }
                logger.debug("Sent message to user: userId={}, sessionCount={}, messageId={}", 
                           userId, sentCount, message.getMessage().getId());
            } else {
                logger.warn("No active sessions found for user: userId={}", userId);
            }
        });
    }
    
    /**
     * 广播消息到所有活跃会话
     * 
     * @param message WebSocket 消息
     * @return 发送结果
     */
    public Mono<Void> broadcastMessage(WebSocketMessage message) {
        return Mono.fromRunnable(() -> {
            String messageData = messageConverter.convertToJson(message);
            int sentCount = 0;
            
            for (WebSocketSession session : sessions.values()) {
                if (session.isOpen()) {
                    try {
                        session.sendMessage(new org.springframework.web.socket.TextMessage(messageData));
                        sentCount++;
                    } catch (Exception e) {
                        logger.error("Failed to broadcast message to session: sessionId={}, error={}", 
                                   session.getId(), e.getMessage());
                    }
                }
            }
            
            logger.debug("Broadcasted message: sessionCount={}, messageId={}", 
                       sentCount, message.getMessage().getId());
        });
    }
    
    /**
     * 获取活跃会话数量
     * 
     * @return 活跃会话数量
     */
    public int getActiveSessionCount() {
        return (int) sessions.values().stream()
                .filter(WebSocketSession::isOpen)
                .count();
    }
    
    /**
     * 获取指定用户的活跃会话数量
     * 
     * @param userId 用户ID
     * @return 活跃会话数量
     */
    public int getUserActiveSessionCount(String userId) {
        Set<String> sessionIds = userSessions.get(userId);
        if (sessionIds == null) {
            return 0;
        }
        
        return (int) sessionIds.stream()
                .map(sessions::get)
                .filter(session -> session != null && session.isOpen())
                .count();
    }
    
    /**
     * 获取所有活跃用户ID
     * 
     * @return 活跃用户ID集合
     */
    public Set<String> getActiveUserIds() {
        return userSessions.entrySet().stream()
                .filter(entry -> entry.getValue().stream()
                        .anyMatch(sessionId -> {
                            WebSocketSession session = sessions.get(sessionId);
                            return session != null && session.isOpen();
                        }))
                .map(ConcurrentMap.Entry::getKey)
                .collect(Collectors.toSet());
    }
    
    /**
     * 清理已关闭的会话
     */
    public void cleanupClosedSessions() {
        sessions.entrySet().removeIf(entry -> !entry.getValue().isOpen());
        userSessions.values().forEach(sessionSet -> 
            sessionSet.removeIf(sessionId -> !sessions.containsKey(sessionId)));
        userSessions.entrySet().removeIf(entry -> entry.getValue().isEmpty());
        
        logger.debug("Cleaned up closed sessions. Active sessions: {}", sessions.size());
    }
    
    /**
     * 检查会话是否存在且活跃
     * 
     * @param sessionId 会话ID
     * @return 是否活跃
     */
    public boolean isSessionActive(String sessionId) {
        WebSocketSession session = sessions.get(sessionId);
        return session != null && session.isOpen();
    }
    
    /**
     * 获取会话关联的用户ID
     * 
     * @param sessionId 会话ID
     * @return 用户ID，如果未找到返回null
     */
    public String getUserIdBySession(String sessionId) {
        return userSessions.entrySet().stream()
                .filter(entry -> entry.getValue().contains(sessionId))
                .map(ConcurrentMap.Entry::getKey)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 移除指定会话
     * 
     * @param sessionId 会话ID
     */
    public void removeSession(String sessionId) {
        unregisterSession(sessionId);
    }
    
    /**
     * 向指定会话发送系统消息
     * 
     * @param sessionId 会话ID
     * @param message 系统消息内容
     */
    public void sendSystemMessage(String sessionId, String message) {
        WebSocketSession session = sessions.get(sessionId);
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new org.springframework.web.socket.TextMessage(message));
                logger.debug("Sent system message to session: sessionId={}, message={}", sessionId, message);
            } catch (Exception e) {
                logger.error("Failed to send system message to session: sessionId={}, error={}", 
                           sessionId, e.getMessage());
            }
        } else {
            logger.warn("Cannot send system message - session not found or closed: sessionId={}", sessionId);
        }
    }
}