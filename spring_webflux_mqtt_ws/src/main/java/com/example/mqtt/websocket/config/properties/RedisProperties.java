package com.example.mqtt.websocket.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.Duration;
import java.util.List;

/**
 * Redis 集群配置属性
 */
@ConfigurationProperties(prefix = "app.redis")
@Validated
public record RedisProperties(
    /**
     * Redis 主机地址
     */
    @NotBlank
    String host,
    
    /**
     * Redis 端口
     */
    @Positive
    int port,
    
    /**
     * Redis 密码
     */
    String password,
    
    /**
     * 数据库索引
     */
    int database,
    
    /**
     * 连接超时时间
     */
    @NotNull
    Duration connectionTimeout,
    
    /**
     * 读取超时时间
     */
    @NotNull
    Duration readTimeout,
    
    /**
     * 集群节点列表
     */
    List<String> clusterNodes,
    
    /**
     * 连接池配置
     */
    @NotNull
    Pool pool,
    
    /**
     * 集群状态管理配置
     */
    @NotNull
    Cluster cluster
) {
    
    public RedisProperties() {
        this("localhost", 6379, null, 0, 
             Duration.ofSeconds(5), Duration.ofSeconds(3),
             List.of(), new Pool(), new Cluster());
    }
    
    /**
     * 连接池配置
     */
    public record Pool(
        /**
         * 最大连接数
         */
        @Positive
        int maxActive,
        
        /**
         * 最大空闲连接数
         */
        @Positive
        int maxIdle,
        
        /**
         * 最小空闲连接数
         */
        int minIdle,
        
        /**
         * 获取连接时的最大等待时间
         */
        @NotNull
        Duration maxWait
    ) {
        public Pool() {
            this(8, 8, 0, Duration.ofMillis(-1));
        }
    }
    
    /**
     * 集群管理配置
     */
    public record Cluster(
        /**
         * 实例注册键前缀
         */
        @NotBlank
        String instanceKeyPrefix,
        
        /**
         * 实例心跳间隔
         */
        @NotNull
        Duration heartbeatInterval,
        
        /**
         * 实例过期时间
         */
        @NotNull
        Duration instanceExpiration,
        
        /**
         * 集群消息通道
         */
        @NotBlank
        String messageChannel,
        
        /**
         * 状态同步间隔
         */
        @NotNull
        Duration stateSyncInterval
    ) {
        public Cluster() {
            this("cluster:instance:", Duration.ofSeconds(30), 
                 Duration.ofMinutes(2), "cluster:messages",
                 Duration.ofSeconds(10));
        }
    }
}