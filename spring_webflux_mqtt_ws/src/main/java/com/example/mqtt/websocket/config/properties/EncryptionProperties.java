package com.example.mqtt.websocket.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 加密配置属性
 */
@Component
@ConfigurationProperties(prefix = "app.encryption")
public class EncryptionProperties {
    
    private String algorithm = "AES";
    private int keyLength = 256;
    private String transformation = "AES/GCM/NoPadding";
    private String masterKey;
    private long keyRotationInterval = 86400000L; // 24 hours in milliseconds

    // Getters and Setters
    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public int getKeyLength() {
        return keyLength;
    }

    public void setKeyLength(int keyLength) {
        this.keyLength = keyLength;
    }

    public String getTransformation() {
        return transformation;
    }

    public void setTransformation(String transformation) {
        this.transformation = transformation;
    }

    public String getMasterKey() {
        return masterKey;
    }

    public void setMasterKey(String masterKey) {
        this.masterKey = masterKey;
    }

    public long getKeyRotationInterval() {
        return keyRotationInterval;
    }

    public void setKeyRotationInterval(long keyRotationInterval) {
        this.keyRotationInterval = keyRotationInterval;
    }
}