package com.example.mqtt.websocket.controller;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.MqttMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import com.example.mqtt.websocket.service.MessageBridgeService;
import com.example.mqtt.websocket.service.MessageConverter;
import com.example.mqtt.websocket.service.MqttPublishService;
import com.example.mqtt.websocket.service.WebSocketSessionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;

/**
 * 消息处理 REST 控制器
 * 提供 HTTP API 接口用于消息发送、接收和管理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/messages")
@Validated
public class MessageController {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageController.class);
    
    private final MessageBridgeService messageBridgeService;
    private final MessageConverter messageConverter;
    private final MqttPublishService mqttPublishService;
    private final WebSocketSessionManager sessionManager;
    
    @Autowired
    public MessageController(
            MessageBridgeService messageBridgeService,
            MessageConverter messageConverter,
            MqttPublishService mqttPublishService,
            WebSocketSessionManager sessionManager) {
        this.messageBridgeService = messageBridgeService;
        this.messageConverter = messageConverter;
        this.mqttPublishService = mqttPublishService;
        this.sessionManager = sessionManager;
    }
    
    /**
     * 发送消息到 MQTT
     * 
     * @param request 消息发送请求
     * @return 发送结果
     */
    @PostMapping("/mqtt/send")
    public Mono<ResponseEntity<MessageResponse>> sendToMqtt(
            @Valid @RequestBody MqttSendRequest request) {
        
        logger.info("Received MQTT send request: topic={}, messageType={}", 
                   request.getTopic(), request.getMessageType());
        
        return Mono.fromCallable(() -> {
            // 创建 BaseMessage
            BaseMessage baseMessage = BaseMessage.newBuilder()
                    .setId(UUID.randomUUID().toString())
                    .setTimestamp(System.currentTimeMillis())
                    .setType(request.getMessageType())
                    .setSource("http-api")
                    .setPayload(com.google.protobuf.ByteString.copyFromUtf8(request.getPayload()))
                    .putAllHeaders(request.getHeaders())
                    .build();
            
            // 创建 MqttMessage
            MqttMessage mqttMessage = MqttMessage.newBuilder()
                    .setTopic(request.getTopic())
                    .setQos(request.getQos())
                    .setRetained(request.isRetained())
                    .setMessage(baseMessage)
                    .build();
            
            return mqttMessage;
        })
        .flatMap(mqttPublishService::publishMessage)
        .map(v -> ResponseEntity.ok(new MessageResponse("Message sent successfully", true)))
        .onErrorResume(error -> {
            logger.error("Failed to send MQTT message: error={}", error.getMessage());
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new MessageResponse("Failed to send message: " + error.getMessage(), false)));
        });
    }
    
    /**
     * 发送消息到 WebSocket
     * 
     * @param request WebSocket 消息发送请求
     * @return 发送结果
     */
    @PostMapping("/websocket/send")
    public Mono<ResponseEntity<MessageResponse>> sendToWebSocket(
            @Valid @RequestBody WebSocketSendRequest request) {
        
        logger.info("Received WebSocket send request: type={}, targetUserId={}", 
                   request.getMessageType(), request.getTargetUserId());
        
        return Mono.fromCallable(() -> {
            // 创建 BaseMessage
            BaseMessage baseMessage = BaseMessage.newBuilder()
                    .setId(UUID.randomUUID().toString())
                    .setTimestamp(System.currentTimeMillis())
                    .setType(request.getMessageType())
                    .setSource("http-api")
                    .setPayload(com.google.protobuf.ByteString.copyFromUtf8(request.getPayload()))
                    .putAllHeaders(request.getHeaders())
                    .build();
            
            // 创建 WebSocketMessage
            WebSocketMessage.Builder wsBuilder = WebSocketMessage.newBuilder()
                    .setMessage(baseMessage);
            
            if (request.getTargetUserId() != null && !request.getTargetUserId().isEmpty()) {
                wsBuilder.setUserId(request.getTargetUserId())
                        .setType(WebSocketMessage.MessageType.UNICAST);
            } else {
                wsBuilder.setType(WebSocketMessage.MessageType.BROADCAST);
            }
            
            return wsBuilder.build();
        })
        .flatMap(wsMessage -> {
            if (wsMessage.getType() == WebSocketMessage.MessageType.UNICAST) {
                return sessionManager.sendToUser(wsMessage.getUserId(), wsMessage);
            } else {
                return sessionManager.broadcastMessage(wsMessage);
            }
        })
        .map(v -> ResponseEntity.ok(new MessageResponse("Message sent successfully", true)))
        .onErrorResume(error -> {
            logger.error("Failed to send WebSocket message: error={}", error.getMessage());
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new MessageResponse("Failed to send message: " + error.getMessage(), false)));
        });
    }
    
    /**
     * 获取消息流 - 支持 Server-Sent Events
     * 
     * @param messageType 消息类型过滤器
     * @param source 消息源过滤器
     * @return 消息流
     */
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<BaseMessage> getMessageStream(
            @RequestParam(required = false) String messageType,
            @RequestParam(required = false) String source) {
        
        logger.info("Starting message stream: messageType={}, source={}", messageType, source);
        
        // 创建模拟消息流（实际实现中应该从消息队列或事件总线获取）
        return Flux.interval(Duration.ofSeconds(5))
                .map(i -> BaseMessage.newBuilder()
                        .setId(UUID.randomUUID().toString())
                        .setTimestamp(System.currentTimeMillis())
                        .setType(messageType != null ? messageType : "stream_message")
                        .setSource(source != null ? source : "stream")
                        .setPayload(com.google.protobuf.ByteString.copyFromUtf8("Stream message " + i))
                        .build())
                .doOnSubscribe(subscription -> logger.info("Client subscribed to message stream"))
                .doOnCancel(() -> logger.info("Client cancelled message stream subscription"))
                .doOnError(error -> logger.error("Error in message stream: {}", error.getMessage()));
    }
    
    /**
     * 获取消息历史记录
     * 
     * @param messageType 消息类型
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 消息历史
     */
    @GetMapping("/history")
    public Flux<BaseMessage> getMessageHistory(
            @RequestParam(required = false) String messageType,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(defaultValue = "0") int offset) {
        
        logger.info("Fetching message history: type={}, limit={}, offset={}", 
                   messageType, limit, offset);
        
        // 模拟历史消息数据（实际实现中应该从数据库或缓存获取）
        return Flux.range(offset, limit)
                .map(i -> BaseMessage.newBuilder()
                        .setId("history-" + i)
                        .setTimestamp(System.currentTimeMillis() - (i * 60000))
                        .setType(messageType != null ? messageType : "historical")
                        .setSource("history")
                        .setPayload(com.google.protobuf.ByteString.copyFromUtf8("Historical message " + i))
                        .build())
                .doOnComplete(() -> logger.info("Completed message history fetch"));
    }
    
    /**
     * 获取消息统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/stats")
    public Mono<ResponseEntity<MessageStats>> getMessageStats() {
        logger.info("Fetching message statistics");
        
        return Mono.fromCallable(() -> {
            // 模拟统计数据（实际实现中应该从监控系统获取）
            MessageStats stats = new MessageStats();
            stats.setTotalMessages(1000L);
            stats.setMqttMessages(600L);
            stats.setWebSocketMessages(400L);
            stats.setActiveConnections(sessionManager.getActiveSessionCount());
            stats.setErrorCount(5L);
            
            return ResponseEntity.ok(stats);
        })
        .doOnSuccess(stats -> logger.info("Message statistics retrieved successfully"))
        .onErrorResume(error -> {
            logger.error("Failed to get message statistics: error={}", error.getMessage());
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
        });
    }
    
    /**
     * 健康检查端点
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Mono<ResponseEntity<Map<String, Object>>> healthCheck() {
        return Mono.fromCallable(() -> {
            Map<String, Object> health = Map.of(
                "status", "UP",
                "timestamp", System.currentTimeMillis(),
                "activeWebSocketSessions", sessionManager.getActiveSessionCount(),
                "mqttConnected", true // 实际实现中应该检查 MQTT 连接状态
            );
            return ResponseEntity.ok(health);
        });
    }
    
    // 请求和响应 DTO 类
    public static class MqttSendRequest {
        @NotBlank(message = "Topic is required")
        private String topic;
        
        @NotBlank(message = "Message type is required")
        private String messageType;
        
        @NotBlank(message = "Payload is required")
        private String payload;
        
        private int qos = 1;
        private boolean retained = false;
        private Map<String, String> headers = Map.of();
        
        // Getters and setters
        public String getTopic() { return topic; }
        public void setTopic(String topic) { this.topic = topic; }
        
        public String getMessageType() { return messageType; }
        public void setMessageType(String messageType) { this.messageType = messageType; }
        
        public String getPayload() { return payload; }
        public void setPayload(String payload) { this.payload = payload; }
        
        public int getQos() { return qos; }
        public void setQos(int qos) { this.qos = qos; }
        
        public boolean isRetained() { return retained; }
        public void setRetained(boolean retained) { this.retained = retained; }
        
        public Map<String, String> getHeaders() { return headers; }
        public void setHeaders(Map<String, String> headers) { this.headers = headers; }
    }
    
    public static class WebSocketSendRequest {
        @NotBlank(message = "Message type is required")
        private String messageType;
        
        @NotBlank(message = "Payload is required")
        private String payload;
        
        private String targetUserId;
        private Map<String, String> headers = Map.of();
        
        // Getters and setters
        public String getMessageType() { return messageType; }
        public void setMessageType(String messageType) { this.messageType = messageType; }
        
        public String getPayload() { return payload; }
        public void setPayload(String payload) { this.payload = payload; }
        
        public String getTargetUserId() { return targetUserId; }
        public void setTargetUserId(String targetUserId) { this.targetUserId = targetUserId; }
        
        public Map<String, String> getHeaders() { return headers; }
        public void setHeaders(Map<String, String> headers) { this.headers = headers; }
    }
    
    public static class MessageResponse {
        private String message;
        private boolean success;
        
        public MessageResponse(String message, boolean success) {
            this.message = message;
            this.success = success;
        }
        
        // Getters and setters
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
    }
    
    public static class MessageStats {
        private Long totalMessages;
        private Long mqttMessages;
        private Long webSocketMessages;
        private Integer activeConnections;
        private Long errorCount;
        
        // Getters and setters
        public Long getTotalMessages() { return totalMessages; }
        public void setTotalMessages(Long totalMessages) { this.totalMessages = totalMessages; }
        
        public Long getMqttMessages() { return mqttMessages; }
        public void setMqttMessages(Long mqttMessages) { this.mqttMessages = mqttMessages; }
        
        public Long getWebSocketMessages() { return webSocketMessages; }
        public void setWebSocketMessages(Long webSocketMessages) { this.webSocketMessages = webSocketMessages; }
        
        public Integer getActiveConnections() { return activeConnections; }
        public void setActiveConnections(Integer activeConnections) { this.activeConnections = activeConnections; }
        
        public Long getErrorCount() { return errorCount; }
        public void setErrorCount(Long errorCount) { this.errorCount = errorCount; }
    }
}