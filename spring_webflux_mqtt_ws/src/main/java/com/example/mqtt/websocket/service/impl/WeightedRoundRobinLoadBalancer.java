package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 加权轮询负载均衡器实现
 */
@Service
@ConditionalOnProperty(name = "app.loadbalancer.strategy", havingValue = "weighted_round_robin")
public class WeightedRoundRobinLoadBalancer implements LoadBalancer {

    private static final Logger logger = LoggerFactory.getLogger(WeightedRoundRobinLoadBalancer.class);

    private final ClusterStateManager clusterStateManager;
    private final Map<String, Boolean> instanceHealthMap = new ConcurrentHashMap<>();
    private final Map<String, Integer> instanceWeightMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> requestCountMap = new ConcurrentHashMap<>();
    private final Map<String, Integer> currentWeightMap = new ConcurrentHashMap<>();
    private final AtomicLong totalRequests = new AtomicLong(0);

    public WeightedRoundRobinLoadBalancer(ClusterStateManager clusterStateManager) {
        this.clusterStateManager = clusterStateManager;
    }

    @Override
    public Mono<String> selectInstance(String key) {
        return selectInstance();
    }

    @Override
    public Mono<String> selectInstance() {
        return getHealthyInstances()
            .flatMap(instances -> {
                if (instances.isEmpty()) {
                    logger.warn("No healthy instances available for weighted load balancing");
                    return Mono.empty();
                }

                String selectedInstance = selectByWeight(instances);
                if (selectedInstance != null) {
                    // 更新统计信息
                    requestCountMap.computeIfAbsent(selectedInstance, k -> new AtomicLong(0)).incrementAndGet();
                    totalRequests.incrementAndGet();
                    
                    logger.debug("Selected instance: {} using weighted round-robin", selectedInstance);
                    return Mono.just(selectedInstance);
                }
                
                return Mono.empty();
            })
            .doOnError(error -> logger.error("Error selecting instance with weighted round-robin", error));
    }

    /**
     * 使用加权轮询算法选择实例
     */
    private String selectByWeight(List<String> instances) {
        if (instances.size() == 1) {
            return instances.get(0);
        }

        String selectedInstance = null;
        int maxCurrentWeight = Integer.MIN_VALUE;

        // 计算总权重
        int totalWeight = 0;
        for (String instanceId : instances) {
            int weight = instanceWeightMap.getOrDefault(instanceId, 1);
            totalWeight += weight;
            
            // 增加当前权重
            int currentWeight = currentWeightMap.getOrDefault(instanceId, 0) + weight;
            currentWeightMap.put(instanceId, currentWeight);
            
            // 找到当前权重最大的实例
            if (currentWeight > maxCurrentWeight) {
                maxCurrentWeight = currentWeight;
                selectedInstance = instanceId;
            }
        }

        // 减少选中实例的当前权重
        if (selectedInstance != null) {
            int currentWeight = currentWeightMap.get(selectedInstance);
            currentWeightMap.put(selectedInstance, currentWeight - totalWeight);
        }

        return selectedInstance;
    }

    @Override
    public Mono<Void> updateInstanceHealth(String instanceId, boolean healthy) {
        return Mono.fromRunnable(() -> {
            boolean previousHealth = instanceHealthMap.getOrDefault(instanceId, true);
            instanceHealthMap.put(instanceId, healthy);
            
            // 如果实例变为不健康，重置其当前权重
            if (!healthy) {
                currentWeightMap.put(instanceId, 0);
            }
            
            if (previousHealth != healthy) {
                logger.info("Instance {} health status changed: {} -> {}", 
                    instanceId, previousHealth, healthy);
            }
        });
    }

    @Override
    public Mono<List<String>> getHealthyInstances() {
        return clusterStateManager.getActiveInstances()
            .map(activeInstances -> {
                List<String> healthyInstances = new ArrayList<>();
                for (String instanceId : activeInstances) {
                    if (instanceHealthMap.getOrDefault(instanceId, true)) {
                        healthyInstances.add(instanceId);
                    }
                }
                return healthyInstances;
            })
            .doOnNext(instances -> logger.debug("Found {} healthy instances for weighted balancing: {}", 
                instances.size(), instances));
    }

    @Override
    public Mono<Integer> getInstanceWeight(String instanceId) {
        return Mono.just(instanceWeightMap.getOrDefault(instanceId, 1));
    }

    @Override
    public Mono<Void> setInstanceWeight(String instanceId, int weight) {
        return Mono.fromRunnable(() -> {
            if (weight <= 0) {
                throw new IllegalArgumentException("Instance weight must be positive");
            }
            instanceWeightMap.put(instanceId, weight);
            // 重置当前权重以立即生效
            currentWeightMap.put(instanceId, 0);
            logger.info("Set weight for instance {}: {}", instanceId, weight);
        });
    }

    @Override
    public Mono<LoadBalancerStats> getStats() {
        return Mono.fromCallable(() -> {
            Map<String, Long> requestCounts = new HashMap<>();
            requestCountMap.forEach((instanceId, count) -> 
                requestCounts.put(instanceId, count.get()));

            return new LoadBalancerStats(
                requestCounts,
                new HashMap<>(instanceHealthMap),
                new HashMap<>(instanceWeightMap),
                totalRequests.get(),
                Strategy.WEIGHTED_ROUND_ROBIN
            );
        });
    }

    /**
     * 重置统计信息和权重状态
     */
    public void resetStats() {
        requestCountMap.clear();
        currentWeightMap.clear();
        totalRequests.set(0);
        logger.info("Weighted load balancer statistics and weights reset");
    }
}