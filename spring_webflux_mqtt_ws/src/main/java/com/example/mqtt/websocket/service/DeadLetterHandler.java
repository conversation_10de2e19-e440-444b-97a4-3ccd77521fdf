package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.exception.MessageProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 死信队列处理器
 * 处理消息处理失败的情况，提供消息存储和重处理机制
 */
@Component
public class DeadLetterHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(DeadLetterHandler.class);
    private static final String DEAD_LETTER_QUEUE_KEY = "dead_letter_queue";
    private static final String DEAD_LETTER_STATS_KEY = "dead_letter_stats";
    
    @Autowired
    private ReactiveRedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    private Counter deadLetterCounter;
    private final AtomicLong totalDeadLetters = new AtomicLong(0);
    
    @PostConstruct
    public void initMetrics() {
        deadLetterCounter = Counter.builder("dead.letter.messages.total")
            .description("Total number of dead letter messages")
            .register(meterRegistry);
    }
    
    /**
     * 处理死信消息
     * 当消息处理失败时，Spring Integration 会将消息发送到错误通道
     */
    @ServiceActivator(inputChannel = "errorChannel")
    public void handleDeadLetter(Message<?> failedMessage) {
        try {
            logger.error("Processing dead letter message: {}", failedMessage);
            
            // 更新统计
            deadLetterCounter.increment();
            totalDeadLetters.incrementAndGet();
            
            // 创建死信记录
            DeadLetterRecord record = createDeadLetterRecord(failedMessage);
            
            // 存储到死信队列
            storeDeadLetterMessage(record)
                .doOnSuccess(result -> {
                    logger.info("Dead letter message stored successfully: {}", record.getId());
                })
                .doOnError(error -> {
                    logger.error("Failed to store dead letter message: {}", record.getId(), error);
                })
                .subscribe();
            
            // 发送告警通知
            sendDeadLetterAlert(record);
            
            // 记录失败消息详情
            logFailedMessageDetails(failedMessage, record);
            
        } catch (Exception e) {
            logger.error("Error handling dead letter message", e);
        }
    }
    
    /**
     * 创建死信记录
     */
    private DeadLetterRecord createDeadLetterRecord(Message<?> failedMessage) {
        DeadLetterRecord record = new DeadLetterRecord();
        record.setId(generateDeadLetterId());
        record.setTimestamp(LocalDateTime.now());
        record.setPayload(failedMessage.getPayload());
        record.setHeaders(extractHeaders(failedMessage.getHeaders()));
        
        // 提取错误信息
        Throwable exception = (Throwable) failedMessage.getHeaders().get("errorCause");
        if (exception != null) {
            record.setErrorMessage(exception.getMessage());
            record.setErrorType(exception.getClass().getSimpleName());
            record.setStackTrace(getStackTrace(exception));
        }
        
        // 提取消息类型和ID
        if (failedMessage.getPayload() != null) {
            record.setMessageType(failedMessage.getPayload().getClass().getSimpleName());
        }
        
        Object messageId = failedMessage.getHeaders().get("messageId");
        if (messageId != null) {
            record.setOriginalMessageId(messageId.toString());
        }
        
        return record;
    }
    
    /**
     * 存储死信消息到 Redis
     */
    private Mono<Boolean> storeDeadLetterMessage(DeadLetterRecord record) {
        try {
            String recordJson = objectMapper.writeValueAsString(record);
            
            return redisTemplate.opsForList()
                .leftPush(DEAD_LETTER_QUEUE_KEY, recordJson)
                .then(updateDeadLetterStats(record))
                .then(Mono.just(true));
                
        } catch (Exception e) {
            logger.error("Failed to serialize dead letter record", e);
            return Mono.just(false);
        }
    }
    
    /**
     * 更新死信统计信息
     */
    private Mono<Void> updateDeadLetterStats(DeadLetterRecord record) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("lastDeadLetterTime", record.getTimestamp().toString());
        stats.put("totalCount", totalDeadLetters.get());
        stats.put("lastErrorType", record.getErrorType());
        stats.put("lastMessageType", record.getMessageType());
        
        return redisTemplate.opsForHash()
            .putAll(DEAD_LETTER_STATS_KEY, stats)
            .then();
    }
    
    /**
     * 获取死信队列中的消息
     */
    public Mono<DeadLetterRecord> getNextDeadLetterMessage() {
        return redisTemplate.opsForList()
            .rightPop(DEAD_LETTER_QUEUE_KEY)
            .cast(String.class)
            .flatMap(this::deserializeDeadLetterRecord);
    }
    
    /**
     * 获取死信队列长度
     */
    public Mono<Long> getDeadLetterQueueSize() {
        return redisTemplate.opsForList().size(DEAD_LETTER_QUEUE_KEY);
    }
    
    /**
     * 手动重处理死信消息
     */
    public Mono<Boolean> reprocessDeadLetterMessage(String deadLetterId) {
        // 这里可以实现重处理逻辑
        // 1. 从死信队列中找到对应的消息
        // 2. 重新发送到原始处理通道
        // 3. 记录重处理结果
        
        logger.info("Reprocessing dead letter message: {}", deadLetterId);
        
        return getDeadLetterMessageById(deadLetterId)
            .flatMap(record -> {
                try {
                    // 重新构造消息并发送到处理通道
                    // 这里需要根据实际的消息处理流程来实现
                    logger.info("Reprocessing message: {}", record.getId());
                    return Mono.just(true);
                } catch (Exception e) {
                    logger.error("Failed to reprocess dead letter message: {}", deadLetterId, e);
                    return Mono.just(false);
                }
            })
            .defaultIfEmpty(false);
    }
    
    /**
     * 根据ID获取死信消息
     */
    private Mono<DeadLetterRecord> getDeadLetterMessageById(String deadLetterId) {
        // 这里需要实现根据ID查找死信消息的逻辑
        // 可以使用 Redis 的 SCAN 命令或者维护一个ID到消息的映射
        return Mono.empty(); // 简化实现
    }
    
    /**
     * 反序列化死信记录
     */
    private Mono<DeadLetterRecord> deserializeDeadLetterRecord(String recordJson) {
        try {
            DeadLetterRecord record = objectMapper.readValue(recordJson, DeadLetterRecord.class);
            return Mono.just(record);
        } catch (Exception e) {
            logger.error("Failed to deserialize dead letter record", e);
            return Mono.empty();
        }
    }
    
    /**
     * 发送死信告警
     */
    private void sendDeadLetterAlert(DeadLetterRecord record) {
        logger.warn("DEAD_LETTER_ALERT - Message ID: {}, Type: {}, Error: {}", 
            record.getId(), record.getMessageType(), record.getErrorMessage());
        
        // 这里可以集成实际的告警系统
        meterRegistry.counter("alerts.dead.letter.total").increment();
    }
    
    /**
     * 记录失败消息详情
     */
    private void logFailedMessageDetails(Message<?> failedMessage, DeadLetterRecord record) {
        logger.error("Failed message details - ID: {}, Payload: {}, Headers: {}, Error: {}", 
            record.getId(), 
            failedMessage.getPayload(), 
            failedMessage.getHeaders(), 
            record.getErrorMessage());
    }
    
    /**
     * 提取消息头信息
     */
    private Map<String, Object> extractHeaders(MessageHeaders headers) {
        Map<String, Object> headerMap = new HashMap<>();
        headers.forEach((key, value) -> {
            if (value != null && !(value instanceof Throwable)) {
                headerMap.put(key, value.toString());
            }
        });
        return headerMap;
    }
    
    /**
     * 获取异常堆栈跟踪
     */
    private String getStackTrace(Throwable throwable) {
        StringBuilder sb = new StringBuilder();
        sb.append(throwable.toString()).append("\n");
        for (StackTraceElement element : throwable.getStackTrace()) {
            sb.append("\tat ").append(element.toString()).append("\n");
        }
        return sb.toString();
    }
    
    /**
     * 生成死信ID
     */
    private String generateDeadLetterId() {
        return "DL_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
    
    /**
     * 死信记录类
     */
    public static class DeadLetterRecord {
        private String id;
        private LocalDateTime timestamp;
        private Object payload;
        private Map<String, Object> headers;
        private String errorMessage;
        private String errorType;
        private String stackTrace;
        private String messageType;
        private String originalMessageId;
        
        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        
        public Object getPayload() { return payload; }
        public void setPayload(Object payload) { this.payload = payload; }
        
        public Map<String, Object> getHeaders() { return headers; }
        public void setHeaders(Map<String, Object> headers) { this.headers = headers; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public String getErrorType() { return errorType; }
        public void setErrorType(String errorType) { this.errorType = errorType; }
        
        public String getStackTrace() { return stackTrace; }
        public void setStackTrace(String stackTrace) { this.stackTrace = stackTrace; }
        
        public String getMessageType() { return messageType; }
        public void setMessageType(String messageType) { this.messageType = messageType; }
        
        public String getOriginalMessageId() { return originalMessageId; }
        public void setOriginalMessageId(String originalMessageId) { this.originalMessageId = originalMessageId; }
    }
}