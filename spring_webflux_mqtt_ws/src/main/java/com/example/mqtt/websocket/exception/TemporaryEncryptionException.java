package com.example.mqtt.websocket.exception;

import com.example.mqtt.websocket.service.EncryptionException;

/**
 * 临时加密异常 - 可重试的加密异常
 */
public class TemporaryEncryptionException extends EncryptionException {
    
    public TemporaryEncryptionException(String message) {
        super(message);
    }
    
    public TemporaryEncryptionException(String message, Throwable cause) {
        super(message, cause);
    }
}