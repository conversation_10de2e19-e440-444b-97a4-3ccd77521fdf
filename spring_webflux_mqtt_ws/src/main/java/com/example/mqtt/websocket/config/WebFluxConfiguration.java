package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.http.codec.protobuf.ProtobufDecoder;
import org.springframework.http.codec.protobuf.ProtobufEncoder;
import org.springframework.web.reactive.config.CorsRegistry;
import org.springframework.web.reactive.config.EnableWebFlux;
import org.springframework.web.reactive.config.WebFluxConfigurer;

/**
 * WebFlux 配置类
 * 配置响应式 Web 框架的编解码器、CORS 等设置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebFlux
public class WebFluxConfiguration implements WebFluxConfigurer {
    
    /**
     * 配置 CORS 跨域设置
     * 
     * @param registry CORS 注册器
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
    
    /**
     * 配置编解码器
     * 
     * @param configurer 编解码器配置器
     */
    @Override
    public void configureHttpMessageCodecs(ServerCodecConfigurer configurer) {
        // 配置 Protobuf 编解码器
        configurer.defaultCodecs().protobufDecoder(new ProtobufDecoder());
        configurer.defaultCodecs().protobufEncoder(new ProtobufEncoder());
        
        // 配置 Jackson JSON 编解码器
        ObjectMapper objectMapper = createObjectMapper();
        configurer.defaultCodecs().jackson2JsonEncoder(new Jackson2JsonEncoder(objectMapper));
        configurer.defaultCodecs().jackson2JsonDecoder(new Jackson2JsonDecoder(objectMapper));
        
        // 设置最大内存大小
        configurer.defaultCodecs().maxInMemorySize(1024 * 1024); // 1MB
    }
    
    /**
     * 创建自定义的 ObjectMapper
     * 
     * @return ObjectMapper 实例
     */
    @Bean
    public ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 配置时间格式
        mapper.findAndRegisterModules();
        
        // 忽略未知属性
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        
        // 忽略空值
        mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_NULL_MAP_VALUES, false);
        
        return mapper;
    }
    
    /**
     * Protobuf 编码器 Bean
     * 
     * @return ProtobufEncoder
     */
    @Bean
    public ProtobufEncoder protobufEncoder() {
        return new ProtobufEncoder();
    }
    
    /**
     * Protobuf 解码器 Bean
     * 
     * @return ProtobufDecoder
     */
    @Bean
    public ProtobufDecoder protobufDecoder() {
        return new ProtobufDecoder();
    }
}