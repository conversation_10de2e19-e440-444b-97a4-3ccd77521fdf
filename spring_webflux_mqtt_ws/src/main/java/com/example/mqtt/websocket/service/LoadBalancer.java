package com.example.mqtt.websocket.service;

import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * 负载均衡接口
 * 负责实例选择策略和健康状态管理
 */
public interface LoadBalancer {

    /**
     * 根据负载均衡策略选择实例
     * 
     * @param key 路由键（可选，用于一致性哈希等策略）
     * @return 选中的实例ID
     */
    Mono<String> selectInstance(String key);

    /**
     * 选择实例（不使用路由键）
     * 
     * @return 选中的实例ID
     */
    Mono<String> selectInstance();

    /**
     * 更新实例健康状态
     * 
     * @param instanceId 实例ID
     * @param healthy 健康状态
     * @return 更新结果
     */
    Mono<Void> updateInstanceHealth(String instanceId, boolean healthy);

    /**
     * 获取所有健康实例
     * 
     * @return 健康实例列表
     */
    Mono<List<String>> getHealthyInstances();

    /**
     * 获取实例权重
     * 
     * @param instanceId 实例ID
     * @return 实例权重
     */
    Mono<Integer> getInstanceWeight(String instanceId);

    /**
     * 设置实例权重
     * 
     * @param instanceId 实例ID
     * @param weight 权重值
     * @return 设置结果
     */
    Mono<Void> setInstanceWeight(String instanceId, int weight);

    /**
     * 获取负载均衡统计信息
     * 
     * @return 统计信息
     */
    Mono<LoadBalancerStats> getStats();

    /**
     * 负载均衡策略枚举
     */
    enum Strategy {
        ROUND_ROBIN,        // 轮询
        WEIGHTED_ROUND_ROBIN, // 加权轮询
        LEAST_CONNECTIONS,  // 最少连接
        CONSISTENT_HASH,    // 一致性哈希
        RANDOM             // 随机
    }

    /**
     * 负载均衡统计信息
     */
    record LoadBalancerStats(
        Map<String, Long> requestCounts,
        Map<String, Boolean> instanceHealth,
        Map<String, Integer> instanceWeights,
        long totalRequests,
        Strategy currentStrategy
    ) {}
}