package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.FailoverManager;
import com.example.mqtt.websocket.service.HealthCheckService;
import com.example.mqtt.websocket.service.LoadBalancer;
import com.example.mqtt.websocket.service.impl.DefaultFailoverManager;
import com.example.mqtt.websocket.service.impl.DefaultHealthCheckService;
import com.example.mqtt.websocket.service.impl.RoundRobinLoadBalancer;
import com.example.mqtt.websocket.service.impl.WeightedRoundRobinLoadBalancer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import jakarta.annotation.PostConstruct;

/**
 * 负载均衡配置类
 */
@Configuration
@EnableConfigurationProperties({LoadBalancerProperties.class, FailoverProperties.class})
public class LoadBalancerConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(LoadBalancerConfiguration.class);

    private final LoadBalancerProperties loadBalancerProperties;
    private final FailoverProperties failoverProperties;

    public LoadBalancerConfiguration(LoadBalancerProperties loadBalancerProperties, 
                                   FailoverProperties failoverProperties) {
        this.loadBalancerProperties = loadBalancerProperties;
        this.failoverProperties = failoverProperties;
    }

    @Bean
    @Primary
    @ConditionalOnProperty(name = "app.loadbalancer.strategy", havingValue = "round_robin", matchIfMissing = true)
    public LoadBalancer roundRobinLoadBalancer(ClusterStateManager clusterStateManager) {
        logger.info("Configuring Round Robin Load Balancer");
        return new RoundRobinLoadBalancer(clusterStateManager);
    }

    @Bean
    @ConditionalOnProperty(name = "app.loadbalancer.strategy", havingValue = "weighted_round_robin")
    public LoadBalancer weightedRoundRobinLoadBalancer(ClusterStateManager clusterStateManager) {
        logger.info("Configuring Weighted Round Robin Load Balancer");
        return new WeightedRoundRobinLoadBalancer(clusterStateManager);
    }

    @Bean
    @ConditionalOnMissingBean
    public FailoverManager failoverManager(ClusterStateManager clusterStateManager, LoadBalancer loadBalancer) {
        logger.info("Configuring Default Failover Manager");
        return new DefaultFailoverManager(clusterStateManager, loadBalancer);
    }

    @Bean
    @ConditionalOnMissingBean
    public HealthCheckService healthCheckService() {
        logger.info("Configuring Default Health Check Service");
        return new DefaultHealthCheckService();
    }

    @PostConstruct
    public void logConfiguration() {
        logger.info("Load Balancer Configuration:");
        logger.info("  Strategy: {}", loadBalancerProperties.getStrategy());
        logger.info("  Health Check Enabled: {}", loadBalancerProperties.isHealthCheckEnabled());
        logger.info("  Health Check Interval: {}s", loadBalancerProperties.getHealthCheckInterval());
        
        logger.info("Failover Configuration:");
        logger.info("  Auto Failover Enabled: {}", failoverProperties.isAutoFailoverEnabled());
        logger.info("  Auto Recovery Enabled: {}", failoverProperties.isAutoRecoveryEnabled());
        logger.info("  Failure Threshold: {}", failoverProperties.getFailureThreshold());
        logger.info("  Recovery Threshold: {}", failoverProperties.getRecoveryThreshold());
    }
}