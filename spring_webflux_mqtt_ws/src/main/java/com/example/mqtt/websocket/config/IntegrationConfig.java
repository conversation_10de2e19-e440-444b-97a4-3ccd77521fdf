package com.example.mqtt.websocket.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.channel.PublishSubscribeChannel;
import org.springframework.integration.channel.QueueChannel;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

/**
 * Spring Integration 配置类
 * 配置消息流、路由规则和处理通道
 */
@Configuration
@EnableIntegration
@IntegrationComponentScan
public class IntegrationConfig {

    /**
     * MQTT 路由通道 - 用于消息路由决策
     */
    @Bean
    public MessageChannel mqttRoutingChannel() {
        return new DirectChannel();
    }

    /**
     * WebSocket 消息通道
     */
    @Bean
    public MessageChannel webSocketChannel() {
        return new PublishSubscribeChannel();
    }

    /**
     * HTTP API 消息通道
     */
    @Bean
    public MessageChannel httpApiChannel() {
        return new DirectChannel();
    }

    /**
     * 系统管理消息通道
     */
    @Bean
    public MessageChannel systemChannel() {
        return new DirectChannel();
    }

    /**
     * 默认处理通道
     */
    @Bean
    public MessageChannel defaultProcessingChannel() {
        return new DirectChannel();
    }

    /**
     * 死信队列通道
     */
    @Bean
    public MessageChannel deadLetterChannel() {
        return new QueueChannel(1000); // 最多缓存1000条失败消息
    }

    /**
     * 消息过滤通道
     */
    @Bean
    public MessageChannel messageFilterChannel() {
        return new DirectChannel();
    }

    /**
     * 消息转换通道
     */
    @Bean
    public MessageChannel messageTransformChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT 消息处理流程
     * 从 MQTT 输入通道到路由处理的完整流程
     */
    @Bean
    public IntegrationFlow mqttProcessingFlow() {
        return IntegrationFlow
                .from("mqttInputChannel")
                // 消息过滤阶段
                .filter(org.springframework.messaging.Message.class, message-> {
                    // 过滤空消息
                    if (message.getPayload() == null) {
                        return false;
                    }
                    
                    // 过滤过大的消息（超过10MB）
                    if (message.getPayload() instanceof byte[]) {
                        byte[] payload = (byte[]) message.getPayload();
                        if (payload.length > 10 * 1024 * 1024) {
                            return false;
                        }
                    }
                    
                    return true;
                })
                // 消息增强 - 添加处理时间戳
                .enrichHeaders(h -> h
                    .header("processedAt", System.currentTimeMillis())
                    .header("processingNode", getNodeId())
                )
                // 路由到具体的处理器
                .channel("mqttRoutingChannel")
                .get();
    }

    /**
     * 主题路由流程
     * 根据 MQTT 主题路由到不同的处理通道
     */
    @Bean
    public IntegrationFlow topicRoutingFlow() {
        return IntegrationFlow
                .from("mqttRoutingChannel")
                .route(org.springframework.messaging.Message.class, message -> {
                    String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
                    if (topic == null) {
                        return "defaultProcessingChannel";
                    }
                    
                    // WebSocket 实时消息路由
                    if (topic.matches("^(ws|websocket)/.*")) {
                        return "webSocketChannel";
                    }
                    
                    // HTTP API 数据路由
                    if (topic.matches("^(api|rest)/.*")) {
                        return "httpApiChannel";
                    }
                    
                    // 系统管理消息路由
                    if (topic.matches("^(system|admin)/.*")) {
                        return "systemChannel";
                    }
                    
                    // 默认路由
                    return "defaultProcessingChannel";
                })
                .get();
    }

    /**
     * WebSocket 消息处理流程
     */
    @Bean
    public IntegrationFlow webSocketFlow() {
        return IntegrationFlow
                .from("webSocketChannel")
                .handle(message -> {
                    // WebSocket 消息处理逻辑将在后续任务中实现
                    System.out.println("Processing WebSocket message: " + message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC));
                })
                .get();
    }

    /**
     * HTTP API 消息处理流程
     */
    @Bean
    public IntegrationFlow httpApiFlow() {
        return IntegrationFlow
                .from("httpApiChannel")
                .handle(message -> {
                    // HTTP API 消息处理逻辑将在后续任务中实现
                    System.out.println("Processing HTTP API message: " + message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC));
                })
                .get();
    }

    /**
     * 系统管理消息处理流程
     */
    @Bean
    public IntegrationFlow systemFlow() {
        return IntegrationFlow
                .from("systemChannel")
                .handle(message -> {
                    // 系统管理消息处理逻辑
                    String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
                    System.out.println("Processing system message from topic: " + topic);
                    
                    // 可以处理系统命令、配置更新、健康检查等
                    if (topic != null && topic.contains("health")) {
                        // 健康检查响应
                        System.out.println("Health check requested");
                    }
                })
                .get();
    }

    /**
     * 默认消息处理流程
     */
    @Bean
    public IntegrationFlow defaultProcessingFlow() {
        return IntegrationFlow
                .from("defaultProcessingChannel")
                .handle(message -> {
                    String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
                    System.out.println("Processing default message from topic: " + topic);
                })
                .get();
    }

    /**
     * 死信队列处理流程
     */
    @Bean
    public IntegrationFlow deadLetterFlow() {
        return IntegrationFlow
                .from("deadLetterChannel")
                .handle(message -> {
                    String topic = (String) message.getHeaders().get("topic");
                    String error = (String) message.getHeaders().get("error");
                    Long timestamp = (Long) message.getHeaders().get("timestamp");
                    
                    System.err.println("Dead letter message - Topic: " + topic + 
                                     ", Error: " + error + 
                                     ", Timestamp: " + timestamp);
                    
                    // 这里可以实现死信消息的持久化、告警通知等逻辑
                })
                .get();
    }

    /**
     * 消息转换流程 - 支持不同格式之间的转换
     */
    @Bean
    public IntegrationFlow messageTransformFlow() {
        return IntegrationFlow
                .from("messageTransformChannel")
                .transform(message -> {
                    // 消息转换逻辑将在消息转换器中实现
                    return message;
                })
                .channel("mqttRoutingChannel")
                .get();
    }

    /**
     * 错误处理流程
     */
    @Bean
    public IntegrationFlow errorHandlingFlow() {
        return IntegrationFlow
                .from("errorChannel")
                .handle(message -> {
                    Exception exception = (Exception) message.getPayload();
                    System.err.println("Integration error occurred: " + exception.getMessage());
                    exception.printStackTrace();
                })
                .get();
    }

    /**
     * 消息日志记录处理器
     */
    @Bean
    public MessageHandler loggingHandler() {
        LoggingHandler handler = new LoggingHandler("INFO");
        handler.setLoggerName("mqtt.message.flow");
        return handler;
    }

    /**
     * 获取当前节点ID
     */
    private String getNodeId() {
        // 可以从环境变量、配置文件或系统属性中获取
        return System.getProperty("node.id", "default-node");
    }
}
