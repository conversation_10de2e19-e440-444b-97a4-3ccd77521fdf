// AES加密解密工具类包声明
package com.example.mqtt.websocket.utils;

// 导入Java安全相关异常类

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

/**
 * AES加密解密工具类
 * 提供AES-128/256加密解密功能，支持CBC模式和PKCS5填充
 * 加密时会在数据前添加时间戳，解密时可验证时间有效性
 */
public class AES {
    // 日志记录器，用于记录加密解密过程中的错误信息
    protected static final Logger LOG = LoggerFactory.getLogger(AES.class);
    // AES密钥长度，默认16字节(128位)，可通过useAes256方法设置为32字节(256位)
    public static int keyLen = 16;
    // 初始化向量(IV)长度，固定为16字节
    private final static int ivLen = 16;
    // 默认AES密钥，32字节长度，支持AES-256
    private static byte[] aes_key= {0x00,0x11,0x22,0x33,0x44,0x55,0x66,0x77,0x78,0x79,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F,0x3A,0x1F,0x28,0x39,0x4F,0x52,0x68,0x79,0x71,0x73,0x7A,0x7B,0x7C,0x7D,0x7E,0x7F};

    /**
     * AES加密方法 - 字符串版本
     * @param sSrc 要加密的字符串
     * @param userKey 用户提供的密钥字符串
     * @return 加密后的字节数组，失败返回null
     */
    public static byte[] AESEncrypt(String sSrc, String userKey) {
        // 将字符串转换为字节数组后调用字节数组版本的加密方法
        return AESEncrypt(sSrc.getBytes(), userKey);
    }

    /**
     * 设置AES加密模式
     * @param aes256 true使用AES-256(32字节密钥)，false使用AES-128(16字节密钥)
     */
    public static void useAes256(boolean aes256) {
        if(aes256) // 如果启用AES-256
            keyLen = 32; // 设置密钥长度为32字节
        else // 否则使用AES-128
            keyLen = 16; // 设置密钥长度为16字节
    }

    /**
     * AES加密方法 - 字节数组版本（使用字节数组密钥）
     * @param tobeencrypdata 要加密的数据字节数组
     * @param aesKey AES密钥字节数组
     * @return 加密后的字节数组，失败返回null
     */
    public static byte[] AESEncrypt(byte[] tobeencrypdata, byte[] aesKey) {
        // 检查密钥是否为空
        if (aesKey == null) {
            LOG.error("Key为空null"); // 输出错误信息
            return null; // 返回null表示加密失败
        }
        // 检查密钥长度是否满足最小要求
        if (aesKey.length < keyLen) {
            LOG.error("Key长度不是16位"); // 输出错误信息
            return null; // 返回null表示加密失败
        }
        // 如果密钥长度超过要求，截取前keyLen个字节
        if(aesKey.length > keyLen) {
            aesKey = Arrays.copyOfRange(aesKey, 0, keyLen); // 截取密钥到指定长度
        }

        // 准备初始化向量(IV)
        byte[] ivKeys;
        if(aesKey.length == ivLen) { // 如果密钥长度等于IV长度
            ivKeys = aesKey; // 直接使用密钥作为IV
        } else { // 否则截取密钥前ivLen个字节作为IV
            ivKeys = Arrays.copyOfRange(aesKey, 0, ivLen);
        }

        try {
            // 创建AES密钥规范对象
            SecretKeySpec skeySpec = new SecretKeySpec(aesKey, "AES");
            // 获取AES加密器实例，使用CBC模式和PKCS5填充
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");//"算法/模式/补码方式"
            // 创建初始化向量参数规范，CBC模式需要IV来增加加密强度
            IvParameterSpec iv = new IvParameterSpec(ivKeys);//使用CBC模式，需要一个向量iv，可增加加密算法的强度
            // 初始化加密器为加密模式
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);

            // 计算当前时间：2018.1.1 0:0:0 以来的小时数
            int curhour = (int) ((System.currentTimeMillis()/1000 - 1514736000)/3600);

            // 创建包含时间戳的数据数组（原数据长度+4字节时间戳）
            byte[] tobeencrypdatawithtime = new byte[tobeencrypdata.length + 4];
            // 将小时数的低8位存储到第0个字节
            byte byte0 = (byte)(curhour & 0xFF);
            tobeencrypdatawithtime[0] = byte0;

            // 将小时数的第9-16位存储到第1个字节
            byte byte1 = (byte)((curhour & 0xFF00) >> 8);
            tobeencrypdatawithtime[1] = byte1;

            // 将小时数的第17-24位存储到第2个字节
            byte byte2 = (byte)((curhour & 0xFF0000) >> 16);
            tobeencrypdatawithtime[2] = byte2;

            // 将小时数的第25-32位存储到第3个字节
            byte byte3 = (byte)((curhour & 0xFF000000) >> 24);
            tobeencrypdatawithtime[3] = byte3;

            // 将原始数据复制到时间戳后面（从第4个字节开始）
            System.arraycopy(tobeencrypdata, 0, tobeencrypdatawithtime, 4, tobeencrypdata.length);

            // 执行AES加密
            byte[] encrypted = cipher.doFinal(tobeencrypdatawithtime);
            return encrypted; // 返回加密后的数据
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace(); // 打印算法不存在异常堆栈
        } catch (NoSuchPaddingException e) {
            e.printStackTrace(); // 打印填充方式不存在异常堆栈
        } catch (InvalidKeyException e) {
            e.printStackTrace(); // 打印无效密钥异常堆栈
        } catch (InvalidAlgorithmParameterException e) {
            e.printStackTrace(); // 打印无效算法参数异常堆栈
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace(); // 打印非法块大小异常堆栈
        } catch (BadPaddingException e) {
            e.printStackTrace(); // 打印填充错误异常堆栈
        }
        return null; // 发生异常时返回null
    }

    /**
     * AES加密方法 - 字节数组版本（使用字符串密钥）
     * @param tobeencrypdata 要加密的数据字节数组
     * @param userKey 用户提供的密钥字符串
     * @return 加密后的字节数组，失败返回null
     */
    public static byte[] AESEncrypt(byte[] tobeencrypdata, String userKey) {
        byte[] aesKey = aes_key; // 默认使用内置密钥
        // 如果用户提供了密钥且不为空，则转换用户密钥
        if (userKey != null && !userKey.isEmpty()) {
            aesKey = convertUserKey(userKey); // 将字符串密钥转换为字节数组
        }
        // 调用字节数组版本的加密方法
        return AESEncrypt(tobeencrypdata, aesKey);
    }

    /**
     * 将有符号字节转换为无符号整数
     * @param data 输入的字节数据
     * @return 0-255范围内的无符号整数值
     */
    public static int getUnsignedByte (byte data){      //将data字节型数据转换为0~255 (0xFF 即BYTE)。
        return data&0x0FF ; // 通过与0xFF进行按位与操作，将有符号字节转换为无符号整数
    }

    /**
     * 将用户提供的字符串密钥转换为字节数组
     * @param userKey 用户密钥字符串
     * @return 转换后的密钥字节数组
     */
    private static byte[] convertUserKey(String userKey) {
        byte[] key = new byte[keyLen]; // 创建指定长度的密钥数组
        // 遍历密钥长度，将字符串的每个字符转换为字节
        for (int i = 0; i < keyLen; i++) {
            key[i] = (byte) (userKey.charAt(i) & 0xFF); // 取字符的低8位作为字节值
        }
        return key; // 返回转换后的密钥字节数组
    }

    /**
     * AES解密方法 - 简化版本（不返回时间验证结果）
     * @param sSrc 要解密的数据字节数组
     * @param userKey 用户提供的密钥字符串
     * @param checkTime 是否检查时间戳有效性
     * @return 解密后的原始数据字节数组，失败返回null
     */
    public static byte[] AESDecrypt(byte[] sSrc, String userKey, boolean checkTime) {
        // 调用完整版本的解密方法，不获取时间验证结果
        return AESDecrypt(sSrc, userKey, checkTime, null);
    }

    /**
     * AES解密方法 - 完整版本（使用字符串密钥）
     * @param sSrc 要解密的数据字节数组
     * @param userKey 用户提供的密钥字符串
     * @param checkTime 是否检查时间戳有效性
     * @param invalidTime 输出参数，用于返回时间是否无效的结果
     * @return 解密后的原始数据字节数组，失败返回null
     */
    public static byte[] AESDecrypt(byte[] sSrc, String userKey, boolean checkTime, boolean[] invalidTime) {
        byte[] aesKey = aes_key; // 默认使用内置密钥
        // 如果用户提供了密钥且不为空，则转换用户密钥
        if (userKey != null && !userKey.isEmpty()) {
            aesKey = convertUserKey(userKey); // 将字符串密钥转换为字节数组
        }
        // 调用字节数组版本的解密方法
        return AESDecrypt(sSrc, aesKey, checkTime, invalidTime);
    }

    /**
     * AES解密方法 - 核心实现（使用字节数组密钥）
     * @param sSrc 要解密的数据字节数组
     * @param aesKey AES密钥字节数组
     * @param checkTime 是否检查时间戳有效性
     * @param invalidTime 输出参数，用于返回时间是否无效的结果
     * @return 解密后的原始数据字节数组，失败返回null
     */
    public static byte[] AESDecrypt(byte[] sSrc, byte[] aesKey, boolean checkTime, boolean[] invalidTime) {
        try {
            LOG.warn("开始解密---------------------------------》");
            // 检查密钥是否为空，如果为空则使用默认密钥
            if (aesKey == null) {
                aesKey = aes_key; // 使用内置默认密钥
            }
            // 检查密钥长度是否满足最小要求
            if (aesKey.length < keyLen) {
                LOG.error("Key长度不是16位"); // 输出错误信息到日志
                return null; // 返回null表示解密失败
            }

            // 如果密钥长度超过要求，截取前keyLen个字节
            if(aesKey.length > keyLen) {
                aesKey = Arrays.copyOfRange(aesKey, 0, keyLen); // 截取密钥到指定长度
            }

            // 准备初始化向量(IV)
            byte[] ivKeys;
            if(aesKey.length == ivLen) { // 如果密钥长度等于IV长度
                ivKeys = aesKey; // 直接使用密钥作为IV
            } else { // 否则截取密钥前ivLen个字节作为IV
                ivKeys = Arrays.copyOfRange(aesKey, 0, ivLen);
            }

            // 创建AES密钥规范对象
            SecretKeySpec skeySpec = new SecretKeySpec(aesKey, "AES");
            // 获取AES解密器实例，使用CBC模式和PKCS5填充
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            // 创建初始化向量参数规范
            IvParameterSpec iv = new IvParameterSpec(ivKeys);
            // 初始化解密器为解密模式
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            try {
                // 执行AES解密
                byte[] original = cipher.doFinal(sSrc);
                int hours = 0; // 用于存储解析出的时间戳（小时数）

                // 检查解密后的数据长度是否包含时间戳（至少4字节）
                if (original.length > 4) {
                    // 从解密数据中提取时间戳（小时数），按小端序解析
                    hours += getUnsignedByte(original[3]); // 获取第4个字节（高位）
                    hours <<= 8; // 左移8位

                    hours += getUnsignedByte(original[2]); // 获取第3个字节
                    hours <<= 8; // 左移8位

                    hours += getUnsignedByte(original[1]); // 获取第2个字节
                    hours <<= 8; // 左移8位

                    hours += getUnsignedByte(original[0]); // 获取第1个字节（低位）

                    // 计算当前时间：2018.1.1 0:0:0 以来的小时数
                    int curhour = (int) ((System.currentTimeMillis()/1000 - 1514736000)/3600);

                    // 如果需要检查时间且时间差超过24小时，则认为数据无效
                    if (Math.abs(curhour - hours)  > 24 && checkTime) {
                        // 如果提供了invalidTime数组，设置时间无效标志
                        if(invalidTime != null) {
                            invalidTime[0] = true; // 标记时间无效
                        }
                        return null; // 返回null表示解密失败（时间无效）
                    }
                    // 创建新数组存储去除时间戳后的原始数据
                    byte[] neworiginal = new byte[original.length - 4];
                    // 复制原始数据（跳过前4个字节的时间戳）
                    System.arraycopy(original, 4, neworiginal, 0, neworiginal.length);
                    LOG.warn("解密完成---------------------------------》");
                    return neworiginal; // 返回解密后的原始数据
                }
                return null; // 数据长度不足，返回null
            } catch (Exception e) {
                e.printStackTrace(); // 打印内层异常堆栈
                LOG.error(e.toString()); // 记录内层异常到日志
                return null; // 解密过程中发生异常，返回null
            }
        } catch (Exception ex) {
            ex.printStackTrace(); // 打印外层异常堆栈
            LOG.error(ex.toString()); // 记录外层异常到日志
            return null; // 初始化解密器时发生异常，返回null
        }
    }
}
