package com.example.mqtt.websocket.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

/**
 * 负载均衡配置属性
 */
@ConfigurationProperties(prefix = "app.loadbalancer")
@Validated
public class LoadBalancerProperties {

    /**
     * 负载均衡策略
     */
    @NotBlank
    private String strategy = "round_robin";

    /**
     * 是否启用健康检查
     */
    private boolean healthCheckEnabled = true;

    /**
     * 健康检查间隔（秒）
     */
    @Min(1)
    private int healthCheckInterval = 30;

    /**
     * 健康检查超时（秒）
     */
    @Min(1)
    private int healthCheckTimeout = 5;

    /**
     * 实例权重映射
     */
    private java.util.Map<String, Integer> instanceWeights = new java.util.HashMap<>();

    /**
     * 是否启用统计信息收集
     */
    private boolean statsEnabled = true;

    /**
     * 统计信息重置间隔（秒）
     */
    @Min(60)
    private int statsResetInterval = 3600;

    // Getters and Setters
    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public boolean isHealthCheckEnabled() {
        return healthCheckEnabled;
    }

    public void setHealthCheckEnabled(boolean healthCheckEnabled) {
        this.healthCheckEnabled = healthCheckEnabled;
    }

    public int getHealthCheckInterval() {
        return healthCheckInterval;
    }

    public void setHealthCheckInterval(int healthCheckInterval) {
        this.healthCheckInterval = healthCheckInterval;
    }

    public int getHealthCheckTimeout() {
        return healthCheckTimeout;
    }

    public void setHealthCheckTimeout(int healthCheckTimeout) {
        this.healthCheckTimeout = healthCheckTimeout;
    }

    public java.util.Map<String, Integer> getInstanceWeights() {
        return instanceWeights;
    }

    public void setInstanceWeights(java.util.Map<String, Integer> instanceWeights) {
        this.instanceWeights = instanceWeights;
    }

    public boolean isStatsEnabled() {
        return statsEnabled;
    }

    public void setStatsEnabled(boolean statsEnabled) {
        this.statsEnabled = statsEnabled;
    }

    public int getStatsResetInterval() {
        return statsResetInterval;
    }

    public void setStatsResetInterval(int statsResetInterval) {
        this.statsResetInterval = statsResetInterval;
    }
}