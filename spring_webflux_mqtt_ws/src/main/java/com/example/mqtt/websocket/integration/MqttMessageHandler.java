package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.MqttMessage;
import com.example.mqtt.websocket.service.EncryptionService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.Router;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * MQTT 消息处理器
 * 处理从 MQTT 接收到的消息，支持路由、转换和过滤
 */

public class MqttMessageHandler {

    private static final Logger logger = LoggerFactory.getLogger(MqttMessageHandler.class);
    
    private final ObjectMapper objectMapper;
    private final EncryptionService encryptionService;
    private final MessageChannel webSocketChannel;
    private final MessageChannel httpApiChannel;
    private final MessageChannel deadLetterChannel;
    
    // 主题路由规则缓存
    private final Map<String, Pattern> topicPatterns = new ConcurrentHashMap<>();
    
    @Autowired
    public MqttMessageHandler(ObjectMapper objectMapper,
                             EncryptionService encryptionService,
                             MessageChannel webSocketChannel,
                             MessageChannel httpApiChannel,
                             MessageChannel deadLetterChannel) {
        this.objectMapper = objectMapper;
        this.encryptionService = encryptionService;
        this.webSocketChannel = webSocketChannel;
        this.httpApiChannel = httpApiChannel;
        this.deadLetterChannel = deadLetterChannel;
        
        // 初始化主题路由模式
        initializeTopicPatterns();
    }

    /**
     * 处理 MQTT 入站消息
     * 
     * @param message 消息内容
     * @param topic MQTT 主题
     * @param qos 服务质量等级
     * @param retained 是否为保留消息
     * @param duplicate 是否为重复消息
     */
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public void handleMqttMessage(@Payload byte[] message,
                                 @Header(MqttHeaders.RECEIVED_TOPIC) String topic,
                                 @Header(MqttHeaders.RECEIVED_QOS) int qos,
                                 @Header(MqttHeaders.RECEIVED_RETAINED) boolean retained,
                                 @Header(MqttHeaders.DUPLICATE) boolean duplicate) {
        
        try {
            logger.info("Received MQTT message from topic: {}, QoS: {}, Retained: {}, Duplicate: {}", 
                       topic, qos, retained, duplicate);
            
            // 消息过滤 - 跳过重复消息（可配置）
            if (duplicate && shouldSkipDuplicates(topic)) {
                logger.debug("Skipping duplicate message from topic: {}", topic);
                return;
            }
            
            // 消息转换和处理
            BaseMessage baseMessage = convertToBaseMessage(message, topic, qos, retained);
            
            // 消息解密（如果需要）
            if (baseMessage.getEncrypted()) {
                baseMessage = decryptMessage(baseMessage);
            }
            
            // 消息路由
            routeMessage(baseMessage, topic);
            
        } catch (Exception e) {
            logger.error("Error processing MQTT message from topic: {}", topic, e);
            handleProcessingError(message, topic, e);
        }
    }

    /**
     * 消息路由器 - 根据主题路由到不同的处理通道
     */
    @Router(inputChannel = "mqttRoutingChannel")
    public String routeByTopic(@Header(MqttHeaders.RECEIVED_TOPIC) String topic) {
        // WebSocket 实时消息路由
        if (isWebSocketTopic(topic)) {
            return "webSocketChannel";
        }
        
        // HTTP API 数据路由
        if (isApiTopic(topic)) {
            return "httpApiChannel";
        }
        
        // 系统管理消息路由
        if (isSystemTopic(topic)) {
            return "systemChannel";
        }
        
        // 默认路由到通用处理通道
        return "defaultProcessingChannel";
    }

    /**
     * 将原始消息转换为 BaseMessage
     */
    private BaseMessage convertToBaseMessage(byte[] rawMessage, String topic, int qos, boolean retained) {
        try {
            // 尝试解析为 Protobuf 格式
            if (isProtobufMessage(rawMessage)) {
                return parseProtobufMessage(rawMessage);
            }
            
            // 尝试解析为 JSON 格式
            if (isJsonMessage(rawMessage)) {
                return parseJsonMessage(rawMessage, topic, qos, retained);
            }
            
            // 作为纯文本处理
            return createTextMessage(rawMessage, topic, qos, retained);
            
        } catch (Exception e) {
            logger.warn("Failed to convert message, treating as raw bytes: {}", e.getMessage());
            return createRawMessage(rawMessage, topic, qos, retained);
        }
    }

    /**
     * 解析 Protobuf 消息
     */
    private BaseMessage parseProtobufMessage(byte[] data) throws InvalidProtocolBufferException {
        // 首先尝试解析为 MqttMessage
        try {
            MqttMessage mqttMessage = MqttMessage.parseFrom(data);
            return mqttMessage.getMessage();
        } catch (InvalidProtocolBufferException e) {
            // 直接解析为 BaseMessage
            return BaseMessage.parseFrom(data);
        }
    }

    /**
     * 解析 JSON 消息
     */
    private BaseMessage parseJsonMessage(byte[] data, String topic, int qos, boolean retained) throws Exception {
        JsonNode jsonNode = objectMapper.readTree(data);
        
        BaseMessage.Builder builder = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("json")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom(data))
                .setEncrypted(false);
        
        // 添加 MQTT 特定的头信息
        builder.putHeaders("mqtt.topic", topic);
        builder.putHeaders("mqtt.qos", String.valueOf(qos));
        builder.putHeaders("mqtt.retained", String.valueOf(retained));
        
        // 如果 JSON 包含特定字段，提取它们
        if (jsonNode.has("id")) {
            builder.setId(jsonNode.get("id").asText());
        }
        if (jsonNode.has("type")) {
            builder.setType(jsonNode.get("type").asText());
        }
        if (jsonNode.has("timestamp")) {
            builder.setTimestamp(jsonNode.get("timestamp").asLong());
        }
        
        return builder.build();
    }

    /**
     * 创建文本消息
     */
    private BaseMessage createTextMessage(byte[] data, String topic, int qos, boolean retained) {
        return BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("text")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom(data))
                .setEncrypted(false)
                .putHeaders("mqtt.topic", topic)
                .putHeaders("mqtt.qos", String.valueOf(qos))
                .putHeaders("mqtt.retained", String.valueOf(retained))
                .build();
    }

    /**
     * 创建原始字节消息
     */
    private BaseMessage createRawMessage(byte[] data, String topic, int qos, boolean retained) {
        return BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("raw")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom(data))
                .setEncrypted(false)
                .putHeaders("mqtt.topic", topic)
                .putHeaders("mqtt.qos", String.valueOf(qos))
                .putHeaders("mqtt.retained", String.valueOf(retained))
                .build();
    }

    /**
     * 解密消息
     */
    private BaseMessage decryptMessage(BaseMessage encryptedMessage) {
        try {
            String keyId = encryptedMessage.getHeadersOrDefault("keyId", "default");
            byte[] decryptedPayload = encryptionService.decrypt(
                encryptedMessage.getPayload().toByteArray(), keyId);
            
            return encryptedMessage.toBuilder()
                    .setPayload(com.google.protobuf.ByteString.copyFrom(decryptedPayload))
                    .setEncrypted(false)
                    .build();
                    
        } catch (Exception e) {
            logger.error("Failed to decrypt message: {}", e.getMessage());
            throw new MqttMessageProcessingException("Message decryption failed", e);
        }
    }

    /**
     * 路由消息到相应的处理通道
     */
    private void routeMessage(BaseMessage baseMessage, String topic) {
        Message<BaseMessage> message = MessageBuilder
                .withPayload(baseMessage)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, topic)
                .setHeader("messageType", baseMessage.getType())
                .setHeader("messageSource", baseMessage.getSource())
                .build();

        if (isWebSocketTopic(topic)) {
            webSocketChannel.send(message);
            logger.debug("Routed message to WebSocket channel from topic: {}", topic);
        } else if (isApiTopic(topic)) {
            httpApiChannel.send(message);
            logger.debug("Routed message to HTTP API channel from topic: {}", topic);
        } else {
            // 默认处理
            logger.debug("Processing message locally from topic: {}", topic);
            processMessageLocally(baseMessage, topic);
        }
    }

    /**
     * 本地处理消息
     */
    private void processMessageLocally(BaseMessage message, String topic) {
        logger.info("Processing message locally - Topic: {}, Type: {}, Size: {} bytes", 
                   topic, message.getType(), message.getPayload().size());
        
        // 这里可以添加具体的业务逻辑
        // 例如：数据持久化、业务规则处理、通知发送等
    }

    /**
     * 处理消息处理错误
     */
    private void handleProcessingError(byte[] originalMessage, String topic, Exception error) {
        try {
            Message<byte[]> errorMessage = MessageBuilder
                    .withPayload(originalMessage)
                    .setHeader("error", error.getMessage())
                    .setHeader("topic", topic)
                    .setHeader("timestamp", Instant.now().toEpochMilli())
                    .build();
            
            deadLetterChannel.send(errorMessage);
            logger.info("Sent failed message to dead letter channel from topic: {}", topic);
            
        } catch (Exception e) {
            logger.error("Failed to send message to dead letter channel: {}", e.getMessage());
        }
    }

    /**
     * 初始化主题路由模式
     */
    private void initializeTopicPatterns() {
        topicPatterns.put("websocket", Pattern.compile("^(ws|websocket)/.*"));
        topicPatterns.put("api", Pattern.compile("^(api|rest)/.*"));
        topicPatterns.put("system", Pattern.compile("^(system|admin)/.*"));
    }

    /**
     * 判断是否为 WebSocket 主题
     */
    private boolean isWebSocketTopic(String topic) {
        return topicPatterns.get("websocket").matcher(topic).matches();
    }

    /**
     * 判断是否为 API 主题
     */
    private boolean isApiTopic(String topic) {
        return topicPatterns.get("api").matcher(topic).matches();
    }

    /**
     * 判断是否为系统主题
     */
    private boolean isSystemTopic(String topic) {
        return topicPatterns.get("system").matcher(topic).matches();
    }

    /**
     * 判断是否应该跳过重复消息
     */
    private boolean shouldSkipDuplicates(String topic) {
        // 可以根据主题配置是否跳过重复消息
        return !topic.startsWith("critical/");
    }

    /**
     * 判断是否为 Protobuf 消息
     */
    private boolean isProtobufMessage(byte[] data) {
        // 简单的 Protobuf 格式检测
        if (data.length < 2) return false;
        
        try {
            BaseMessage.parseFrom(data);
            return true;
        } catch (InvalidProtocolBufferException e) {
            return false;
        }
    }

    /**
     * 判断是否为 JSON 消息
     */
    private boolean isJsonMessage(byte[] data) {
        try {
            String content = new String(data).trim();
            return (content.startsWith("{") && content.endsWith("}")) ||
                   (content.startsWith("[") && content.endsWith("]"));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * MQTT 消息处理异常
     */
    public static class MqttMessageProcessingException extends RuntimeException {
        public MqttMessageProcessingException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
