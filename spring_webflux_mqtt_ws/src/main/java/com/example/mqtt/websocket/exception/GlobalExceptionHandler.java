package com.example.mqtt.websocket.exception;

import com.example.mqtt.websocket.service.AlertService;
import com.example.mqtt.websocket.service.EncryptionException;
import com.example.mqtt.websocket.service.ExceptionStatisticsService;
import com.example.mqtt.websocket.service.WebSocketSessionManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 全局异常处理器
 * 处理系统中的各类异常，提供统一的异常处理策略
 */
@Component("applicationGlobalExceptionHandler")
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    private static final Logger securityLogger = LoggerFactory.getLogger("SECURITY");
    
    @Autowired
    private WebSocketSessionManager sessionManager;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @Autowired
    private AlertService alertService;
    
    @Autowired
    private ExceptionStatisticsService statisticsService;
    
    // 异常统计计数器
    private Counter mqttExceptionCounter;
    private Counter webSocketExceptionCounter;
    private Counter encryptionExceptionCounter;
    private Counter messageProcessingExceptionCounter;
    
    // 异常统计
    private final AtomicLong totalExceptions = new AtomicLong(0);
    private final AtomicLong mqttExceptions = new AtomicLong(0);
    private final AtomicLong webSocketExceptions = new AtomicLong(0);
    private final AtomicLong encryptionExceptions = new AtomicLong(0);
    
    @PostConstruct
    public void initMetrics() {
        mqttExceptionCounter = Counter.builder("mqtt.exceptions.total")
            .description("Total number of MQTT exceptions")
            .register(meterRegistry);
            
        webSocketExceptionCounter = Counter.builder("websocket.exceptions.total")
            .description("Total number of WebSocket exceptions")
            .register(meterRegistry);
            
        encryptionExceptionCounter = Counter.builder("encryption.exceptions.total")
            .description("Total number of encryption exceptions")
            .register(meterRegistry);
            
        messageProcessingExceptionCounter = Counter.builder("message.processing.exceptions.total")
            .description("Total number of message processing exceptions")
            .register(meterRegistry);
    }
    
    /**
     * 处理 MQTT 连接异常
     */
    @EventListener
    public void handleMqttConnectionException(MqttConnectionException ex) {
        logger.error("MQTT connection exception occurred", ex);
        
        // 更新统计
        mqttExceptionCounter.increment();
        mqttExceptions.incrementAndGet();
        totalExceptions.incrementAndGet();
        
        // 记录到统计服务
        statisticsService.recordException("MqttConnectionException", ex.getMessage(), ex);
        
        // 记录异常详情
        logger.warn("MQTT connection failed - Broker: {}, Client: {}, Error: {}", 
            ex.getBrokerUrl(), ex.getClientId(), ex.getMessage());
        
        // 触发重连机制 (通过发布事件)
        // 这里可以发布一个重连事件，由 MqttConnectionMonitor 处理
        logger.info("Triggering MQTT reconnection for client: {}", ex.getClientId());
        
        // 发送告警通知
        alertService.sendAlert(AlertService.AlertLevel.HIGH, 
            "MQTT Connection Failed", 
            String.format("MQTT connection failed for client %s to broker %s", 
                ex.getClientId(), ex.getBrokerUrl()), 
            "MQTT");
    }
    
    /**
     * 处理 WebSocket 异常
     */
    @EventListener
    public void handleWebSocketException(WebSocketException ex) {
        logger.error("WebSocket exception occurred", ex);
        
        // 更新统计
        webSocketExceptionCounter.increment();
        webSocketExceptions.incrementAndGet();
        totalExceptions.incrementAndGet();
        
        // 记录到统计服务
        statisticsService.recordException("WebSocketException", ex.getMessage(), ex);
        
        // 清理会话资源
        if (ex.getSessionId() != null) {
            try {
                sessionManager.removeSession(ex.getSessionId());
                logger.info("Cleaned up WebSocket session: {}", ex.getSessionId());
            } catch (Exception cleanupEx) {
                logger.error("Failed to cleanup WebSocket session: {}", ex.getSessionId(), cleanupEx);
            }
        }
        
        // 通知客户端重连 (如果会话仍然活跃)
        if (ex.getSessionId() != null && sessionManager.isSessionActive(ex.getSessionId())) {
            try {
                sessionManager.sendSystemMessage(ex.getSessionId(), 
                    "Connection error occurred. Please reconnect.");
            } catch (Exception notifyEx) {
                logger.error("Failed to notify client about connection error", notifyEx);
            }
        }
        
        // 发送告警通知
        alertService.sendAlert(AlertService.AlertLevel.MEDIUM, 
            "WebSocket Connection Error", 
            String.format("WebSocket error for session %s (user %s): %s", 
                ex.getSessionId(), ex.getUserId(), ex.getMessage()), 
            "WEBSOCKET");
        
        // 记录异常统计
        logger.warn("WebSocket error - Session: {}, User: {}, Error: {}", 
            ex.getSessionId(), ex.getUserId(), ex.getMessage());
    }
    
    /**
     * 处理加密解密异常
     */
    @EventListener
    public void handleEncryptionException(EncryptionException ex) {
        // 记录安全事件
        securityLogger.error("Encryption/Decryption exception occurred", ex);
        
        // 更新统计
        encryptionExceptionCounter.increment();
        encryptionExceptions.incrementAndGet();
        totalExceptions.incrementAndGet();
        
        // 记录到统计服务
        statisticsService.recordException("EncryptionException", ex.getMessage(), ex);
        
        // 记录安全审计日志
        securityLogger.warn("Security event - Encryption operation failed: {}", ex.getMessage());
        
        // 拒绝处理消息 (通过抛出异常)
        logger.error("Message processing rejected due to encryption failure: {}", ex.getMessage());
        
        // 触发安全审计
        triggerSecurityAudit("ENCRYPTION_FAILURE", ex.getMessage());
        
        // 发送安全告警
        alertService.sendAlert(AlertService.AlertLevel.CRITICAL, 
            "Encryption Failure", 
            "Encryption/Decryption operation failed: " + ex.getMessage(), 
            "SECURITY");
    }
    
    /**
     * 处理消息处理异常
     */
    @EventListener
    public void handleMessageProcessingException(MessageProcessingException ex) {
        logger.error("Message processing exception occurred", ex);
        
        // 更新统计
        messageProcessingExceptionCounter.increment();
        totalExceptions.incrementAndGet();
        
        // 记录到统计服务
        statisticsService.recordException("MessageProcessingException", ex.getMessage(), ex);
        
        // 记录消息处理失败详情
        logger.warn("Message processing failed - ID: {}, Type: {}, Error: {}", 
            ex.getMessageId(), ex.getMessageType(), ex.getMessage());
        
        // 发送告警通知
        alertService.sendAlert(AlertService.AlertLevel.MEDIUM, 
            "Message Processing Failed", 
            String.format("Failed to process %s message %s: %s", 
                ex.getMessageType(), ex.getMessageId(), ex.getMessage()), 
            "MESSAGE_PROCESSING");
        
        // 这里消息会被发送到死信队列 (由 Spring Integration 的错误处理机制处理)
    }
    
    /**
     * 处理通用异常
     */
    @EventListener
    public void handleGenericException(Exception ex) {
        logger.error("Unhandled exception occurred", ex);
        
        totalExceptions.incrementAndGet();
        
        // 记录到统计服务
        statisticsService.recordException("GenericException", ex.getMessage(), ex);
        
        // 发送通用告警
        alertService.sendAlert(AlertService.AlertLevel.MEDIUM, 
            "Unhandled Exception", 
            "An unhandled exception occurred: " + ex.getMessage(), 
            "GENERIC");
    }
    

    
    /**
     * 触发安全审计
     */
    private void triggerSecurityAudit(String eventType, String details) {
        securityLogger.info("SECURITY_AUDIT - Event: {}, Details: {}, Timestamp: {}", 
            eventType, details, LocalDateTime.now());
    }
    
    /**
     * 获取异常统计信息
     */
    public ExceptionStatistics getExceptionStatistics() {
        return new ExceptionStatistics(
            totalExceptions.get(),
            mqttExceptions.get(),
            webSocketExceptions.get(),
            encryptionExceptions.get()
        );
    }
    
    /**
     * 异常统计信息类
     */
    public static class ExceptionStatistics {
        private final long totalExceptions;
        private final long mqttExceptions;
        private final long webSocketExceptions;
        private final long encryptionExceptions;
        
        public ExceptionStatistics(long totalExceptions, long mqttExceptions, 
                                 long webSocketExceptions, long encryptionExceptions) {
            this.totalExceptions = totalExceptions;
            this.mqttExceptions = mqttExceptions;
            this.webSocketExceptions = webSocketExceptions;
            this.encryptionExceptions = encryptionExceptions;
        }
        
        public long getTotalExceptions() { return totalExceptions; }
        public long getMqttExceptions() { return mqttExceptions; }
        public long getWebSocketExceptions() { return webSocketExceptions; }
        public long getEncryptionExceptions() { return encryptionExceptions; }
    }
}
