package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * WebSocket 消息服务
 * 提供消息创建、处理和路由的高级接口
 * 
 * <AUTHOR>
 */
@Service
public class WebSocketMessageService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketMessageService.class);
    
    private final WebSocketSessionManager sessionManager;
    
    @Autowired
    public WebSocketMessageService(WebSocketSessionManager sessionManager) {
        this.sessionManager = sessionManager;
    }
    
    /**
     * 创建并发送广播消息
     * 
     * @param messageType 消息类型
     * @param payload 消息内容
     * @param source 消息来源
     * @return 发送结果
     */
    public Mono<Void> broadcastMessage(String messageType, String payload, String source) {
        BaseMessage baseMessage = createBaseMessage(messageType, payload, source);
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setType(WebSocketMessage.MessageType.BROADCAST)
                .setMessage(baseMessage)
                .build();
        
        logger.info("Broadcasting message: type={}, source={}, messageId={}", 
                   messageType, source, baseMessage.getId());
        
        return sessionManager.broadcastMessage(wsMessage);
    }
    
    /**
     * 创建并发送单播消息
     * 
     * @param targetUserId 目标用户ID
     * @param messageType 消息类型
     * @param payload 消息内容
     * @param source 消息来源
     * @return 发送结果
     */
    public Mono<Void> sendToUser(String targetUserId, String messageType, String payload, String source) {
        BaseMessage baseMessage = createBaseMessage(messageType, payload, source);
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setUserId(targetUserId)
                .setType(WebSocketMessage.MessageType.UNICAST)
                .setMessage(baseMessage)
                .build();
        
        logger.info("Sending unicast message: targetUserId={}, type={}, source={}, messageId={}", 
                   targetUserId, messageType, source, baseMessage.getId());
        
        return sessionManager.sendToUser(targetUserId, wsMessage);
    }
    
    /**
     * 创建并发送系统消息
     * 
     * @param sessionId 目标会话ID
     * @param messageType 消息类型
     * @param payload 消息内容
     * @return 发送结果
     */
    public Mono<Void> sendSystemMessage(String sessionId, String messageType, String payload) {
        BaseMessage baseMessage = createBaseMessage(messageType, payload, "system");
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setSessionId(sessionId)
                .setType(WebSocketMessage.MessageType.SYSTEM)
                .setMessage(baseMessage)
                .build();
        
        logger.info("Sending system message: sessionId={}, type={}, messageId={}", 
                   sessionId, messageType, baseMessage.getId());
        
        return sessionManager.sendToSession(sessionId, wsMessage);
    }
    
    /**
     * 发送通知消息到所有用户
     * 
     * @param title 通知标题
     * @param content 通知内容
     * @param level 通知级别（info, warning, error）
     * @return 发送结果
     */
    public Mono<Void> sendNotification(String title, String content, String level) {
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType("notification")
                .setSource("system")
                .putHeaders("title", title)
                .putHeaders("level", level)
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8(content))
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setType(WebSocketMessage.MessageType.BROADCAST)
                .setMessage(baseMessage)
                .build();
        
        logger.info("Sending notification: title={}, level={}, messageId={}", 
                   title, level, baseMessage.getId());
        
        return sessionManager.broadcastMessage(wsMessage);
    }
    
    /**
     * 获取系统状态信息
     * 
     * @return 系统状态消息
     */
    public WebSocketMessage getSystemStatus() {
        BaseMessage statusMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType("system_status")
                .setSource("system")
                .putHeaders("activeConnections", String.valueOf(sessionManager.getActiveSessionCount()))
                .putHeaders("serverTime", String.valueOf(System.currentTimeMillis()))
                .build();
        
        return WebSocketMessage.newBuilder()
                .setType(WebSocketMessage.MessageType.SYSTEM)
                .setMessage(statusMessage)
                .build();
    }
    
    /**
     * 创建基础消息
     * 
     * @param messageType 消息类型
     * @param payload 消息内容
     * @param source 消息来源
     * @return BaseMessage
     */
    private BaseMessage createBaseMessage(String messageType, String payload, String source) {
        return BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType(messageType)
                .setSource(source)
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8(payload))
                .setEncrypted(false) // 默认不加密，后续可根据需要加密
                .build();
    }
    
    /**
     * 验证消息格式
     * 
     * @param message WebSocket 消息
     * @return 是否有效
     */
    public boolean isValidMessage(WebSocketMessage message) {
        if (message == null || !message.hasMessage()) {
            return false;
        }
        
        BaseMessage baseMessage = message.getMessage();
        return !baseMessage.getId().isEmpty() && 
               baseMessage.getTimestamp() > 0 && 
               !baseMessage.getType().isEmpty() && 
               !baseMessage.getSource().isEmpty();
    }
    
    /**
     * 获取活跃连接数
     * 
     * @return 活跃连接数
     */
    public int getActiveConnectionCount() {
        return sessionManager.getActiveSessionCount();
    }
}