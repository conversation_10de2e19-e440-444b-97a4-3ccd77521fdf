package com.example.mqtt.websocket.service;

import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.Map;

/**
 * 健康检查服务接口
 */
public interface HealthCheckService {

    /**
     * 执行完整的健康检查
     * 
     * @return 健康检查结果
     */
    Mono<OverallHealthStatus> performHealthCheck();

    /**
     * 检查特定组件的健康状态
     * 
     * @param componentName 组件名称
     * @return 组件健康状态
     */
    Mono<ComponentHealth> checkComponentHealth(String componentName);

    /**
     * 获取所有组件的健康状态
     * 
     * @return 组件健康状态映射
     */
    Mono<Map<String, ComponentHealth>> getAllComponentsHealth();

    /**
     * 注册健康检查器
     * 
     * @param componentName 组件名称
     * @param checker 健康检查器
     */
    void registerHealthChecker(String componentName, HealthChecker checker);

    /**
     * 注销健康检查器
     * 
     * @param componentName 组件名称
     */
    void unregisterHealthChecker(String componentName);

    /**
     * 健康检查器接口
     */
    @FunctionalInterface
    interface HealthChecker {
        Mono<ComponentHealth> check();
    }

    /**
     * 组件健康状态
     */
    record ComponentHealth(
        String componentName,
        HealthStatus status,
        String message,
        Map<String, Object> details,
        Instant checkTime,
        long responseTime
    ) {}

    /**
     * 整体健康状态
     */
    record OverallHealthStatus(
        HealthStatus status,
        Map<String, ComponentHealth> components,
        Instant checkTime,
        long totalResponseTime
    ) {}

    /**
     * 健康状态枚举
     */
    enum HealthStatus {
        UP,
        DOWN,
        DEGRADED,
        UNKNOWN
    }
}