package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.MqttMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 消息桥接服务
 * 负责 MQTT 和 WebSocket 之间的消息转发和转换
 * 
 * <AUTHOR>
 */
@Service
public class MessageBridgeService {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageBridgeService.class);
    
    private final WebSocketSessionManager sessionManager;
    private final MqttPublishService mqttPublishService;
    private final EncryptionService encryptionService;
    private final MessageConverter messageConverter;
    
    @Autowired
    public MessageBridgeService(
            WebSocketSessionManager sessionManager,
            MqttPublishService mqttPublishService,
            EncryptionService encryptionService,
            MessageConverter messageConverter) {
        this.sessionManager = sessionManager;
        this.mqttPublishService = mqttPublishService;
        this.encryptionService = encryptionService;
        this.messageConverter = messageConverter;
    }
    
    /**
     * 将 MQTT 消息转发到 WebSocket 客户端
     * 
     * @param mqttMessage MQTT 消息
     * @return 转发结果
     */
    public Mono<Void> forwardMqttToWebSocket(MqttMessage mqttMessage) {
        logger.info("Forwarding MQTT message to WebSocket: topic={}, messageId={}", 
                   mqttMessage.getTopic(), mqttMessage.getMessage().getId());
        
        return Mono.fromCallable(() -> {
            // 解密消息（如果需要）
            BaseMessage baseMessage = mqttMessage.getMessage();
            if (baseMessage.getEncrypted()) {
                baseMessage = decryptMessage(baseMessage);
            }
            
            // 转换为 WebSocket 消息
            WebSocketMessage wsMessage = convertMqttToWebSocket(mqttMessage, baseMessage);
            
            return wsMessage;
        })
        .flatMap(wsMessage -> {
            // 根据消息类型决定发送方式
            return routeWebSocketMessage(wsMessage);
        })
        .doOnSuccess(v -> logger.info("Successfully forwarded MQTT message to WebSocket: messageId={}", 
                                     mqttMessage.getMessage().getId()))
        .doOnError(error -> logger.error("Failed to forward MQTT message to WebSocket: messageId={}, error={}", 
                                        mqttMessage.getMessage().getId(), error.getMessage()));
    }
    
    /**
     * 将 WebSocket 消息发布到 MQTT
     * 
     * @param wsMessage WebSocket 消息
     * @param targetTopic 目标 MQTT 主题
     * @return 发布结果
     */
    public Mono<Void> forwardWebSocketToMqtt(WebSocketMessage wsMessage, String targetTopic) {
        logger.info("Forwarding WebSocket message to MQTT: sessionId={}, topic={}, messageId={}", 
                   wsMessage.getSessionId(), targetTopic, wsMessage.getMessage().getId());
        
        return Mono.fromCallable(() -> {
            // 加密消息（如果需要）
            BaseMessage baseMessage = wsMessage.getMessage();
            if (shouldEncryptMessage(baseMessage)) {
                baseMessage = encryptMessage(baseMessage);
            }
            
            // 转换为 MQTT 消息
            MqttMessage mqttMessage = convertWebSocketToMqtt(wsMessage, baseMessage, targetTopic);
            
            return mqttMessage;
        })
        .flatMap(mqttMessage -> {
            // 发布到 MQTT
            return mqttPublishService.publishMessage(mqttMessage);
        })
        .doOnSuccess(v -> logger.info("Successfully forwarded WebSocket message to MQTT: messageId={}", 
                                     wsMessage.getMessage().getId()))
        .doOnError(error -> logger.error("Failed to forward WebSocket message to MQTT: messageId={}, error={}", 
                                        wsMessage.getMessage().getId(), error.getMessage()));
    }
    
    /**
     * 双向消息桥接 - 处理来自 MQTT 的消息并转发到 WebSocket
     * 
     * @param topic MQTT 主题
     * @param payload 消息载荷
     * @param qos QoS 级别
     * @param retained 是否保留消息
     * @return 处理结果
     */
    public Mono<Void> handleMqttMessage(String topic, byte[] payload, int qos, boolean retained) {
        logger.debug("Handling MQTT message: topic={}, payloadSize={}, qos={}, retained={}", 
                    topic, payload.length, qos, retained);
        
        return Mono.fromCallable(() -> {
            // 解析消息
            BaseMessage baseMessage = messageConverter.parseFromBytes(payload);
            
            MqttMessage mqttMessage = MqttMessage.newBuilder()
                    .setTopic(topic)
                    .setQos(qos)
                    .setRetained(retained)
                    .setMessage(baseMessage)
                    .build();
            
            return mqttMessage;
        })
        .flatMap(this::forwardMqttToWebSocket)
        .onErrorResume(error -> {
            logger.error("Error handling MQTT message: topic={}, error={}", topic, error.getMessage());
            return Mono.empty();
        });
    }
    
    /**
     * 双向消息桥接 - 处理来自 WebSocket 的消息并发布到 MQTT
     * 
     * @param sessionId WebSocket 会话ID
     * @param messageData 消息数据
     * @return 处理结果
     */
    public Mono<Void> handleWebSocketMessage(String sessionId, String messageData) {
        logger.debug("Handling WebSocket message: sessionId={}, dataSize={}", 
                    sessionId, messageData.length());
        
        return Mono.fromCallable(() -> {
            // 解析 WebSocket 消息
            WebSocketMessage wsMessage = messageConverter.parseWebSocketMessage(messageData);
            
            // 设置会话ID
            WebSocketMessage.Builder builder = wsMessage.toBuilder();
            builder.setSessionId(sessionId);
            
            return builder.build();
        })
        .flatMap(wsMessage -> {
            // 确定目标 MQTT 主题
            String targetTopic = determineMqttTopic(wsMessage);
            
            if (targetTopic != null) {
                return forwardWebSocketToMqtt(wsMessage, targetTopic);
            } else {
                logger.warn("No MQTT topic determined for WebSocket message: sessionId={}, messageId={}", 
                           sessionId, wsMessage.getMessage().getId());
                return Mono.empty();
            }
        })
        .onErrorResume(error -> {
            logger.error("Error handling WebSocket message: sessionId={}, error={}", sessionId, error.getMessage());
            return Mono.empty();
        });
    }
    
    /**
     * 转换 MQTT 消息为 WebSocket 消息
     */
    private WebSocketMessage convertMqttToWebSocket(MqttMessage mqttMessage, BaseMessage baseMessage) {
        WebSocketMessage.Builder builder = WebSocketMessage.newBuilder()
                .setMessage(baseMessage);
        
        // 根据 MQTT 主题确定 WebSocket 消息类型
        String topic = mqttMessage.getTopic();
        if (topic.contains("broadcast") || topic.contains("system")) {
            builder.setType(WebSocketMessage.MessageType.BROADCAST);
        } else if (baseMessage.getHeadersMap().containsKey("targetUserId")) {
            builder.setType(WebSocketMessage.MessageType.UNICAST)
                   .setUserId(baseMessage.getHeadersMap().get("targetUserId"));
        } else {
            builder.setType(WebSocketMessage.MessageType.BROADCAST);
        }
        
        // 添加 MQTT 特定的头信息
        BaseMessage.Builder messageBuilder = baseMessage.toBuilder();
        messageBuilder.putHeaders("mqtt.topic", topic)
                     .putHeaders("mqtt.qos", String.valueOf(mqttMessage.getQos()))
                     .putHeaders("mqtt.retained", String.valueOf(mqttMessage.getRetained()));
        
        builder.setMessage(messageBuilder.build());
        
        return builder.build();
    }
    
    /**
     * 转换 WebSocket 消息为 MQTT 消息
     */
    private MqttMessage convertWebSocketToMqtt(WebSocketMessage wsMessage, BaseMessage baseMessage, String topic) {
        // 添加 WebSocket 特定的头信息
        BaseMessage.Builder messageBuilder = baseMessage.toBuilder();
        messageBuilder.putHeaders("websocket.sessionId", wsMessage.getSessionId())
                     .putHeaders("websocket.type", wsMessage.getType().name());
        
        if (!wsMessage.getUserId().isEmpty()) {
            messageBuilder.putHeaders("websocket.userId", wsMessage.getUserId());
        }
        
        return MqttMessage.newBuilder()
                .setTopic(topic)
                .setQos(1) // 默认 QoS 1
                .setRetained(false)
                .setMessage(messageBuilder.build())
                .build();
    }
    
    /**
     * 路由 WebSocket 消息
     */
    private Mono<Void> routeWebSocketMessage(WebSocketMessage wsMessage) {
        switch (wsMessage.getType()) {
            case BROADCAST:
                return sessionManager.broadcastMessage(wsMessage);
            case UNICAST:
                return sessionManager.sendToUser(wsMessage.getUserId(), wsMessage);
            case SYSTEM:
                if (!wsMessage.getSessionId().isEmpty()) {
                    return sessionManager.sendToSession(wsMessage.getSessionId(), wsMessage);
                } else {
                    return sessionManager.broadcastMessage(wsMessage);
                }
            default:
                logger.warn("Unknown WebSocket message type: {}", wsMessage.getType());
                return Mono.empty();
        }
    }
    
    /**
     * 确定 MQTT 目标主题
     */
    private String determineMqttTopic(WebSocketMessage wsMessage) {
        BaseMessage baseMessage = wsMessage.getMessage();
        
        // 检查消息头中是否指定了目标主题
        if (baseMessage.getHeadersMap().containsKey("mqtt.targetTopic")) {
            return baseMessage.getHeadersMap().get("mqtt.targetTopic");
        }
        
        // 根据消息类型确定主题
        String messageType = baseMessage.getType();
        switch (messageType) {
            case "sensor_data":
                return "response/sensor/data";
            case "device_command":
                return "device/command/" + wsMessage.getUserId();
            case "system_notification":
                return "system/notification";
            case "broadcast":
                return "system/broadcast";
            default:
                return "response/websocket/" + messageType;
        }
    }
    
    /**
     * 判断是否需要加密消息
     */
    private boolean shouldEncryptMessage(BaseMessage message) {
        // 检查消息类型或头信息来决定是否加密
        String messageType = message.getType();
        return messageType.contains("sensitive") || 
               messageType.contains("private") ||
               message.getHeadersMap().containsKey("encrypt");
    }
    
    /**
     * 加密消息
     */
    private BaseMessage encryptMessage(BaseMessage message) {
        try {
            byte[] encryptedPayload = encryptionService.encrypt(
                message.getPayload().toByteArray(), 
                "default"
            );
            
            return message.toBuilder()
                    .setPayload(com.google.protobuf.ByteString.copyFrom(encryptedPayload))
                    .setEncrypted(true)
                    .build();
        } catch (Exception e) {
            logger.error("Failed to encrypt message: messageId={}, error={}", 
                        message.getId(), e.getMessage());
            throw new RuntimeException("Message encryption failed", e);
        }
    }
    
    /**
     * 解密消息
     */
    private BaseMessage decryptMessage(BaseMessage message) {
        try {
            byte[] decryptedPayload = encryptionService.decrypt(
                message.getPayload().toByteArray(), 
                "default"
            );
            
            return message.toBuilder()
                    .setPayload(com.google.protobuf.ByteString.copyFrom(decryptedPayload))
                    .setEncrypted(false)
                    .build();
        } catch (Exception e) {
            logger.error("Failed to decrypt message: messageId={}, error={}", 
                        message.getId(), e.getMessage());
            throw new RuntimeException("Message decryption failed", e);
        }
    }
}