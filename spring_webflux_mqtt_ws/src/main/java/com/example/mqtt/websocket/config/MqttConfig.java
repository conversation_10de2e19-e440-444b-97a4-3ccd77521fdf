package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.config.properties.MqttProperties;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;
import org.springframework.util.StringUtils;

import java.util.UUID;

/**
 * MQTT 配置类
 * 配置 MQTT 连接工厂、消息通道和适配器
 */
@Configuration
public class MqttConfig {

    @Autowired
    private MqttProperties mqttProperties;

    /**
     * MQTT 客户端工厂配置
     */
    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        
        // 基本连接配置
        options.setServerURIs(new String[]{mqttProperties.getBrokerUrl()});
        options.setConnectionTimeout(mqttProperties.getConnectionTimeout());
        options.setKeepAliveInterval(mqttProperties.getKeepAliveInterval());
        options.setCleanSession(mqttProperties.isCleanSession());
        options.setAutomaticReconnect(mqttProperties.isAutomaticReconnect());
        
        // 认证配置
        if (StringUtils.hasText(mqttProperties.getUsername())) {
            options.setUserName(mqttProperties.getUsername());
        }
        if (StringUtils.hasText(mqttProperties.getPassword())) {
            options.setPassword(mqttProperties.getPassword().toCharArray());
        }
        
        // 重连配置
        options.setMaxReconnectDelay(32000);
        options.setExecutorServiceTimeout(1000);
        
        factory.setConnectionOptions(options);
        return factory;
    }

    /**
     * MQTT 入站消息通道
     */
    @Bean
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT 出站消息通道
     */
    @Bean
    public MessageChannel mqttOutboundChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT 错误处理通道
     */
    @Bean
    public MessageChannel mqttErrorChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT 入站通道适配器 - 订阅消息
     */
    @Bean
    public MessageProducer mqttInbound() {
        String[] topics = mqttProperties.getSubscribeTopics() != null 
            ? mqttProperties.getSubscribeTopics().toArray(new String[0])
            : new String[]{"default/topic"};
            
        MqttPahoMessageDrivenChannelAdapter adapter = 
            new MqttPahoMessageDrivenChannelAdapter(
                generateClientId("subscriber"), 
                mqttClientFactory(), 
                topics);
        
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setQos(1);
        adapter.setOutputChannel(mqttInputChannel());
        adapter.setErrorChannel(mqttErrorChannel());
        
        return adapter;
    }

    /**
     * MQTT 出站消息处理器 - 发布消息
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttOutboundChannel")
    public MessageHandler mqttOutbound() {
        MqttPahoMessageHandler messageHandler = 
            new MqttPahoMessageHandler(
                generateClientId("publisher"), 
                mqttClientFactory());
        
        messageHandler.setAsync(true);
        messageHandler.setDefaultTopic("default/topic");
        messageHandler.setDefaultQos(1);
        messageHandler.setDefaultRetained(false);
        
        return messageHandler;
    }

    /**
     * 生成唯一的客户端ID
     */
    private String generateClientId(String prefix) {
        String clientId = mqttProperties.getClientId();
        if (!StringUtils.hasText(clientId)) {
            clientId = prefix + "-" + UUID.randomUUID().toString().substring(0, 8);
        }
        return clientId;
    }
}