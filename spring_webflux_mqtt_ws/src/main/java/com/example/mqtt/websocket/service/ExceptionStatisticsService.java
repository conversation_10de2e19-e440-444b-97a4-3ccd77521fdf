package com.example.mqtt.websocket.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异常统计服务
 * 收集和分析系统异常统计信息
 */
@Service
public class ExceptionStatisticsService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExceptionStatisticsService.class);
    private static final String EXCEPTION_STATS_KEY = "exception_statistics";
    
    @Autowired
    private ReactiveRedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @Autowired
    private AlertService alertService;
    
    // 异常计数器
    private final Map<String, AtomicLong> exceptionCounters = new ConcurrentHashMap<>();
    private final AtomicLong totalExceptions = new AtomicLong(0);
    
    // 异常率统计
    private final AtomicLong exceptionsInLastMinute = new AtomicLong(0);
    private final AtomicLong exceptionsInLastHour = new AtomicLong(0);
    
    // Micrometer 指标
    private Timer exceptionProcessingTimer;
    
    @PostConstruct
    public void initMetrics() {
        // 注册异常总数指标
        Gauge.builder("exceptions.total", totalExceptions, AtomicLong::get)
            .description("Total number of exceptions")
            .register(meterRegistry);
        
        // 注册异常率指标
        Gauge.builder("exceptions.rate.per.minute", exceptionsInLastMinute, AtomicLong::get)
            .description("Exception rate per minute")
            .register(meterRegistry);
        
        Gauge.builder("exceptions.rate.per.hour", exceptionsInLastHour, AtomicLong::get)
            .description("Exception rate per hour")
            .register(meterRegistry);
        
        // 注册异常处理时间指标
        exceptionProcessingTimer = Timer.builder("exception.processing.time")
            .description("Time taken to process exceptions")
            .register(meterRegistry);
    }
    
    /**
     * 记录异常
     */
    public void recordException(String exceptionType, String message, Throwable throwable) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            // 更新计数器
            exceptionCounters.computeIfAbsent(exceptionType, k -> new AtomicLong(0)).incrementAndGet();
            totalExceptions.incrementAndGet();
            exceptionsInLastMinute.incrementAndGet();
            exceptionsInLastHour.incrementAndGet();
            
            // 更新 Micrometer 指标
            meterRegistry.counter("exceptions.by.type", "type", exceptionType).increment();
            
            // 创建异常记录
            ExceptionRecord record = new ExceptionRecord(exceptionType, message, throwable);
            
            // 异步存储到 Redis
            storeExceptionRecord(record)
                .doOnSuccess(result -> logger.debug("Exception record stored: {}", record.getId()))
                .doOnError(error -> logger.error("Failed to store exception record", error))
                .subscribe();
            
            // 检查异常阈值
            checkExceptionThresholds(exceptionType);
            
        } finally {
            sample.stop(exceptionProcessingTimer);
        }
    }
    
    /**
     * 存储异常记录
     */
    private Mono<Boolean> storeExceptionRecord(ExceptionRecord record) {
        Map<String, Object> recordMap = new HashMap<>();
        recordMap.put("id", record.getId());
        recordMap.put("type", record.getType());
        recordMap.put("message", record.getMessage());
        recordMap.put("timestamp", record.getTimestamp().toString());
        recordMap.put("stackTrace", record.getStackTrace());
        
        return redisTemplate.opsForHash()
            .putAll(EXCEPTION_STATS_KEY + ":" + record.getId(), recordMap)
            .then(Mono.just(true));
    }
    
    /**
     * 检查异常阈值
     */
    private void checkExceptionThresholds(String exceptionType) {
        long count = exceptionCounters.get(exceptionType).get();
        
        // 检查单个异常类型阈值
        if (count > 0 && count % 10 == 0) {
            alertService.sendAlert(
                AlertService.AlertLevel.HIGH,
                "Exception Threshold Exceeded",
                String.format("Exception type %s has occurred %d times", exceptionType, count),
                "EXCEPTION_THRESHOLD"
            );
        }
        
        // 检查总异常数阈值
        long total = totalExceptions.get();
        if (total > 0 && total % 50 == 0) {
            alertService.sendAlert(
                AlertService.AlertLevel.CRITICAL,
                "Total Exception Threshold Exceeded",
                String.format("Total exceptions have reached %d", total),
                "TOTAL_EXCEPTION_THRESHOLD"
            );
        }
        
        // 检查异常率阈值
        long ratePerMinute = exceptionsInLastMinute.get();
        if (ratePerMinute > 20) {
            alertService.sendAlert(
                AlertService.AlertLevel.CRITICAL,
                "High Exception Rate",
                String.format("Exception rate is %d per minute", ratePerMinute),
                "HIGH_EXCEPTION_RATE"
            );
        }
    }
    
    /**
     * 获取异常统计信息
     */
    public ExceptionStatistics getExceptionStatistics() {
        Map<String, Long> typeCounters = new HashMap<>();
        exceptionCounters.forEach((type, counter) -> typeCounters.put(type, counter.get()));
        
        return new ExceptionStatistics(
            totalExceptions.get(),
            exceptionsInLastMinute.get(),
            exceptionsInLastHour.get(),
            typeCounters
        );
    }
    
    /**
     * 重置异常统计
     */
    public void resetStatistics() {
        logger.info("Resetting exception statistics");
        
        exceptionCounters.clear();
        totalExceptions.set(0);
        exceptionsInLastMinute.set(0);
        exceptionsInLastHour.set(0);
        
        // 清理 Redis 中的统计数据
        redisTemplate.delete(EXCEPTION_STATS_KEY + "*")
            .doOnSuccess(result -> logger.info("Exception statistics cleared from Redis"))
            .subscribe();
    }
    
    /**
     * 定时重置分钟级异常计数
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void resetMinuteCounter() {
        exceptionsInLastMinute.set(0);
        logger.debug("Reset minute exception counter");
    }
    
    /**
     * 定时重置小时级异常计数
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void resetHourCounter() {
        exceptionsInLastHour.set(0);
        logger.debug("Reset hour exception counter");
    }
    
    /**
     * 定时生成异常报告
     */
    @Scheduled(cron = "0 0 * * * *") // 每小时执行一次
    public void generateExceptionReport() {
        ExceptionStatistics stats = getExceptionStatistics();
        
        logger.info("Exception Report - Total: {}, Last Hour: {}, By Type: {}", 
            stats.getTotalExceptions(), 
            stats.getExceptionsInLastHour(), 
            stats.getExceptionsByType());
        
        // 如果异常数量较高，发送报告告警
        if (stats.getExceptionsInLastHour() > 100) {
            alertService.sendAlert(
                AlertService.AlertLevel.MEDIUM,
                "Hourly Exception Report",
                String.format("High exception count in last hour: %d", stats.getExceptionsInLastHour()),
                "EXCEPTION_REPORT"
            );
        }
    }
    
    /**
     * 异常记录类
     */
    private static class ExceptionRecord {
        private final String id;
        private final String type;
        private final String message;
        private final LocalDateTime timestamp;
        private final String stackTrace;
        
        public ExceptionRecord(String type, String message, Throwable throwable) {
            this.id = generateId();
            this.type = type;
            this.message = message;
            this.timestamp = LocalDateTime.now();
            this.stackTrace = getStackTrace(throwable);
        }
        
        private String generateId() {
            return "EXC_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
        }
        
        private String getStackTrace(Throwable throwable) {
            if (throwable == null) return "";
            
            StringBuilder sb = new StringBuilder();
            sb.append(throwable.toString()).append("\n");
            for (StackTraceElement element : throwable.getStackTrace()) {
                sb.append("\tat ").append(element.toString()).append("\n");
            }
            return sb.toString();
        }
        
        public String getId() { return id; }
        public String getType() { return type; }
        public String getMessage() { return message; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public String getStackTrace() { return stackTrace; }
    }
    
    /**
     * 异常统计信息类
     */
    public static class ExceptionStatistics {
        private final long totalExceptions;
        private final long exceptionsInLastMinute;
        private final long exceptionsInLastHour;
        private final Map<String, Long> exceptionsByType;
        
        public ExceptionStatistics(long totalExceptions, long exceptionsInLastMinute, 
                                 long exceptionsInLastHour, Map<String, Long> exceptionsByType) {
            this.totalExceptions = totalExceptions;
            this.exceptionsInLastMinute = exceptionsInLastMinute;
            this.exceptionsInLastHour = exceptionsInLastHour;
            this.exceptionsByType = new HashMap<>(exceptionsByType);
        }
        
        public long getTotalExceptions() { return totalExceptions; }
        public long getExceptionsInLastMinute() { return exceptionsInLastMinute; }
        public long getExceptionsInLastHour() { return exceptionsInLastHour; }
        public Map<String, Long> getExceptionsByType() { return exceptionsByType; }
    }
}