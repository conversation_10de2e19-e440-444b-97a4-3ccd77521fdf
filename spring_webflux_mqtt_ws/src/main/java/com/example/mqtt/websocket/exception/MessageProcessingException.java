package com.example.mqtt.websocket.exception;

/**
 * 消息处理异常
 */
public class MessageProcessingException extends RuntimeException {
    
    private final String messageId;
    private final String messageType;
    
    public MessageProcessingException(String messageId, String messageType, String message) {
        super(String.format("Message processing failed for %s message %s: %s", messageType, messageId, message));
        this.messageId = messageId;
        this.messageType = messageType;
    }
    
    public MessageProcessingException(String messageId, String messageType, String message, Throwable cause) {
        super(String.format("Message processing failed for %s message %s: %s", messageType, messageId, message), cause);
        this.messageId = messageId;
        this.messageType = messageType;
    }
    
    public String getMessageId() {
        return messageId;
    }
    
    public String getMessageType() {
        return messageType;
    }
}