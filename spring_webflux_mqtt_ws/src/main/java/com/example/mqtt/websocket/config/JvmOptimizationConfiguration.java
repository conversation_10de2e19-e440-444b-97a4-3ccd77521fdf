package com.example.mqtt.websocket.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.List;

/**
 * JVM 优化配置
 * 提供 JVM 参数建议和运行时优化配置
 */
@Configuration
@ConfigurationProperties(prefix = "app.jvm")
public class JvmOptimizationConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(JvmOptimizationConfiguration.class);

    // GC 配置建议
    private boolean enableG1GC = true;
    private int maxGCPauseMillis = 200;
    private int g1HeapRegionSize = 16; // MB
    private double g1MixedGCCountTarget = 8;
    private double g1OldCSetRegionThreshold = 10;

    // 内存配置建议
    private String heapSize = "2g";
    private String newRatio = "3";
    private String survivorRatio = "8";
    private int maxDirectMemorySize = 512; // MB

    // 性能调优参数
    private boolean enableCompressedOops = true;
    private boolean enableTieredCompilation = true;
    private int compileThreshold = 10000;
    private boolean enableAggressiveOpts = false;

    @PostConstruct
    public void logCurrentJvmSettings() {
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        List<String> jvmArgs = runtimeMXBean.getInputArguments();
        
        logger.info("Current JVM arguments:");
        jvmArgs.forEach(arg -> logger.info("  {}", arg));
        
        logger.info("JVM Information:");
        logger.info("  Java Version: {}", System.getProperty("java.version"));
        logger.info("  JVM Name: {}", runtimeMXBean.getVmName());
        logger.info("  JVM Version: {}", runtimeMXBean.getVmVersion());
        logger.info("  Available Processors: {}", Runtime.getRuntime().availableProcessors());
        logger.info("  Max Memory: {} MB", Runtime.getRuntime().maxMemory() / 1024 / 1024);
        logger.info("  Total Memory: {} MB", Runtime.getRuntime().totalMemory() / 1024 / 1024);
        logger.info("  Free Memory: {} MB", Runtime.getRuntime().freeMemory() / 1024 / 1024);
        
        // 检查是否使用了推荐的 JVM 参数
        checkJvmOptimizations(jvmArgs);
        
        // 输出推荐的 JVM 参数
        logRecommendedJvmArgs();
    }

    /**
     * 检查当前 JVM 优化设置
     */
    private void checkJvmOptimizations(List<String> jvmArgs) {
        boolean hasG1GC = jvmArgs.stream().anyMatch(arg -> arg.contains("UseG1GC"));
        boolean hasHeapSize = jvmArgs.stream().anyMatch(arg -> arg.startsWith("-Xmx"));
        boolean hasCompressedOops = jvmArgs.stream().anyMatch(arg -> arg.contains("UseCompressedOops"));
        
        logger.info("JVM Optimization Check:");
        logger.info("  G1GC Enabled: {}", hasG1GC ? "✓" : "✗ (Recommended: -XX:+UseG1GC)");
        logger.info("  Heap Size Set: {}", hasHeapSize ? "✓" : "✗ (Recommended: -Xmx" + heapSize + ")");
        logger.info("  Compressed OOPs: {}", hasCompressedOops ? "✓" : "✗ (Recommended: -XX:+UseCompressedOops)");
        
        if (!hasG1GC || !hasHeapSize) {
            logger.warn("Some recommended JVM optimizations are not applied. See recommended settings below.");
        }
    }

    /**
     * 输出推荐的 JVM 参数
     */
    private void logRecommendedJvmArgs() {
        logger.info("Recommended JVM Arguments for Production:");
        
        // 内存设置
        logger.info("Memory Settings:");
        logger.info("  -Xmx{} -Xms{}", heapSize, heapSize);
        logger.info("  -XX:MaxDirectMemorySize={}m", maxDirectMemorySize);
        logger.info("  -XX:NewRatio={}", newRatio);
        logger.info("  -XX:SurvivorRatio={}", survivorRatio);
        
        // G1GC 设置
        if (enableG1GC) {
            logger.info("G1GC Settings:");
            logger.info("  -XX:+UseG1GC");
            logger.info("  -XX:MaxGCPauseMillis={}", maxGCPauseMillis);
            logger.info("  -XX:G1HeapRegionSize={}m", g1HeapRegionSize);
            logger.info("  -XX:G1MixedGCCountTarget={}", (int) g1MixedGCCountTarget);
            logger.info("  -XX:G1OldCSetRegionThreshold={}", (int) g1OldCSetRegionThreshold);
        }
        
        // 性能优化设置
        logger.info("Performance Settings:");
        if (enableCompressedOops) {
            logger.info("  -XX:+UseCompressedOops");
        }
        if (enableTieredCompilation) {
            logger.info("  -XX:+TieredCompilation");
            logger.info("  -XX:CompileThreshold={}", compileThreshold);
        }
        if (enableAggressiveOpts) {
            logger.info("  -XX:+AggressiveOpts");
        }
        
        // GC 日志设置
        logger.info("GC Logging:");
        logger.info("  -Xlog:gc*:gc.log:time,tags");
        logger.info("  -XX:+UseGCLogFileRotation");
        logger.info("  -XX:NumberOfGCLogFiles=5");
        logger.info("  -XX:GCLogFileSize=10M");
        
        // 其他推荐设置
        logger.info("Other Recommendations:");
        logger.info("  -server");
        logger.info("  -XX:+HeapDumpOnOutOfMemoryError");
        logger.info("  -XX:HeapDumpPath=./heapdump.hprof");
        logger.info("  -XX:+PrintGCDetails");
        logger.info("  -XX:+PrintGCTimeStamps");
        logger.info("  -XX:+PrintGCApplicationStoppedTime");
    }

    /**
     * 获取完整的推荐 JVM 参数字符串
     */
    public String getRecommendedJvmArgs() {
        StringBuilder args = new StringBuilder();
        
        // 基础设置
        args.append("-server ");
        args.append("-Xmx").append(heapSize).append(" ");
        args.append("-Xms").append(heapSize).append(" ");
        args.append("-XX:MaxDirectMemorySize=").append(maxDirectMemorySize).append("m ");
        
        // G1GC 设置
        if (enableG1GC) {
            args.append("-XX:+UseG1GC ");
            args.append("-XX:MaxGCPauseMillis=").append(maxGCPauseMillis).append(" ");
            args.append("-XX:G1HeapRegionSize=").append(g1HeapRegionSize).append("m ");
            args.append("-XX:G1MixedGCCountTarget=").append((int) g1MixedGCCountTarget).append(" ");
            args.append("-XX:G1OldCSetRegionThreshold=").append((int) g1OldCSetRegionThreshold).append(" ");
        }
        
        // 性能优化
        if (enableCompressedOops) {
            args.append("-XX:+UseCompressedOops ");
        }
        if (enableTieredCompilation) {
            args.append("-XX:+TieredCompilation ");
            args.append("-XX:CompileThreshold=").append(compileThreshold).append(" ");
        }
        if (enableAggressiveOpts) {
            args.append("-XX:+AggressiveOpts ");
        }
        
        // 内存比例设置
        args.append("-XX:NewRatio=").append(newRatio).append(" ");
        args.append("-XX:SurvivorRatio=").append(survivorRatio).append(" ");
        
        // 诊断和日志
        args.append("-XX:+HeapDumpOnOutOfMemoryError ");
        args.append("-XX:HeapDumpPath=./heapdump.hprof ");
        args.append("-Xlog:gc*:gc.log:time,tags ");
        
        return args.toString().trim();
    }

    // Getters and Setters
    public boolean isEnableG1GC() { return enableG1GC; }
    public void setEnableG1GC(boolean enableG1GC) { this.enableG1GC = enableG1GC; }

    public int getMaxGCPauseMillis() { return maxGCPauseMillis; }
    public void setMaxGCPauseMillis(int maxGCPauseMillis) { this.maxGCPauseMillis = maxGCPauseMillis; }

    public int getG1HeapRegionSize() { return g1HeapRegionSize; }
    public void setG1HeapRegionSize(int g1HeapRegionSize) { this.g1HeapRegionSize = g1HeapRegionSize; }

    public double getG1MixedGCCountTarget() { return g1MixedGCCountTarget; }
    public void setG1MixedGCCountTarget(double g1MixedGCCountTarget) { 
        this.g1MixedGCCountTarget = g1MixedGCCountTarget; 
    }

    public double getG1OldCSetRegionThreshold() { return g1OldCSetRegionThreshold; }
    public void setG1OldCSetRegionThreshold(double g1OldCSetRegionThreshold) { 
        this.g1OldCSetRegionThreshold = g1OldCSetRegionThreshold; 
    }

    public String getHeapSize() { return heapSize; }
    public void setHeapSize(String heapSize) { this.heapSize = heapSize; }

    public String getNewRatio() { return newRatio; }
    public void setNewRatio(String newRatio) { this.newRatio = newRatio; }

    public String getSurvivorRatio() { return survivorRatio; }
    public void setSurvivorRatio(String survivorRatio) { this.survivorRatio = survivorRatio; }

    public int getMaxDirectMemorySize() { return maxDirectMemorySize; }
    public void setMaxDirectMemorySize(int maxDirectMemorySize) { 
        this.maxDirectMemorySize = maxDirectMemorySize; 
    }

    public boolean isEnableCompressedOops() { return enableCompressedOops; }
    public void setEnableCompressedOops(boolean enableCompressedOops) { 
        this.enableCompressedOops = enableCompressedOops; 
    }

    public boolean isEnableTieredCompilation() { return enableTieredCompilation; }
    public void setEnableTieredCompilation(boolean enableTieredCompilation) { 
        this.enableTieredCompilation = enableTieredCompilation; 
    }

    public int getCompileThreshold() { return compileThreshold; }
    public void setCompileThreshold(int compileThreshold) { this.compileThreshold = compileThreshold; }

    public boolean isEnableAggressiveOpts() { return enableAggressiveOpts; }
    public void setEnableAggressiveOpts(boolean enableAggressiveOpts) { 
        this.enableAggressiveOpts = enableAggressiveOpts; 
    }
}