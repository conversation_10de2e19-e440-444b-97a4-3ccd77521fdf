package com.example.mqtt.websocket.service;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.Map;
import java.util.Set;

/**
 * 集群状态管理接口
 * 负责实例注册、发现、健康检查和状态同步
 */
public interface ClusterStateManager {

    /**
     * 注册当前实例到集群
     * 
     * @param instanceId 实例ID
     * @param instanceInfo 实例信息
     * @return 注册结果
     */
    Mono<Void> registerInstance(String instanceId, InstanceInfo instanceInfo);

    /**
     * 注销当前实例
     * 
     * @param instanceId 实例ID
     * @return 注销结果
     */
    Mono<Void> unregisterInstance(String instanceId);

    /**
     * 更新实例心跳
     * 
     * @param instanceId 实例ID
     * @return 更新结果
     */
    Mono<Void> updateHeartbeat(String instanceId);

    /**
     * 获取所有活跃实例
     * 
     * @return 活跃实例集合
     */
    Mono<Set<String>> getActiveInstances();

    /**
     * 获取实例详细信息
     * 
     * @param instanceId 实例ID
     * @return 实例信息
     */
    Mono<InstanceInfo> getInstanceInfo(String instanceId);

    /**
     * 获取所有实例信息
     * 
     * @return 实例信息映射
     */
    Mono<Map<String, InstanceInfo>> getAllInstancesInfo();

    /**
     * 广播消息到集群中的所有实例
     * 
     * @param message 消息内容
     * @return 广播结果
     */
    Mono<Void> broadcastToCluster(Object message);

    /**
     * 监听集群消息
     * 
     * @return 消息流
     */
    Flux<ClusterMessage> listenClusterMessages();

    /**
     * 检查实例是否健康
     * 
     * @param instanceId 实例ID
     * @return 健康状态
     */
    Mono<Boolean> isInstanceHealthy(String instanceId);

    /**
     * 清理过期实例
     * 
     * @return 清理的实例数量
     */
    Mono<Long> cleanupExpiredInstances();

    /**
     * 实例信息
     */
    record InstanceInfo(
        String instanceId,
        String host,
        int port,
        Instant startTime,
        Instant lastHeartbeat,
        Map<String, Object> metadata
    ) {}

    /**
     * 集群消息
     */
    record ClusterMessage(
        String messageId,
        String sourceInstanceId,
        String messageType,
        Object payload,
        Instant timestamp
    ) {}
}