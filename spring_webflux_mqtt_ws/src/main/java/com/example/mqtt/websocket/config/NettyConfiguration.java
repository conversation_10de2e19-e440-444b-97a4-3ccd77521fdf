package com.example.mqtt.websocket.config;

import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.netty.resources.LoopResources;

import java.time.Duration;

/**
 * Netty 性能优化配置
 * 优化线程池、连接池和资源管理
 */
@Configuration
@ConfigurationProperties(prefix = "app.netty")
public class NettyConfiguration {

    private int bossThreads = 1;
    private int workerThreads = Runtime.getRuntime().availableProcessors() * 2;
    private int maxConnections = 1000;
    private int pendingAcquireMaxCount = 2000;
    private Duration pendingAcquireTimeout = Duration.ofSeconds(45);
    private Duration maxIdleTime = Duration.ofSeconds(30);
    private Duration maxLifeTime = Duration.ofMinutes(30);
    private boolean keepAlive = true;
    private boolean tcpNoDelay = true;
    private int soBacklog = 1024;

    /**
     * 配置 Netty EventLoopGroup 用于 WebSocket 服务器
     */
    @Bean(name = "bossEventLoopGroup", destroyMethod = "shutdownGracefully")
    public EventLoopGroup bossEventLoopGroup() {
        return new NioEventLoopGroup(bossThreads, 
            new DefaultThreadFactory("netty-boss", true));
    }

    @Bean(name = "workerEventLoopGroup", destroyMethod = "shutdownGracefully")
    public EventLoopGroup workerEventLoopGroup() {
        return new NioEventLoopGroup(workerThreads, 
            new DefaultThreadFactory("netty-worker", true));
    }

    /**
     * 配置 Reactor Netty 连接池
     */
    @Bean
    public ConnectionProvider connectionProvider() {
        return ConnectionProvider.builder("custom-pool")
            .maxConnections(maxConnections)
            .pendingAcquireMaxCount(pendingAcquireMaxCount)
            .pendingAcquireTimeout(pendingAcquireTimeout)
            .maxIdleTime(maxIdleTime)
            .maxLifeTime(maxLifeTime)
            .build();
    }

    /**
     * 配置 Reactor Netty LoopResources
     */
    @Bean
    public LoopResources loopResources() {
        return LoopResources.create("reactor-http", workerThreads, true);
    }

    /**
     * 配置优化的 HttpClient
     */
    @Bean
    public HttpClient httpClient(ConnectionProvider connectionProvider, 
                                LoopResources loopResources) {
        return HttpClient.create(connectionProvider)
            .runOn(loopResources)
            .option(io.netty.channel.ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
            .option(io.netty.channel.ChannelOption.SO_KEEPALIVE, keepAlive)
            .option(io.netty.channel.ChannelOption.TCP_NODELAY, tcpNoDelay)
            .option(io.netty.channel.ChannelOption.SO_BACKLOG, soBacklog)
            .compress(true)
            .keepAlive(true);
    }

    /**
     * 配置优化的 WebClient
     */
    @Bean
    public WebClient webClient(HttpClient httpClient) {
        return WebClient.builder()
            .clientConnector(new ReactorClientHttpConnector(httpClient))
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
            .build();
    }

    // Getters and Setters
    public int getBossThreads() { return bossThreads; }
    public void setBossThreads(int bossThreads) { this.bossThreads = bossThreads; }

    public int getWorkerThreads() { return workerThreads; }
    public void setWorkerThreads(int workerThreads) { this.workerThreads = workerThreads; }

    public int getMaxConnections() { return maxConnections; }
    public void setMaxConnections(int maxConnections) { this.maxConnections = maxConnections; }

    public int getPendingAcquireMaxCount() { return pendingAcquireMaxCount; }
    public void setPendingAcquireMaxCount(int pendingAcquireMaxCount) { 
        this.pendingAcquireMaxCount = pendingAcquireMaxCount; 
    }

    public Duration getPendingAcquireTimeout() { return pendingAcquireTimeout; }
    public void setPendingAcquireTimeout(Duration pendingAcquireTimeout) { 
        this.pendingAcquireTimeout = pendingAcquireTimeout; 
    }

    public Duration getMaxIdleTime() { return maxIdleTime; }
    public void setMaxIdleTime(Duration maxIdleTime) { this.maxIdleTime = maxIdleTime; }

    public Duration getMaxLifeTime() { return maxLifeTime; }
    public void setMaxLifeTime(Duration maxLifeTime) { this.maxLifeTime = maxLifeTime; }

    public boolean isKeepAlive() { return keepAlive; }
    public void setKeepAlive(boolean keepAlive) { this.keepAlive = keepAlive; }

    public boolean isTcpNoDelay() { return tcpNoDelay; }
    public void setTcpNoDelay(boolean tcpNoDelay) { this.tcpNoDelay = tcpNoDelay; }

    public int getSoBacklog() { return soBacklog; }
    public void setSoBacklog(int soBacklog) { this.soBacklog = soBacklog; }
}