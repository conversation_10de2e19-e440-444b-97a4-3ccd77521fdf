package com.example.mqtt.websocket.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket 连接管理器
 * 负责连接数限制、资源清理和会话管理
 */
@Service
public class WebSocketConnectionManager {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketConnectionManager.class);

    private final ConcurrentHashMap<String, WebSocketSession> activeSessions = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Instant> sessionLastActivity = new ConcurrentHashMap<>();
    private final AtomicInteger connectionCount = new AtomicInteger(0);
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(
        r -> new Thread(r, "websocket-cleanup"));

    @Value("${app.websocket.max-connections:1000}")
    private int maxConnections;

    @Value("${app.websocket.session-timeout:300}")
    private int sessionTimeoutSeconds;

    @Value("${app.websocket.cleanup-interval:60}")
    private int cleanupIntervalSeconds;

    public WebSocketConnectionManager() {
        // 启动定期清理任务
        cleanupExecutor.scheduleAtFixedRate(
            this::cleanupInactiveSessions,
            cleanupIntervalSeconds,
            cleanupIntervalSeconds,
            TimeUnit.SECONDS
        );
    }

    /**
     * 尝试添加新的 WebSocket 连接
     */
    public boolean addConnection(WebSocketSession session) {
        if (connectionCount.get() >= maxConnections) {
            logger.warn("WebSocket connection limit reached: {}/{}", connectionCount.get(), maxConnections);
            return false;
        }

        String sessionId = session.getId();
        activeSessions.put(sessionId, session);
        sessionLastActivity.put(sessionId, Instant.now());
        int currentCount = connectionCount.incrementAndGet();
        
        logger.info("WebSocket connection added: {} (total: {})", sessionId, currentCount);
        return true;
    }

    /**
     * 移除 WebSocket 连接
     */
    public void removeConnection(String sessionId) {
        WebSocketSession session = activeSessions.remove(sessionId);
        sessionLastActivity.remove(sessionId);
        
        if (session != null) {
            int currentCount = connectionCount.decrementAndGet();
            logger.info("WebSocket connection removed: {} (total: {})", sessionId, currentCount);
            
            // 确保会话关闭
            if (!session.isOpen()) {
                return;
            }
            
            session.close()
                .doOnSuccess(v -> logger.debug("WebSocket session closed: {}", sessionId))
                .doOnError(e -> logger.warn("Error closing WebSocket session {}: {}", sessionId, e.getMessage()))
                .subscribe();
        }
    }

    /**
     * 更新会话活动时间
     */
    public void updateActivity(String sessionId) {
        if (activeSessions.containsKey(sessionId)) {
            sessionLastActivity.put(sessionId, Instant.now());
        }
    }

    /**
     * 获取活跃会话
     */
    public WebSocketSession getSession(String sessionId) {
        return activeSessions.get(sessionId);
    }

    /**
     * 获取当前连接数
     */
    public int getConnectionCount() {
        return connectionCount.get();
    }

    /**
     * 获取最大连接数
     */
    public int getMaxConnections() {
        return maxConnections;
    }

    /**
     * 检查是否可以接受新连接
     */
    public boolean canAcceptConnection() {
        return connectionCount.get() < maxConnections;
    }

    /**
     * 广播消息到所有活跃会话
     */
    public Mono<Void> broadcastToAll(String message) {
        return Mono.fromRunnable(() -> {
            activeSessions.values().parallelStream()
                .filter(WebSocketSession::isOpen)
                .forEach(session -> {
                    session.send(Mono.just(session.textMessage(message)))
                        .doOnError(e -> logger.warn("Error broadcasting to session {}: {}", 
                            session.getId(), e.getMessage()))
                        .subscribe();
                });
        });
    }

    /**
     * 发送消息到指定会话
     */
    public Mono<Void> sendToSession(String sessionId, String message) {
        WebSocketSession session = activeSessions.get(sessionId);
        if (session != null && session.isOpen()) {
            return session.send(Mono.just(session.textMessage(message)))
                .doOnSuccess(v -> updateActivity(sessionId))
                .doOnError(e -> logger.warn("Error sending to session {}: {}", sessionId, e.getMessage()));
        }
        return Mono.empty();
    }

    /**
     * 清理不活跃的会话
     */
    private void cleanupInactiveSessions() {
        Instant cutoff = Instant.now().minus(Duration.ofSeconds(sessionTimeoutSeconds));
        
        sessionLastActivity.entrySet().removeIf(entry -> {
            String sessionId = entry.getKey();
            Instant lastActivity = entry.getValue();
            
            if (lastActivity.isBefore(cutoff)) {
                logger.info("Cleaning up inactive WebSocket session: {}", sessionId);
                removeConnection(sessionId);
                return true;
            }
            return false;
        });
    }

    /**
     * 获取连接统计信息
     */
    public ConnectionStats getConnectionStats() {
        return new ConnectionStats(
            connectionCount.get(),
            maxConnections,
            activeSessions.size(),
            sessionLastActivity.size()
        );
    }

    /**
     * 连接统计信息
     */
    public static class ConnectionStats {
        private final int activeConnections;
        private final int maxConnections;
        private final int sessionCount;
        private final int activityTrackingCount;

        public ConnectionStats(int activeConnections, int maxConnections, 
                             int sessionCount, int activityTrackingCount) {
            this.activeConnections = activeConnections;
            this.maxConnections = maxConnections;
            this.sessionCount = sessionCount;
            this.activityTrackingCount = activityTrackingCount;
        }

        public int getActiveConnections() { return activeConnections; }
        public int getMaxConnections() { return maxConnections; }
        public int getSessionCount() { return sessionCount; }
        public int getActivityTrackingCount() { return activityTrackingCount; }
        
        public double getConnectionUtilization() {
            return maxConnections > 0 ? (double) activeConnections / maxConnections : 0.0;
        }
    }

    /**
     * 关闭连接管理器
     */
    public void shutdown() {
        logger.info("Shutting down WebSocket connection manager");
        
        // 关闭所有活跃连接
        activeSessions.values().forEach(session -> {
            if (session.isOpen()) {
                session.close().subscribe();
            }
        });
        
        activeSessions.clear();
        sessionLastActivity.clear();
        cleanupExecutor.shutdown();
        
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}