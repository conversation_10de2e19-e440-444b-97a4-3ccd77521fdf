package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.config.properties.MqttProperties;
import org.eclipse.paho.client.mqttv3.IMqttClient;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * MQTT 连接监控服务
 * 监控 MQTT 连接状态并处理重连逻辑
 */
@Service
public class MqttConnectionMonitor {

    private static final Logger logger = LoggerFactory.getLogger(MqttConnectionMonitor.class);

    @Autowired
    private MqttProperties mqttProperties;

    @Autowired
    private MqttPahoClientFactory mqttClientFactory;

    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicBoolean isReconnecting = new AtomicBoolean(false);

    @PostConstruct
    public void initialize() {
        logger.info("Initializing MQTT Connection Monitor");
        // 初始化连接状态监控
    }

    /**
     * 定期检查 MQTT 连接状态
     */
    @Scheduled(fixedDelay = 30000) // 每30秒检查一次
    public void checkConnectionStatus() {
        try {
            // 这里可以通过 Spring Integration 的 MqttPahoClientFactory 检查连接状态
            // 由于 Spring Integration 已经处理了自动重连，这里主要用于监控和日志记录
            
            if (mqttProperties.isAutomaticReconnect()) {
                logger.debug("MQTT automatic reconnect is enabled");
            }
            
            // 可以添加自定义的健康检查逻辑
            performHealthCheck();
            
        } catch (Exception e) {
            logger.error("Error during MQTT connection status check", e);
        }
    }

    /**
     * 执行 MQTT 连接健康检查
     */
    private void performHealthCheck() {
        // 可以发送心跳消息或检查特定主题的可用性
        logger.debug("Performing MQTT connection health check");
        
        // 这里可以实现自定义的健康检查逻辑
        // 例如：发送测试消息、检查订阅状态等
    }

    /**
     * 处理连接丢失事件
     */
    public void onConnectionLost(Throwable cause) {
        logger.warn("MQTT connection lost: {}", cause.getMessage());
        isConnected.set(false);
        
        if (mqttProperties.isAutomaticReconnect() && !isReconnecting.get()) {
            logger.info("Attempting to reconnect to MQTT broker...");
            isReconnecting.set(true);
            
            // Spring Integration 的自动重连机制会处理重连
            // 这里可以添加额外的重连逻辑或通知
        }
    }

    /**
     * 处理连接成功事件
     */
    public void onConnectionEstablished() {
        logger.info("MQTT connection established successfully");
        isConnected.set(true);
        isReconnecting.set(false);
    }

    /**
     * 获取连接状态
     */
    public boolean isConnected() {
        return isConnected.get();
    }

    /**
     * 获取重连状态
     */
    public boolean isReconnecting() {
        return isReconnecting.get();
    }

    @PreDestroy
    public void cleanup() {
        logger.info("Shutting down MQTT Connection Monitor");
        // 清理资源
    }

    /**
     * MQTT 回调实现
     * 用于监听连接状态变化
     */
    public static class ConnectionMonitorCallback implements MqttCallback {
        
        private final MqttConnectionMonitor monitor;
        
        public ConnectionMonitorCallback(MqttConnectionMonitor monitor) {
            this.monitor = monitor;
        }

        @Override
        public void connectionLost(Throwable cause) {
            monitor.onConnectionLost(cause);
        }

        @Override
        public void messageArrived(String topic, MqttMessage message) throws Exception {
            // 消息到达处理由 Spring Integration 处理
        }

        @Override
        public void deliveryComplete(org.eclipse.paho.client.mqttv3.IMqttDeliveryToken token) {
            // 消息发送完成
        }
    }
}