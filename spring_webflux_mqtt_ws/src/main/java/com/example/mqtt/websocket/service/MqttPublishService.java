package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.model.proto.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * MQTT 消息发布服务
 * 负责将消息发布到 MQTT 代理
 * 
 * <AUTHOR>
 */
@Service
public class MqttPublishService {
    
    private static final Logger logger = LoggerFactory.getLogger(MqttPublishService.class);
    
    private final MqttGateway mqttGateway;
    private final MessageConverter messageConverter;
    
    @Autowired
    public MqttPublishService(MqttGateway mqttGateway, MessageConverter messageConverter) {
        this.mqttGateway = mqttGateway;
        this.messageConverter = messageConverter;
    }
    
    /**
     * 发布 MQTT 消息
     * 
     * @param mqttMessage MQTT 消息
     * @return 发布结果
     */
    public Mono<Void> publishMessage(MqttMessage mqttMessage) {
        return Mono.fromRunnable(() -> {
            try {
                String topic = mqttMessage.getTopic();
                byte[] payload = messageConverter.convertToBytes(mqttMessage.getMessage());
                int qos = mqttMessage.getQos();
                boolean retained = mqttMessage.getRetained();
                
                logger.debug("Publishing MQTT message: topic={}, payloadSize={}, qos={}, retained={}, messageId={}", 
                           topic, payload.length, qos, retained, mqttMessage.getMessage().getId());
                
                mqttGateway.publishMessage(topic, payload, qos, retained);
                
                logger.info("Successfully published MQTT message: topic={}, messageId={}", 
                           topic, mqttMessage.getMessage().getId());
                
            } catch (Exception e) {
                logger.error("Failed to publish MQTT message: topic={}, messageId={}, error={}", 
                           mqttMessage.getTopic(), mqttMessage.getMessage().getId(), e.getMessage());
                throw new RuntimeException("MQTT message publish failed", e);
            }
        });
    }
    
    /**
     * 发布简单的文本消息到 MQTT
     * 
     * @param topic MQTT 主题
     * @param message 消息内容
     * @return 发布结果
     */
    public Mono<Void> publishTextMessage(String topic, String message) {
        return Mono.fromRunnable(() -> {
            try {
                logger.debug("Publishing text message to MQTT: topic={}, messageLength={}", 
                           topic, message.length());
                
                mqttGateway.publishTextMessage(topic, message);
                
                logger.info("Successfully published text message to MQTT: topic={}", topic);
                
            } catch (Exception e) {
                logger.error("Failed to publish text message to MQTT: topic={}, error={}", 
                           topic, e.getMessage());
                throw new RuntimeException("MQTT text message publish failed", e);
            }
        });
    }
    
    /**
     * 发布字节数组消息到 MQTT
     * 
     * @param topic MQTT 主题
     * @param payload 消息载荷
     * @param qos QoS 级别
     * @param retained 是否保留消息
     * @return 发布结果
     */
    public Mono<Void> publishBytesMessage(String topic, byte[] payload, int qos, boolean retained) {
        return Mono.fromRunnable(() -> {
            try {
                logger.debug("Publishing bytes message to MQTT: topic={}, payloadSize={}, qos={}, retained={}", 
                           topic, payload.length, qos, retained);
                
                mqttGateway.publishMessage(topic, payload, qos, retained);
                
                logger.info("Successfully published bytes message to MQTT: topic={}, payloadSize={}", 
                           topic, payload.length);
                
            } catch (Exception e) {
                logger.error("Failed to publish bytes message to MQTT: topic={}, error={}", 
                           topic, e.getMessage());
                throw new RuntimeException("MQTT bytes message publish failed", e);
            }
        });
    }
    
    /**
     * MQTT 网关接口
     * 使用 Spring Integration 进行消息发布
     */
    @MessagingGateway
    public interface MqttGateway {
        
        /**
         * 发布消息到 MQTT
         * 
         * @param topic MQTT 主题
         * @param payload 消息载荷
         * @param qos QoS 级别
         * @param retained 是否保留消息
         */
        @Gateway(requestChannel = "mqttOutboundChannel")
        void publishMessage(String topic, byte[] payload, int qos, boolean retained);
        
        /**
         * 发布文本消息到 MQTT
         * 
         * @param topic MQTT 主题
         * @param message 文本消息
         */
        @Gateway(requestChannel = "mqttTextOutboundChannel")
        void publishTextMessage(String topic, String message);
    }
}