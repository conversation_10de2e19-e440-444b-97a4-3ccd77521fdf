package com.example.mqtt.websocket.integration;

import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;

/**
 * MQTT 网关接口
 * 提供消息发布功能的统一接口
 */
@MessagingGateway(defaultRequestChannel = "mqttOutboundChannel")
public interface MqttGateway {

    /**
     * 发送消息到指定主题
     * 
     * @param topic 主题名称
     * @param payload 消息内容
     */
    @Gateway
    void sendToMqtt(@Header("mqtt_topic") String topic, @Payload Object payload);

    /**
     * 发送消息到指定主题，指定 QoS
     * 
     * @param topic 主题名称
     * @param qos 服务质量等级 (0, 1, 2)
     * @param payload 消息内容
     */
    @Gateway
    void sendToMqtt(@Header("mqtt_topic") String topic, 
                   @Header("mqtt_qos") int qos, 
                   @Payload Object payload);

    /**
     * 发送保留消息到指定主题
     * 
     * @param topic 主题名称
     * @param qos 服务质量等级
     * @param retained 是否为保留消息
     * @param payload 消息内容
     */
    @Gateway
    void sendToMqtt(@Header("mqtt_topic") String topic,
                   @Header("mqtt_qos") int qos,
                   @Header("mqtt_retained") boolean retained,
                   @Payload Object payload);

    /**
     * 发送字节数组消息
     * 
     * @param topic 主题名称
     * @param payload 字节数组消息内容
     */
    @Gateway
    void publishMessage(@Header("mqtt_topic") String topic, @Payload byte[] payload);
}