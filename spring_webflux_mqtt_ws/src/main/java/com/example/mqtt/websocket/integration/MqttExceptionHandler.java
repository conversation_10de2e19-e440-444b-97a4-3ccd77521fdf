package com.example.mqtt.websocket.integration;

import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

/**
 * MQTT 异常处理器
 * 处理 MQTT 连接和消息传输过程中的异常
 */
@Component
public class MqttExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(MqttExceptionHandler.class);

    /**
     * 处理 MQTT 错误消息
     * 
     * @param failedMessage 失败的消息
     */
    @ServiceActivator(inputChannel = "mqttErrorChannel")
    public void handleMqttError(Message<?> failedMessage) {
        logger.error("MQTT error occurred for message: {}", failedMessage);
        
        Throwable exception = (Throwable) failedMessage.getPayload();
        
        if (exception instanceof MqttException) {
            handleMqttException((MqttException) exception);
        } else if (exception instanceof MessagingException) {
            handleMessagingException((MessagingException) exception);
        } else {
            handleGenericException(exception);
        }
    }

    /**
     * 处理 MQTT 特定异常
     * 
     * @param mqttException MQTT 异常
     */
    private void handleMqttException(MqttException mqttException) {
        int reasonCode = mqttException.getReasonCode();
        
        switch (reasonCode) {
            case MqttException.REASON_CODE_BROKER_UNAVAILABLE:
                logger.error("MQTT Broker unavailable: {}", mqttException.getMessage());
                // 可以触发重连逻辑或发送告警
                break;
                
            case MqttException.REASON_CODE_CLIENT_TIMEOUT:
                logger.error("MQTT Client timeout: {}", mqttException.getMessage());
                // 可以调整超时配置或重试
                break;
                
            case MqttException.REASON_CODE_CONNECTION_LOST:
                logger.error("MQTT Connection lost: {}", mqttException.getMessage());
                // 自动重连机制会处理这种情况
                break;
                
            case MqttException.REASON_CODE_MAX_INFLIGHT:
                logger.error("MQTT Max inflight messages reached: {}", mqttException.getMessage());
                // 可以实现消息队列或限流机制
                break;
                
            default:
                logger.error("MQTT Exception with reason code {}: {}", 
                           reasonCode, mqttException.getMessage());
        }
    }

    /**
     * 处理消息传递异常
     * 
     * @param messagingException 消息异常
     */
    private void handleMessagingException(MessagingException messagingException) {
        logger.error("Messaging exception occurred: {}", messagingException.getMessage());
        
        // 可以实现消息重试逻辑
        Message<?> failedMessage = messagingException.getFailedMessage();
        if (failedMessage != null) {
            logger.error("Failed message headers: {}", failedMessage.getHeaders());
            // 可以将失败的消息发送到死信队列
        }
    }

    /**
     * 处理通用异常
     * 
     * @param exception 异常
     */
    private void handleGenericException(Throwable exception) {
        logger.error("Generic exception in MQTT processing: {}", exception.getMessage(), exception);
        
        // 可以实现通用的错误处理逻辑
        // 例如：发送告警、记录统计信息等
    }
}