package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.config.properties.WebSocketProperties;
import com.example.mqtt.websocket.handler.ReactiveWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.reactive.HandlerMapping;
import org.springframework.web.reactive.handler.SimpleUrlHandlerMapping;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.server.support.WebSocketHandlerAdapter;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket 配置类
 * 配置 WebSocket 端点、处理器映射和相关设置
 * 
 * <AUTHOR>
 */
@Configuration
public class WebSocketConfig {
    
    private final WebSocketProperties webSocketProperties;
    private final ReactiveWebSocketHandler webSocketHandler;
    
    @Autowired
    public WebSocketConfig(WebSocketProperties webSocketProperties, 
                          ReactiveWebSocketHandler webSocketHandler) {
        this.webSocketProperties = webSocketProperties;
        this.webSocketHandler = webSocketHandler;
    }
    
    /**
     * 配置 WebSocket 处理器映射
     * 
     * @return HandlerMapping
     */
    @Bean
    public HandlerMapping webSocketHandlerMapping() {
        Map<String, WebSocketHandler> map = new HashMap<>();
        map.put(webSocketProperties.getEndpoint(), (WebSocketHandler) webSocketHandler);
        
        SimpleUrlHandlerMapping handlerMapping = new SimpleUrlHandlerMapping();
        handlerMapping.setOrder(1);
        handlerMapping.setUrlMap(map);
        
        // 配置 CORS
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowCredentials(true);
        corsConfig.addAllowedOriginPattern(webSocketProperties.getAllowedOrigins());
        corsConfig.addAllowedHeader("*");
        corsConfig.addAllowedMethod("*");
        
        Map<String, CorsConfiguration> corsConfigMap = new HashMap<>();
        corsConfigMap.put(webSocketProperties.getEndpoint(), corsConfig);
        handlerMapping.setCorsConfigurations(corsConfigMap);
        
        return handlerMapping;
    }
    
    /**
     * WebSocket 处理器适配器
     * 
     * @return WebSocketHandlerAdapter
     */
    @Bean
    public WebSocketHandlerAdapter handlerAdapter() {
        WebSocketHandlerAdapter adapter = new WebSocketHandlerAdapter();
        
        // 配置会话超时 - 方法在新版本中已移除，使用默认配置
        
        return adapter;
    }
}