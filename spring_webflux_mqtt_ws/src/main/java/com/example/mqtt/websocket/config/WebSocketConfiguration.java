package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.handler.ReactiveWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket 配置
 * 配置 WebSocket 端点和处理器
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebSocket
public class WebSocketConfiguration implements WebSocketConfigurer {
    
    @Value("${app.websocket.endpoint}")
    private String webSocketEndpoint;
    
    @Value("${app.websocket.allowed-origins}")
    private String allowedOrigins;
    
    @Autowired
    private ReactiveWebSocketHandler webSocketHandler;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(webSocketHandler, webSocketEndpoint)
                .setAllowedOrigins(allowedOrigins)
                .withSockJS();
    }
}