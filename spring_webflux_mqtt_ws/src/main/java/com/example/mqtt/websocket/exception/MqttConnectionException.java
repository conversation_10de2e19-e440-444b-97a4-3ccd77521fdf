package com.example.mqtt.websocket.exception;

/**
 * MQTT 连接异常
 */
public class MqttConnectionException extends RuntimeException {
    
    private final String brokerUrl;
    private final String clientId;
    
    public MqttConnectionException(String brokerUrl, String clientId, String message) {
        super(String.format("MQTT connection failed for client %s to broker %s: %s", clientId, brokerUrl, message));
        this.brokerUrl = brokerUrl;
        this.clientId = clientId;
    }
    
    public MqttConnectionException(String brokerUrl, String clientId, String message, Throwable cause) {
        super(String.format("MQTT connection failed for client %s to broker %s: %s", clientId, brokerUrl, message), cause);
        this.brokerUrl = brokerUrl;
        this.clientId = clientId;
    }
    
    public String getBrokerUrl() {
        return brokerUrl;
    }
    
    public String getClientId() {
        return clientId;
    }
}