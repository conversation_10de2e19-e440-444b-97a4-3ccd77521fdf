package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 消息转换器
 * 负责不同消息格式之间的转换
 * 
 * <AUTHOR>
 */
@Service("serviceMessageConverter")
public class MessageConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageConverter.class);
    
    private final ObjectMapper objectMapper;
    private final JsonFormat.Parser jsonParser;
    private final JsonFormat.Printer jsonPrinter;
    
    public MessageConverter() {
        this.objectMapper = new ObjectMapper();
        this.jsonParser = JsonFormat.parser().ignoringUnknownFields();
        this.jsonPrinter = JsonFormat.printer().includingDefaultValueFields();
    }
    
    /**
     * 将字节数组解析为 BaseMessage
     * 
     * @param data 字节数组
     * @return BaseMessage
     */
    public BaseMessage parseFromBytes(byte[] data) {
        try {
            return BaseMessage.parseFrom(data);
        } catch (InvalidProtocolBufferException e) {
            logger.error("Failed to parse BaseMessage from bytes: error={}", e.getMessage());
            throw new RuntimeException("Failed to parse message from bytes", e);
        }
    }
    
    /**
     * 将 BaseMessage 转换为字节数组
     * 
     * @param message BaseMessage
     * @return 字节数组
     */
    public byte[] convertToBytes(BaseMessage message) {
        return message.toByteArray();
    }
    
    /**
     * 将 JSON 字符串解析为 WebSocketMessage
     * 
     * @param jsonData JSON 字符串
     * @return WebSocketMessage
     */
    public WebSocketMessage parseWebSocketMessage(String jsonData) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonData);
            
            // 检查是否是完整的 WebSocketMessage 格式
            if (jsonNode.has("message") && jsonNode.has("type")) {
                WebSocketMessage.Builder builder = WebSocketMessage.newBuilder();
                jsonParser.merge(jsonData, builder);
                return builder.build();
            } else {
                // 如果是简单的消息格式，创建默认的 WebSocketMessage
                return createDefaultWebSocketMessage(jsonNode);
            }
        } catch (Exception e) {
            logger.error("Failed to parse WebSocketMessage from JSON: error={}", e.getMessage());
            throw new RuntimeException("Failed to parse WebSocket message", e);
        }
    }
    
    /**
     * 将 WebSocketMessage 转换为 JSON 字符串
     * 
     * @param message WebSocketMessage
     * @return JSON 字符串
     */
    public String convertToJson(WebSocketMessage message) {
        try {
            return jsonPrinter.print(message);
        } catch (Exception e) {
            logger.error("Failed to convert WebSocketMessage to JSON: error={}", e.getMessage());
            throw new RuntimeException("Failed to convert message to JSON", e);
        }
    }
    
    /**
     * 将 BaseMessage 转换为 JSON 字符串
     * 
     * @param message BaseMessage
     * @return JSON 字符串
     */
    public String convertToJson(BaseMessage message) {
        try {
            return jsonPrinter.print(message);
        } catch (Exception e) {
            logger.error("Failed to convert BaseMessage to JSON: error={}", e.getMessage());
            throw new RuntimeException("Failed to convert message to JSON", e);
        }
    }
    
    /**
     * 将 JSON 字符串解析为 BaseMessage
     * 
     * @param jsonData JSON 字符串
     * @return BaseMessage
     */
    public BaseMessage parseBaseMessage(String jsonData) {
        try {
            BaseMessage.Builder builder = BaseMessage.newBuilder();
            jsonParser.merge(jsonData, builder);
            return builder.build();
        } catch (Exception e) {
            logger.error("Failed to parse BaseMessage from JSON: error={}", e.getMessage());
            throw new RuntimeException("Failed to parse base message", e);
        }
    }
    
    /**
     * 创建默认的 WebSocketMessage
     * 
     * @param jsonNode JSON 节点
     * @return WebSocketMessage
     */
    private WebSocketMessage createDefaultWebSocketMessage(JsonNode jsonNode) {
        try {
            BaseMessage.Builder baseBuilder = BaseMessage.newBuilder()
                    .setId(UUID.randomUUID().toString())
                    .setTimestamp(System.currentTimeMillis())
                    .setSource("websocket");
            
            // 提取消息类型
            if (jsonNode.has("type")) {
                baseBuilder.setType(jsonNode.get("type").asText());
            } else {
                baseBuilder.setType("message");
            }
            
            // 提取消息内容
            if (jsonNode.has("content")) {
                String content = jsonNode.get("content").asText();
                baseBuilder.setPayload(com.google.protobuf.ByteString.copyFromUtf8(content));
            } else if (jsonNode.has("data")) {
                String data = jsonNode.get("data").toString();
                baseBuilder.setPayload(com.google.protobuf.ByteString.copyFromUtf8(data));
            } else {
                // 使用整个 JSON 作为载荷
                baseBuilder.setPayload(com.google.protobuf.ByteString.copyFromUtf8(jsonNode.toString()));
            }
            
            // 提取头信息
            if (jsonNode.has("headers")) {
                JsonNode headersNode = jsonNode.get("headers");
                headersNode.fields().forEachRemaining(entry -> 
                    baseBuilder.putHeaders(entry.getKey(), entry.getValue().asText()));
            }
            
            WebSocketMessage.Builder wsBuilder = WebSocketMessage.newBuilder()
                    .setMessage(baseBuilder.build())
                    .setType(WebSocketMessage.MessageType.BROADCAST);
            
            // 提取用户ID
            if (jsonNode.has("userId")) {
                wsBuilder.setUserId(jsonNode.get("userId").asText())
                        .setType(WebSocketMessage.MessageType.UNICAST);
            }
            
            // 提取会话ID
            if (jsonNode.has("sessionId")) {
                wsBuilder.setSessionId(jsonNode.get("sessionId").asText());
            }
            
            return wsBuilder.build();
            
        } catch (Exception e) {
            logger.error("Failed to create default WebSocketMessage: error={}", e.getMessage());
            throw new RuntimeException("Failed to create default WebSocket message", e);
        }
    }
    
    /**
     * 验证消息格式
     * 
     * @param jsonData JSON 数据
     * @return 是否有效
     */
    public boolean isValidMessageFormat(String jsonData) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonData);
            return jsonNode.isObject() && (jsonNode.has("type") || jsonNode.has("content") || jsonNode.has("data"));
        } catch (Exception e) {
            logger.debug("Invalid message format: error={}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 提取消息类型
     * 
     * @param jsonData JSON 数据
     * @return 消息类型
     */
    public String extractMessageType(String jsonData) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonData);
            if (jsonNode.has("type")) {
                return jsonNode.get("type").asText();
            } else if (jsonNode.has("message") && jsonNode.get("message").has("type")) {
                return jsonNode.get("message").get("type").asText();
            } else {
                return "unknown";
            }
        } catch (Exception e) {
            logger.debug("Failed to extract message type: error={}", e.getMessage());
            return "unknown";
        }
    }
    
    /**
     * 创建错误响应消息
     * 
     * @param errorMessage 错误信息
     * @param originalMessageId 原始消息ID
     * @return WebSocketMessage
     */
    public WebSocketMessage createErrorResponse(String errorMessage, String originalMessageId) {
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType("error")
                .setSource("system")
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8(errorMessage))
                .putHeaders("originalMessageId", originalMessageId != null ? originalMessageId : "unknown")
                .build();
        
        return WebSocketMessage.newBuilder()
                .setType(WebSocketMessage.MessageType.SYSTEM)
                .setMessage(baseMessage)
                .build();
    }
}
