package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.service.MessageBridgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;

/**
 * 消息桥接配置
 * 配置 MQTT 和 WebSocket 之间的消息路由和转换
 * 
 * <AUTHOR>
 */
@Configuration
public class MessageBridgeConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageBridgeConfiguration.class);
    
    @Value("${app.mqtt.client-id}")
    private String clientId;
    
    @Autowired
    private MessageBridgeService messageBridgeService;
    
    @Autowired
    private MqttPahoClientFactory mqttClientFactory;
    
    /**
     * MQTT 文本出站消息通道
     */
    @Bean
    public MessageChannel mqttTextOutboundChannel() {
        return new DirectChannel();
    }
    
    /**
     * WebSocket 入站消息通道
     */
    @Bean
    public MessageChannel webSocketInboundChannel() {
        return new DirectChannel();
    }
    
    /**
     * WebSocket 出站消息通道
     */
    @Bean
    public MessageChannel webSocketOutboundChannel() {
        return new DirectChannel();
    }
    
    /**
     * MQTT 文本出站处理器 - 发送文本消息
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttTextOutboundChannel")
    public MessageHandler mqttTextOutbound() {
        MqttPahoMessageHandler messageHandler = 
            new MqttPahoMessageHandler(clientId + "_text_outbound", mqttClientFactory);
        
        messageHandler.setAsync(true);
        messageHandler.setDefaultTopic("default/text");
        messageHandler.setDefaultQos(1);
        messageHandler.setDefaultRetained(false);
        
        logger.info("Configured MQTT text outbound handler: clientId={}", clientId + "_text_outbound");
        
        return messageHandler;
    }
    
    /**
     * MQTT 消息处理器 - 处理接收到的 MQTT 消息并转发到 WebSocket
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public MessageHandler mqttMessageHandler() {
        return new MessageHandler() {
            @Override
            public void handleMessage(Message<?> message) throws MessagingException {
                try {
                    String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
                    Integer qos = (Integer) message.getHeaders().get(MqttHeaders.RECEIVED_QOS);
                    Boolean retained = (Boolean) message.getHeaders().get(MqttHeaders.RECEIVED_RETAINED);
                    
                    byte[] payload;
                    if (message.getPayload() instanceof byte[]) {
                        payload = (byte[]) message.getPayload();
                    } else {
                        payload = message.getPayload().toString().getBytes();
                    }
                    
                    logger.debug("Received MQTT message: topic={}, payloadSize={}, qos={}, retained={}", 
                               topic, payload.length, qos, retained);
                    
                    // 异步处理消息桥接
                    messageBridgeService.handleMqttMessage(
                        topic, 
                        payload, 
                        qos != null ? qos : 0, 
                        retained != null ? retained : false
                    ).subscribe(
                        null,
                        error -> logger.error("Error processing MQTT message: topic={}, error={}", 
                                             topic, error.getMessage())
                    );
                    
                } catch (Exception e) {
                    logger.error("Error handling MQTT message: error={}", e.getMessage());
                    throw new MessagingException("Failed to handle MQTT message", e);
                }
            }
        };
    }
    
    /**
     * WebSocket 消息处理器 - 处理来自 WebSocket 的消息并转发到 MQTT
     */
    @Bean
    @ServiceActivator(inputChannel = "webSocketInboundChannel")
    public MessageHandler webSocketMessageHandler() {
        return new MessageHandler() {
            @Override
            public void handleMessage(Message<?> message) throws MessagingException {
                try {
                    String sessionId = (String) message.getHeaders().get("sessionId");
                    String messageData = message.getPayload().toString();
                    
                    logger.debug("Received WebSocket message: sessionId={}, dataSize={}", 
                               sessionId, messageData.length());
                    
                    // 异步处理消息桥接
                    messageBridgeService.handleWebSocketMessage(sessionId, messageData)
                        .subscribe(
                            null,
                            error -> logger.error("Error processing WebSocket message: sessionId={}, error={}", 
                                                 sessionId, error.getMessage())
                        );
                    
                } catch (Exception e) {
                    logger.error("Error handling WebSocket message: error={}", e.getMessage());
                    throw new MessagingException("Failed to handle WebSocket message", e);
                }
            }
        };
    }
}
