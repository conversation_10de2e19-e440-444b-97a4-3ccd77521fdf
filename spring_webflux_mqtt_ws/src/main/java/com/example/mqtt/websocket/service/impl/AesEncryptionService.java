package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.properties.EncryptionProperties;
import com.example.mqtt.websocket.service.EncryptionException;
import com.example.mqtt.websocket.service.EncryptionService;
import com.example.mqtt.websocket.service.KeyManager;
import com.example.mqtt.websocket.service.KeyNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import java.nio.ByteBuffer;
import java.security.SecureRandom;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

/**
 * AES-256 加密服务实现
 */
@Service
public class AesEncryptionService {
    
    private static final Logger logger = LoggerFactory.getLogger(AesEncryptionService.class);
    private static final int GCM_IV_LENGTH = 12; // GCM 推荐的 IV 长度
    private static final int GCM_TAG_LENGTH = 16; // GCM 认证标签长度
    
    private final KeyManager keyManager;
    private final EncryptionProperties encryptionProperties;
    private final SecureRandom secureRandom;
    private final AtomicReference<String> currentKeyId;
    
    public AesEncryptionService(KeyManager keyManager, EncryptionProperties encryptionProperties) {
        this.keyManager = keyManager;
        this.encryptionProperties = encryptionProperties;
        this.secureRandom = new SecureRandom();
        this.currentKeyId = new AtomicReference<>();
        
        // 初始化默认密钥
        initializeDefaultKey();
    }
    
    private void initializeDefaultKey() {
        try {
            String defaultKeyId = "default-" + System.currentTimeMillis();
            keyManager.generateKey(defaultKeyId);
            currentKeyId.set(defaultKeyId);
            logger.info("Initialized default encryption key: {}", defaultKeyId);
        } catch (Exception e) {
            logger.error("Failed to initialize default encryption key", e);
        }
    }
    
    public byte[] encrypt(byte[] plaintext, String keyId) throws EncryptionException {
        if (plaintext == null || plaintext.length == 0) {
            throw new EncryptionException("Plaintext cannot be null or empty");
        }
        
        if (keyId == null || keyId.trim().isEmpty()) {
            throw new EncryptionException("Key ID cannot be null or empty");
        }
        
        try {
            SecretKey secretKey = keyManager.getKey(keyId);
            
            // 生成随机 IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            secureRandom.nextBytes(iv);
            
            // 初始化加密器
            Cipher cipher = Cipher.getInstance(encryptionProperties.getTransformation());
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec);
            
            // 执行加密
            byte[] ciphertext = cipher.doFinal(plaintext);
            
            // 组合 IV 和密文 (IV + 密文)
            ByteBuffer buffer = ByteBuffer.allocate(GCM_IV_LENGTH + ciphertext.length);
            buffer.put(iv);
            buffer.put(ciphertext);
            
            logger.debug("Successfully encrypted data with key: {}, size: {} bytes", keyId, plaintext.length);
            return buffer.array();
        } catch (KeyNotFoundException e) {
            throw new EncryptionException("Encryption key not found: " + keyId, e);
        } catch (Exception e) {
            throw new EncryptionException("Encryption failed for key: " + keyId, e);
        }
    }
    
    public byte[] decrypt(byte[] ciphertext, String keyId) throws EncryptionException {
        if (ciphertext == null || ciphertext.length <= GCM_IV_LENGTH) {
            throw new EncryptionException("Ciphertext is invalid or too short");
        }
        
        if (keyId == null || keyId.trim().isEmpty()) {
            throw new EncryptionException("Key ID cannot be null or empty");
        }
        
        try {
            SecretKey secretKey = keyManager.getKey(keyId);
            
            // 分离 IV 和密文
            ByteBuffer buffer = ByteBuffer.wrap(ciphertext);
            byte[] iv = new byte[GCM_IV_LENGTH];
            buffer.get(iv);
            
            byte[] encryptedData = new byte[buffer.remaining()];
            buffer.get(encryptedData);
            
            // 初始化解密器
            Cipher cipher = Cipher.getInstance(encryptionProperties.getTransformation());
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);
            
            // 执行解密
            byte[] plaintext = cipher.doFinal(encryptedData);
            
            logger.debug("Successfully decrypted data with key: {}, size: {} bytes", keyId, plaintext.length);
            return plaintext;
        } catch (KeyNotFoundException e) {
            throw new EncryptionException("Decryption key not found: " + keyId, e);
        } catch (Exception e) {
            throw new EncryptionException("Decryption failed for key: " + keyId, e);
        }
    }
    
    public String generateKeyId() {
        String keyId = "key-" + UUID.randomUUID().toString();
        logger.debug("Generated new key ID: {}", keyId);
        return keyId;
    }
    
    public String getCurrentKeyId() {
        String keyId = currentKeyId.get();
        
        // 检查当前密钥是否需要轮换
        if (keyId != null && keyManager.shouldRotateKey(keyId)) {
            try {
                String newKeyId = generateKeyId();
                keyManager.generateKey(newKeyId);
                currentKeyId.set(newKeyId);
                
                logger.info("Rotated current key from {} to {}", keyId, newKeyId);
                return newKeyId;
            } catch (Exception e) {
                logger.error("Failed to rotate current key: {}", keyId, e);
                // 继续使用旧密钥
            }
        }
        
        return keyId;
    }
    
    public boolean keyExists(String keyId) {
        if (keyId == null || keyId.trim().isEmpty()) {
            return false;
        }
        
        return keyManager.keyExists(keyId);
    }
    
    /**
     * 设置当前活跃的密钥ID
     * 
     * @param keyId 密钥ID
     */
    public void setCurrentKeyId(String keyId) {
        if (keyExists(keyId)) {
            currentKeyId.set(keyId);
            logger.info("Set current key ID to: {}", keyId);
        } else {
            logger.warn("Attempted to set non-existent key as current: {}", keyId);
        }
    }
    
    /**
     * 加密字符串数据
     * 
     * @param plaintext 明文字符串
     * @param keyId 密钥ID
     * @return 加密后的字节数组
     * @throws EncryptionException 加密失败时抛出
     */
    public byte[] encryptString(String plaintext, String keyId) throws EncryptionException {
        if (plaintext == null) {
            throw new EncryptionException("Plaintext string cannot be null");
        }
        
        return encrypt(plaintext.getBytes(), keyId);
    }
    
    /**
     * 解密为字符串数据
     * 
     * @param ciphertext 密文字节数组
     * @param keyId 密钥ID
     * @return 解密后的字符串
     * @throws EncryptionException 解密失败时抛出
     */
    public String decryptToString(byte[] ciphertext, String keyId) throws EncryptionException {
        byte[] plaintext = decrypt(ciphertext, keyId);
        return new String(plaintext);
    }
}
