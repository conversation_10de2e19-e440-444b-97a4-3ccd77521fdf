package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.FailoverManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 默认故障转移管理器实现
 */
@Service
public class DefaultFailoverManager implements FailoverManager {

    private static final Logger logger = LoggerFactory.getLogger(DefaultFailoverManager.class);

    private final ClusterStateManager clusterStateManager;
    private final LoadBalancer loadBalancer;
    private final AtomicBoolean detectionRunning = new AtomicBoolean(false);
    private final Map<String, Integer> failureCountMap = new ConcurrentHashMap<>();
    private final Map<String, Integer> recoveryCountMap = new ConcurrentHashMap<>();
    private final Set<String> failedInstances = ConcurrentHashMap.newKeySet();
    private final Sinks.Many<FailoverEvent> eventSink = Sinks.many().multicast().onBackpressureBuffer();

    // 配置参数
    @Value("${app.failover.health-check-interval:30}")
    private int healthCheckInterval;

    @Value("${app.failover.health-check-timeout:5}")
    private int healthCheckTimeout;

    @Value("${app.failover.max-retries:3}")
    private int maxRetries;

    @Value("${app.failover.failure-threshold:3}")
    private int failureThreshold;

    @Value("${app.failover.recovery-threshold:2}")
    private int recoveryThreshold;

    @Value("${app.failover.auto-failover-enabled:true}")
    private boolean autoFailoverEnabled;

    @Value("${app.failover.auto-recovery-enabled:true}")
    private boolean autoRecoveryEnabled;

    public DefaultFailoverManager(ClusterStateManager clusterStateManager, LoadBalancer loadBalancer) {
        this.clusterStateManager = clusterStateManager;
        this.loadBalancer = loadBalancer;
    }

    @Override
    public Mono<Void> startFailureDetection() {
        if (detectionRunning.compareAndSet(false, true)) {
            logger.info("Starting failure detection with interval: {}s", healthCheckInterval);
            
            return Flux.interval(Duration.ofSeconds(healthCheckInterval))
                .takeWhile(tick -> detectionRunning.get())
                .flatMap(tick -> performHealthChecks())
                .subscribeOn(Schedulers.boundedElastic())
                .then()
                .doOnCancel(() -> {
                    detectionRunning.set(false);
                    logger.info("Failure detection stopped");
                });
        }
        
        return Mono.empty();
    }

    @Override
    public Mono<Void> stopFailureDetection() {
        return Mono.fromRunnable(() -> {
            if (detectionRunning.compareAndSet(true, false)) {
                logger.info("Stopping failure detection");
            }
        });
    }

    @Override
    public Mono<FailoverResult> triggerFailover(String failedInstanceId) {
        return Mono.fromCallable(() -> {
            logger.warn("Triggering failover for instance: {}", failedInstanceId);
            
            // 标记实例为失败
            failedInstances.add(failedInstanceId);
            loadBalancer.updateInstanceHealth(failedInstanceId, false).subscribe();
            
            // 发送故障转移事件
            FailoverEvent event = new FailoverEvent(
                UUID.randomUUID().toString(),
                FailoverEventType.FAILOVER_TRIGGERED,
                failedInstanceId,
                "Manual failover triggered",
                Instant.now()
            );
            eventSink.tryEmitNext(event);
            
            return new FailoverResult(
                failedInstanceId,
                null, // 由负载均衡器选择目标实例
                true,
                "Failover triggered successfully",
                Instant.now()
            );
        })
        .doOnSuccess(result -> logger.info("Failover completed for instance: {}", failedInstanceId))
        .doOnError(error -> logger.error("Failover failed for instance: {}", failedInstanceId, error));
    }

    @Override
    public Mono<HealthCheckResult> checkInstanceHealth(String instanceId) {
        return Mono.fromCallable(() -> {
            Instant checkTime = Instant.now();
            long startTime = System.currentTimeMillis();
            
            try {
                // 检查实例是否在集群中活跃
                boolean isActive = clusterStateManager.getActiveInstances()
                    .map(instances -> instances.contains(instanceId))
                    .block(Duration.ofSeconds(healthCheckTimeout));
                
                long responseTime = System.currentTimeMillis() - startTime;
                
                if (Boolean.TRUE.equals(isActive)) {
                    // 检查实例心跳
                    Boolean isHealthy = clusterStateManager.isInstanceHealthy(instanceId)
                        .block(Duration.ofSeconds(healthCheckTimeout));
                    
                    return new HealthCheckResult(
                        instanceId,
                        Boolean.TRUE.equals(isHealthy),
                        responseTime,
                        Boolean.TRUE.equals(isHealthy) ? null : "Instance heartbeat check failed",
                        checkTime
                    );
                } else {
                    return new HealthCheckResult(
                        instanceId,
                        false,
                        responseTime,
                        "Instance not found in active instances",
                        checkTime
                    );
                }
            } catch (Exception e) {
                long responseTime = System.currentTimeMillis() - startTime;
                return new HealthCheckResult(
                    instanceId,
                    false,
                    responseTime,
                    "Health check failed: " + e.getMessage(),
                    checkTime
                );
            }
        })
        .timeout(Duration.ofSeconds(healthCheckTimeout))
        .onErrorReturn(new HealthCheckResult(
            instanceId,
            false,
            healthCheckTimeout * 1000L,
            "Health check timeout",
            Instant.now()
        ));
    }

    @Override
    public Mono<List<String>> getFailedInstances() {
        return Mono.just(new ArrayList<>(failedInstances));
    }

    @Override
    public Mono<RecoveryResult> attemptInstanceRecovery(String instanceId) {
        return checkInstanceHealth(instanceId)
            .flatMap(healthResult -> {
                if (healthResult.healthy()) {
                    // 实例已恢复
                    failedInstances.remove(instanceId);
                    failureCountMap.remove(instanceId);
                    recoveryCountMap.remove(instanceId);
                    
                    return loadBalancer.updateInstanceHealth(instanceId, true)
                        .then(Mono.fromCallable(() -> {
                            FailoverEvent event = new FailoverEvent(
                                UUID.randomUUID().toString(),
                                FailoverEventType.INSTANCE_RECOVERED,
                                instanceId,
                                "Instance recovered successfully",
                                Instant.now()
                            );
                            eventSink.tryEmitNext(event);
                            
                            logger.info("Instance {} recovered successfully", instanceId);
                            return new RecoveryResult(
                                instanceId,
                                true,
                                "Instance health check passed",
                                Instant.now()
                            );
                        }));
                } else {
                    return Mono.just(new RecoveryResult(
                        instanceId,
                        false,
                        healthResult.errorMessage(),
                        Instant.now()
                    ));
                }
            });
    }

    @Override
    public Flux<FailoverEvent> listenFailoverEvents() {
        return eventSink.asFlux();
    }

    @Override
    public Mono<FailoverConfig> getFailoverConfig() {
        return Mono.just(new FailoverConfig(
            healthCheckInterval,
            healthCheckTimeout,
            maxRetries,
            failureThreshold,
            recoveryThreshold,
            autoFailoverEnabled,
            autoRecoveryEnabled
        ));
    }

    @Override
    public Mono<Void> updateFailoverConfig(FailoverConfig config) {
        return Mono.fromRunnable(() -> {
            this.healthCheckInterval = config.healthCheckInterval();
            this.healthCheckTimeout = config.healthCheckTimeout();
            this.maxRetries = config.maxRetries();
            this.failureThreshold = config.failureThreshold();
            this.recoveryThreshold = config.recoveryThreshold();
            this.autoFailoverEnabled = config.autoFailoverEnabled();
            this.autoRecoveryEnabled = config.autoRecoveryEnabled();
            
            logger.info("Failover configuration updated: {}", config);
        });
    }

    /**
     * 执行健康检查
     */
    private Mono<Void> performHealthChecks() {
        return clusterStateManager.getActiveInstances()
            .flatMapMany(Flux::fromIterable)
            .flatMap(this::checkAndHandleInstanceHealth)
            .then();
    }

    /**
     * 检查并处理实例健康状态
     */
    private Mono<Void> checkAndHandleInstanceHealth(String instanceId) {
        return checkInstanceHealth(instanceId)
            .flatMap(healthResult -> {
                if (healthResult.healthy()) {
                    return handleHealthyInstance(instanceId);
                } else {
                    return handleUnhealthyInstance(instanceId, healthResult);
                }
            })
            .onErrorResume(error -> {
                logger.error("Error checking health for instance: {}", instanceId, error);
                return Mono.empty();
            });
    }

    /**
     * 处理健康实例
     */
    private Mono<Void> handleHealthyInstance(String instanceId) {
        if (failedInstances.contains(instanceId) && autoRecoveryEnabled) {
            int recoveryCount = recoveryCountMap.getOrDefault(instanceId, 0) + 1;
            recoveryCountMap.put(instanceId, recoveryCount);
            
            if (recoveryCount >= recoveryThreshold) {
                return attemptInstanceRecovery(instanceId).then();
            }
        } else {
            // 重置失败计数
            failureCountMap.remove(instanceId);
        }
        
        return Mono.empty();
    }

    /**
     * 处理不健康实例
     */
    private Mono<Void> handleUnhealthyInstance(String instanceId, HealthCheckResult healthResult) {
        int failureCount = failureCountMap.getOrDefault(instanceId, 0) + 1;
        failureCountMap.put(instanceId, failureCount);
        
        FailoverEvent event = new FailoverEvent(
            UUID.randomUUID().toString(),
            FailoverEventType.HEALTH_CHECK_FAILED,
            instanceId,
            "Health check failed: " + healthResult.errorMessage(),
            Instant.now()
        );
        eventSink.tryEmitNext(event);
        
        if (failureCount >= failureThreshold && autoFailoverEnabled) {
            return triggerFailover(instanceId).then();
        }
        
        return Mono.empty();
    }
}