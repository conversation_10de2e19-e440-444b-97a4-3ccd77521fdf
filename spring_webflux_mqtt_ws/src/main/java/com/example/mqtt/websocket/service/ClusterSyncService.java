package com.example.mqtt.websocket.service;

import reactor.core.publisher.Mono;

/**
 * 集群同步服务接口
 * 负责集群间的状态同步和消息协调
 */
public interface ClusterSyncService {

    /**
     * 启动集群同步服务
     * 
     * @return 启动结果
     */
    Mono<Void> start();

    /**
     * 停止集群同步服务
     * 
     * @return 停止结果
     */
    Mono<Void> stop();

    /**
     * 同步集群状态
     * 
     * @return 同步结果
     */
    Mono<Void> syncClusterState();

    /**
     * 处理实例加入事件
     * 
     * @param instanceId 实例ID
     * @return 处理结果
     */
    Mono<Void> handleInstanceJoined(String instanceId);

    /**
     * 处理实例离开事件
     * 
     * @param instanceId 实例ID
     * @return 处理结果
     */
    Mono<Void> handleInstanceLeft(String instanceId);

    /**
     * 获取当前实例ID
     * 
     * @return 实例ID
     */
    String getCurrentInstanceId();
}