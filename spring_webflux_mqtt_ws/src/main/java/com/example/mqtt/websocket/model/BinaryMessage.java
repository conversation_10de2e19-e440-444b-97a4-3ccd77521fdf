package com.example.mqtt.websocket.model;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * WebSocket二进制消息封装类
 * 兼容MQTT协议的消息结构设计
 */
public class BinaryMessage {

    // MQTT消息类型常量 (按照MQTT协议定义)
    public static final byte CONNECT = 1;
    public static final byte CONNACK = 2;
    public static final byte PUBLISH = 3;
    public static final byte PUBACK = 4;
    public static final byte PUBREC = 5;
    public static final byte PUBREL = 6;
    public static final byte PUBCOMP = 7;
    public static final byte SUBSCRIBE = 8;
    public static final byte SUBACK = 9;
    public static final byte UNSUBSCRIBE = 10;
    public static final byte UNSUBACK = 11;
    public static final byte PINGREQ = 12;
    public static final byte PINGRESP = 13;
    public static final byte DISCONNECT = 14;
    public static final byte AUTH = 15;

    // 标志位常量
    public static final byte FLAG_DUP = 0x08;      // 重发标志
    public static final byte FLAG_QOS_0 = 0x00;    // QoS 0
    public static final byte FLAG_QOS_1 = 0x02;    // QoS 1
    public static final byte FLAG_QOS_2 = 0x04;    // QoS 2
    public static final byte FLAG_RETAIN = 0x01;   // 保留消息标志

    // 固定头部的第一个字节
    private byte fixedHeaderByte1;

    // 消息ID (2字节): 用于请求-响应匹配
    private short messageId;

    // 有效载荷: 实际的消息内容
    private byte[] payload;

    // 主题名称 (仅用于PUBLISH消息)
    private String topicName;

    /**
     * 创建一个新的MQTT二进制消息
     *
     * @param messageType MQTT消息类型 (1-15)
     * @param dup         是否是重发消息
     * @param qosLevel    服务质量等级 (0-2)
     * @param retain      是否保留消息
     * @param messageId   消息ID
     * @param payload     有效载荷
     */
    public BinaryMessage(byte messageType, boolean dup, byte qosLevel, boolean retain, short messageId, byte[] payload) {
        // 验证消息类型
        if (messageType < 1 || messageType > 15) {
            throw new IllegalArgumentException("Invalid MQTT message type: " + messageType);
        }

        // 验证QoS级别
        if (qosLevel < 0 || qosLevel > 2) {
            throw new IllegalArgumentException("Invalid QoS level: " + qosLevel);
        }

        // 构建固定头部第一个字节
        // 高4位为消息类型，低4位为标志位
        this.fixedHeaderByte1 = (byte) ((messageType << 4) & 0xF0);

        // 设置标志位
        if (dup) {
            this.fixedHeaderByte1 |= FLAG_DUP;
        }

        // 设置QoS级别
        if (qosLevel == 1) {
            this.fixedHeaderByte1 |= FLAG_QOS_1;
        } else if (qosLevel == 2) {
            this.fixedHeaderByte1 |= FLAG_QOS_2;
        }

        // 设置保留标志
        if (retain) {
            this.fixedHeaderByte1 |= FLAG_RETAIN;
        }

        this.messageId = messageId;
        this.payload = payload;
    }

    /**
     * 创建一个简化的MQTT二进制消息（无DUP、QoS=0、无RETAIN）
     *
     * @param messageType MQTT消息类型 (1-15)
     * @param messageId   消息ID
     * @param payload     有效载荷
     */
    public BinaryMessage(byte messageType, short messageId, byte[] payload) {
        this(messageType, false, (byte) 0, false, messageId, payload);
    }

    /**
     * 将消息编码为字节数组
     *
     * @return 编码后的字节数组
     */
    public byte[] encode() {
        // 计算有效载荷长度
        int payloadLength = payload != null ? payload.length : 0;

        // 计算剩余长度字段所需的字节数
        int remainingLengthSize = 0;
        int remainingLength = payloadLength + 2; // 有效载荷长度 + 消息ID长度
        int x = remainingLength;
        do {
            x = x / 128;
            remainingLengthSize++;
        } while (x > 0);

        // 分配缓冲区
        ByteBuffer buffer = ByteBuffer.allocate(1 + remainingLengthSize + 2 + payloadLength);

        // 写入固定头部第一个字节
        buffer.put(fixedHeaderByte1);

        // 写入剩余长度字段（可变长度编码）
        x = remainingLength;
        do {
            byte encodedByte = (byte) (x % 128);
            x = x / 128;
            if (x > 0) {
                encodedByte = (byte) (encodedByte | 128);
            }
            buffer.put(encodedByte);
        } while (x > 0);

        // 写入消息ID
        buffer.putShort(messageId);

        // 写入有效载荷
        if (payload != null && payload.length > 0) {
            buffer.put(payload);
        }

        return buffer.array();
    }

    /**
     * 从字节数组解码消息
     *
     * @param data 字节数组
     * @return 解码后的二进制消息对象
     */
    public static BinaryMessage decode(byte[] data) {
        if (data == null || data.length < 3) { // 至少需要固定头部1字节 + 剩余长度至少1字节 + 消息ID 2字节
            throw new IllegalArgumentException("Invalid MQTT message data");
        }

        ByteBuffer buffer = ByteBuffer.wrap(data);

        // 读取固定头部第一个字节
        byte fixedHeaderByte1 = buffer.get();

        // 解析消息类型和标志位
        byte messageType = (byte) ((fixedHeaderByte1 >> 4) & 0x0F);
        boolean dup = (fixedHeaderByte1 & FLAG_DUP) != 0;
        byte qosLevel = (byte) ((fixedHeaderByte1 & 0x06) >> 1);
        boolean retain = (fixedHeaderByte1 & FLAG_RETAIN) != 0;

        // 读取剩余长度
        int multiplier = 1;
        int remainingLength = 0;
        byte encodedByte;
        int loops = 0;
        do {
            encodedByte = buffer.get();
            remainingLength += (encodedByte & 127) * multiplier;
            multiplier *= 128;
            loops++;
        } while ((encodedByte & 128) != 0 && loops < 4);

        // 读取消息ID
        short messageId = buffer.getShort();

        // 计算有效载荷长度
        int payloadLength = remainingLength - 2; // 减去消息ID长度

        // 读取有效载荷
        byte[] payload = new byte[payloadLength];
        if (payloadLength > 0) {
            buffer.get(payload);
        }

        return new BinaryMessage(messageType, dup, qosLevel, retain, messageId, payload);
    }

    /**
     * 创建一个PUBLISH消息
     *
     * @param topicName 主题名称
     * @param payload   有效载荷
     * @param messageId 消息ID (仅当QoS > 0时使用)
     * @param qosLevel  服务质量等级 (0-2)
     * @param dup       是否是重发消息
     * @param retain    是否保留消息
     * @return 编码后的MQTT PUBLISH消息字节数组
     */
    public static byte[] encodePublish(String topicName, byte[] payload, short messageId, byte qosLevel, boolean dup, boolean retain) {
        // 验证参数
        if (topicName == null || topicName.isEmpty()) {
            throw new IllegalArgumentException("Topic name cannot be null or empty");
        }

        if (qosLevel < 0 || qosLevel > 2) {
            throw new IllegalArgumentException("Invalid QoS level: " + qosLevel);
        }

        // 将主题名称转换为UTF-8字节数组
        byte[] topicNameBytes = topicName.getBytes(StandardCharsets.UTF_8);

        // 计算可变头部长度
        int variableHeaderLength = 2 + topicNameBytes.length; // 主题长度(2字节) + 主题名称
        if (qosLevel > 0) {
            variableHeaderLength += 2; // 消息ID(2字节)，仅当QoS > 0时
        }

        // 计算剩余长度
        int remainingLength = variableHeaderLength;
        if (payload != null) {
            remainingLength += payload.length;
        }

        // 计算剩余长度字段所需的字节数
        int remainingLengthSize = 0;
        int x = remainingLength;
        do {
            x = x / 128;
            remainingLengthSize++;
        } while (x > 0);

        // 分配缓冲区
        ByteBuffer buffer = ByteBuffer.allocate(1 + remainingLengthSize + remainingLength);

        // 写入固定头部第一个字节 (PUBLISH = 3 << 4)
        byte fixedHeaderByte1 = (byte) (PUBLISH << 4);
        if (dup) {
            fixedHeaderByte1 |= FLAG_DUP;
        }
        if (qosLevel == 1) {
            fixedHeaderByte1 |= FLAG_QOS_1;
        } else if (qosLevel == 2) {
            fixedHeaderByte1 |= FLAG_QOS_2;
        }
        if (retain) {
            fixedHeaderByte1 |= FLAG_RETAIN;
        }
        buffer.put(fixedHeaderByte1);

        // 写入剩余长度字段
        x = remainingLength;
        do {
            byte encodedByte = (byte) (x % 128);
            x = x / 128;
            if (x > 0) {
                encodedByte = (byte) (encodedByte | 128);
            }
            buffer.put(encodedByte);
        } while (x > 0);

        // 写入主题名称
        buffer.putShort((short) topicNameBytes.length);
        buffer.put(topicNameBytes);

        // 写入消息ID (仅当QoS > 0时)
        if (qosLevel > 0) {
            buffer.putShort(messageId);
        }

        // 写入有效载荷
        if (payload != null && payload.length > 0) {
            buffer.put(payload);
        }

        return buffer.array();
    }

    /**
     * 创建一个简化的PUBLISH消息 (QoS=0, 无DUP, 无RETAIN)
     *
     * @param topicName 主题名称
     * @param payload   有效载荷
     * @return 编码后的MQTT PUBLISH消息字节数组
     */
    public static byte[] encodePublish(String topicName, byte[] payload) {
        return encodePublish(topicName, payload, (short) 0, (byte) 0, false, false);
    }

    /**
     * 创建一个SUBSCRIBE消息
     *
     * @param messageId    消息ID
     * @param topicFilters 主题过滤器列表
     * @param qosLevels    对应的QoS级别列表
     * @return 编码后的MQTT SUBSCRIBE消息字节数组
     */
    public static byte[] encodeSubscribe(short messageId, List<String> topicFilters, List<Byte> qosLevels) {
        // 验证参数
        if (topicFilters == null || topicFilters.isEmpty()) {
            throw new IllegalArgumentException("Topic filters cannot be null or empty");
        }

        if (qosLevels == null || qosLevels.size() != topicFilters.size()) {
            throw new IllegalArgumentException("QoS levels must match topic filters");
        }

        // 计算可变头部长度
        int variableHeaderLength = 2; // 消息ID(2字节)

        // 计算有效载荷长度
        int payloadLength = 0;
        List<byte[]> topicFilterBytes = new ArrayList<>();

        for (String topicFilter : topicFilters) {
            byte[] bytes = topicFilter.getBytes(StandardCharsets.UTF_8);
            topicFilterBytes.add(bytes);
            payloadLength += 2 + bytes.length + 1; // 主题长度(2字节) + 主题 + QoS(1字节)
        }

        // 计算剩余长度
        int remainingLength = variableHeaderLength + payloadLength;

        // 计算剩余长度字段所需的字节数
        int remainingLengthSize = 0;
        int x = remainingLength;
        do {
            x = x / 128;
            remainingLengthSize++;
        } while (x > 0);

        // 分配缓冲区
        ByteBuffer buffer = ByteBuffer.allocate(1 + remainingLengthSize + remainingLength);

        // 写入固定头部第一个字节 (SUBSCRIBE = 8 << 4 | 0x02)
        // SUBSCRIBE消息的保留位必须为0，QoS必须为1
        buffer.put((byte) ((SUBSCRIBE << 4) | 0x02));

        // 写入剩余长度字段
        x = remainingLength;
        do {
            byte encodedByte = (byte) (x % 128);
            x = x / 128;
            if (x > 0) {
                encodedByte = (byte) (encodedByte | 128);
            }
            buffer.put(encodedByte);
        } while (x > 0);

        // 写入消息ID
        buffer.putShort(messageId);

        // 写入主题过滤器和QoS级别
        for (int i = 0; i < topicFilters.size(); i++) {
            byte[] topicBytes = topicFilterBytes.get(i);
            buffer.putShort((short) topicBytes.length);
            buffer.put(topicBytes);
            buffer.put(qosLevels.get(i));
        }

        return buffer.array();
    }

    /**
     * 创建一个PINGREQ消息
     *
     * @return 编码后的MQTT PINGREQ消息字节数组
     */
    public static byte[] encodePingReq() {
        // PINGREQ消息只有固定头部，没有可变头部和有效载荷
        byte[] message = new byte[2];
        message[0] = (byte) (PINGREQ << 4);
        message[1] = 0; // 剩余长度为0
        return message;
    }

    /**
     * 从MQTT消息中提取有效载荷
     *
     * @param mqttMessage MQTT消息字节数组
     * @return 提取的有效载荷字节数组
     */
    public static byte[] extractPayload(byte[] mqttMessage) {
        if (mqttMessage == null || mqttMessage.length < 2) {
            throw new IllegalArgumentException("Invalid MQTT message");
        }

        ByteBuffer buffer = ByteBuffer.wrap(mqttMessage);

        // 读取固定头部第一个字节
        byte fixedHeaderByte1 = buffer.get();
        byte messageType = (byte) ((fixedHeaderByte1 >> 4) & 0x0F);
        byte qos = (byte) ((fixedHeaderByte1 & 0x06) >> 1);

        // 确保这是PUBLISH消息
        if (messageType != PUBLISH) {
            throw new IllegalArgumentException("Not a PUBLISH message");
        }

        // 读取剩余长度
        int multiplier = 1;
        int remainingLength = 0;
        byte encodedByte;
        do {
            encodedByte = buffer.get();
            remainingLength += (encodedByte & 127) * multiplier;
            multiplier *= 128;
        } while ((encodedByte & 128) != 0);

        // 读取主题名称长度
        short topicLength = buffer.getShort();

        // 跳过主题名称
        buffer.position(buffer.position() + topicLength);

        // 如果QoS > 0，跳过消息ID
        if (qos > 0) {
            buffer.position(buffer.position() + 2);
        }

        // 计算payload长度
        int payloadLength = remainingLength - (2 + topicLength); // 减去主题长度字段和主题名称
        if (qos > 0) {
            payloadLength -= 2; // 减去消息ID
        }

        // 读取payload
        byte[] payload = new byte[payloadLength];
        buffer.get(payload);

        return payload;
    }

    /**
     * 从MQTT PUBLISH消息中提取主题名称
     *
     * @param mqttMessage MQTT消息字节数组
     * @return 提取的主题名称
     */
    public static String extractTopicName(byte[] mqttMessage) {
        if (mqttMessage == null || mqttMessage.length < 2) {
            throw new IllegalArgumentException("Invalid MQTT message");
        }

        ByteBuffer buffer = ByteBuffer.wrap(mqttMessage);

        // 读取固定头部第一个字节
        byte fixedHeaderByte1 = buffer.get();
        byte messageType = (byte) ((fixedHeaderByte1 >> 4) & 0x0F);

        // 确保这是PUBLISH消息
        if (messageType != PUBLISH) {
            throw new IllegalArgumentException("Not a PUBLISH message");
        }

        // 读取剩余长度
        int multiplier = 1;
        int remainingLength = 0;
        byte encodedByte;
        do {
            encodedByte = buffer.get();
            remainingLength += (encodedByte & 127) * multiplier;
            multiplier *= 128;
        } while ((encodedByte & 128) != 0);

        // 读取主题名称长度
        short topicLength = buffer.getShort();

        // 读取主题名称
        byte[] topicBytes = new byte[topicLength];
        buffer.get(topicBytes);

        return new String(topicBytes, StandardCharsets.UTF_8);
    }

    // Getters
    public byte getMessageType() {
        return (byte) ((fixedHeaderByte1 >> 4) & 0x0F);
    }

    public boolean isDup() {
        return (fixedHeaderByte1 & FLAG_DUP) != 0;
    }

    public byte getQosLevel() {
        return (byte) ((fixedHeaderByte1 & 0x06) >> 1);
    }

    public boolean isRetain() {
        return (fixedHeaderByte1 & FLAG_RETAIN) != 0;
    }

    public short getMessageId() {
        return messageId;
    }

    public byte[] getPayload() {
        return payload;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    @Override
    public String toString() {
        return "MqttMessage{" +
            "messageType=" + getMessageType() +
            ", dup=" + isDup() +
            ", qosLevel=" + getQosLevel() +
            ", retain=" + isRetain() +
            ", messageId=" + messageId +
            ", topicName=" + (topicName != null ? "'" + topicName + "'" : "null") +
            ", payloadLength=" + (payload != null ? payload.length : 0) +
            '}';
    }

}
