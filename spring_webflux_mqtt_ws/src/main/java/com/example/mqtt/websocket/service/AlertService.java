package com.example.mqtt.websocket.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;

/**
 * 告警服务
 * 提供统一的告警通知功能
 */
@Service
public class AlertService {
    
    private static final Logger logger = LoggerFactory.getLogger(AlertService.class);
    private static final Logger alertLogger = LoggerFactory.getLogger("ALERT");
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @Autowired
    private AlertProperties alertProperties;
    
    private Counter alertCounter;
    
    @PostConstruct
    public void initMetrics() {
        alertCounter = Counter.builder("alerts.sent.total")
            .description("Total number of alerts sent")
            .register(meterRegistry);
    }
    
    /**
     * 发送告警
     */
    public void sendAlert(AlertLevel level, String title, String message) {
        sendAlert(level, title, message, null);
    }
    
    /**
     * 发送告警（带标签）
     */
    public void sendAlert(AlertLevel level, String title, String message, String tag) {
        try {
            Alert alert = new Alert(level, title, message, tag);
            
            // 记录告警日志
            logAlert(alert);
            
            // 更新指标
            alertCounter.increment();
            meterRegistry.counter("alerts.by.level", "level", level.name().toLowerCase()).increment();
            
            // 异步发送告警通知
            CompletableFuture.runAsync(() -> processAlert(alert));
            
        } catch (Exception e) {
            logger.error("Failed to send alert", e);
        }
    }
    
    /**
     * 处理告警
     */
    private void processAlert(Alert alert) {
        try {
            // 根据告警级别决定处理方式
            switch (alert.getLevel()) {
                case CRITICAL:
                    processCriticalAlert(alert);
                    break;
                case HIGH:
                    processHighAlert(alert);
                    break;
                case MEDIUM:
                    processMediumAlert(alert);
                    break;
                case LOW:
                    processLowAlert(alert);
                    break;
                case INFO:
                    processInfoAlert(alert);
                    break;
            }
            
        } catch (Exception e) {
            logger.error("Failed to process alert: {}", alert.getTitle(), e);
        }
    }
    
    /**
     * 处理严重告警
     */
    private void processCriticalAlert(Alert alert) {
        logger.error("CRITICAL ALERT: {} - {}", alert.getTitle(), alert.getMessage());
        
        // 发送邮件通知
        if (alertProperties.getEmail().isEnabled()) {
            sendEmailAlert(alert);
        }
        
        // 发送短信通知
        if (alertProperties.getSms().isEnabled()) {
            sendSmsAlert(alert);
        }
        
        // 发送 Slack 通知
        if (alertProperties.getSlack().isEnabled()) {
            sendSlackAlert(alert);
        }
        
        // 记录到告警系统
        recordAlert(alert);
    }
    
    /**
     * 处理高级告警
     */
    private void processHighAlert(Alert alert) {
        logger.warn("HIGH ALERT: {} - {}", alert.getTitle(), alert.getMessage());
        
        // 发送邮件通知
        if (alertProperties.getEmail().isEnabled()) {
            sendEmailAlert(alert);
        }
        
        // 发送 Slack 通知
        if (alertProperties.getSlack().isEnabled()) {
            sendSlackAlert(alert);
        }
        
        // 记录到告警系统
        recordAlert(alert);
    }
    
    /**
     * 处理中级告警
     */
    private void processMediumAlert(Alert alert) {
        logger.warn("MEDIUM ALERT: {} - {}", alert.getTitle(), alert.getMessage());
        
        // 发送 Slack 通知
        if (alertProperties.getSlack().isEnabled()) {
            sendSlackAlert(alert);
        }
        
        // 记录到告警系统
        recordAlert(alert);
    }
    
    /**
     * 处理低级告警
     */
    private void processLowAlert(Alert alert) {
        logger.info("LOW ALERT: {} - {}", alert.getTitle(), alert.getMessage());
        
        // 只记录到告警系统
        recordAlert(alert);
    }
    
    /**
     * 处理信息告警
     */
    private void processInfoAlert(Alert alert) {
        logger.info("INFO ALERT: {} - {}", alert.getTitle(), alert.getMessage());
        
        // 只记录日志
        recordAlert(alert);
    }
    
    /**
     * 发送邮件告警
     */
    private void sendEmailAlert(Alert alert) {
        try {
            // 这里可以集成实际的邮件发送服务
            logger.info("Sending email alert: {}", alert.getTitle());
            
            // 模拟邮件发送
            Thread.sleep(100);
            
            logger.info("Email alert sent successfully: {}", alert.getTitle());
            
        } catch (Exception e) {
            logger.error("Failed to send email alert: {}", alert.getTitle(), e);
        }
    }
    
    /**
     * 发送短信告警
     */
    private void sendSmsAlert(Alert alert) {
        try {
            // 这里可以集成实际的短信发送服务
            logger.info("Sending SMS alert: {}", alert.getTitle());
            
            // 模拟短信发送
            Thread.sleep(100);
            
            logger.info("SMS alert sent successfully: {}", alert.getTitle());
            
        } catch (Exception e) {
            logger.error("Failed to send SMS alert: {}", alert.getTitle(), e);
        }
    }
    
    /**
     * 发送 Slack 告警
     */
    private void sendSlackAlert(Alert alert) {
        try {
            // 这里可以集成实际的 Slack API
            logger.info("Sending Slack alert: {}", alert.getTitle());
            
            // 模拟 Slack 消息发送
            Thread.sleep(100);
            
            logger.info("Slack alert sent successfully: {}", alert.getTitle());
            
        } catch (Exception e) {
            logger.error("Failed to send Slack alert: {}", alert.getTitle(), e);
        }
    }
    
    /**
     * 记录告警到系统
     */
    private void recordAlert(Alert alert) {
        alertLogger.info("ALERT_RECORD - Level: {}, Title: {}, Message: {}, Tag: {}, Timestamp: {}", 
            alert.getLevel(), alert.getTitle(), alert.getMessage(), alert.getTag(), alert.getTimestamp());
    }
    
    /**
     * 记录告警日志
     */
    private void logAlert(Alert alert) {
        String logMessage = String.format("[%s] %s: %s", 
            alert.getLevel(), alert.getTitle(), alert.getMessage());
        
        switch (alert.getLevel()) {
            case CRITICAL:
            case HIGH:
                logger.error(logMessage);
                break;
            case MEDIUM:
                logger.warn(logMessage);
                break;
            case LOW:
            case INFO:
                logger.info(logMessage);
                break;
        }
    }
    
    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        CRITICAL,   // 严重 - 需要立即处理
        HIGH,       // 高级 - 需要尽快处理
        MEDIUM,     // 中级 - 需要关注
        LOW,        // 低级 - 一般关注
        INFO        // 信息 - 仅记录
    }
    
    /**
     * 告警对象
     */
    public static class Alert {
        private final AlertLevel level;
        private final String title;
        private final String message;
        private final String tag;
        private final LocalDateTime timestamp;
        
        public Alert(AlertLevel level, String title, String message, String tag) {
            this.level = level;
            this.title = title;
            this.message = message;
            this.tag = tag;
            this.timestamp = LocalDateTime.now();
        }
        
        public AlertLevel getLevel() { return level; }
        public String getTitle() { return title; }
        public String getMessage() { return message; }
        public String getTag() { return tag; }
        public LocalDateTime getTimestamp() { return timestamp; }
        
        @Override
        public String toString() {
            return String.format("Alert{level=%s, title='%s', message='%s', tag='%s', timestamp=%s}", 
                level, title, message, tag, timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
    }
    
    /**
     * 告警配置属性
     */
    @ConfigurationProperties(prefix = "app.alert")
    public static class AlertProperties {
        
        private Email email = new Email();
        private Sms sms = new Sms();
        private Slack slack = new Slack();
        
        public Email getEmail() { return email; }
        public void setEmail(Email email) { this.email = email; }
        
        public Sms getSms() { return sms; }
        public void setSms(Sms sms) { this.sms = sms; }
        
        public Slack getSlack() { return slack; }
        public void setSlack(Slack slack) { this.slack = slack; }
        
        public static class Email {
            private boolean enabled = false;
            private String smtpHost;
            private int smtpPort = 587;
            private String username;
            private String password;
            private String[] recipients;
            
            public boolean isEnabled() { return enabled; }
            public void setEnabled(boolean enabled) { this.enabled = enabled; }
            
            public String getSmtpHost() { return smtpHost; }
            public void setSmtpHost(String smtpHost) { this.smtpHost = smtpHost; }
            
            public int getSmtpPort() { return smtpPort; }
            public void setSmtpPort(int smtpPort) { this.smtpPort = smtpPort; }
            
            public String getUsername() { return username; }
            public void setUsername(String username) { this.username = username; }
            
            public String getPassword() { return password; }
            public void setPassword(String password) { this.password = password; }
            
            public String[] getRecipients() { return recipients; }
            public void setRecipients(String[] recipients) { this.recipients = recipients; }
        }
        
        public static class Sms {
            private boolean enabled = false;
            private String apiKey;
            private String apiSecret;
            private String[] phoneNumbers;
            
            public boolean isEnabled() { return enabled; }
            public void setEnabled(boolean enabled) { this.enabled = enabled; }
            
            public String getApiKey() { return apiKey; }
            public void setApiKey(String apiKey) { this.apiKey = apiKey; }
            
            public String getApiSecret() { return apiSecret; }
            public void setApiSecret(String apiSecret) { this.apiSecret = apiSecret; }
            
            public String[] getPhoneNumbers() { return phoneNumbers; }
            public void setPhoneNumbers(String[] phoneNumbers) { this.phoneNumbers = phoneNumbers; }
        }
        
        public static class Slack {
            private boolean enabled = false;
            private String webhookUrl;
            private String channel;
            
            public boolean isEnabled() { return enabled; }
            public void setEnabled(boolean enabled) { this.enabled = enabled; }
            
            public String getWebhookUrl() { return webhookUrl; }
            public void setWebhookUrl(String webhookUrl) { this.webhookUrl = webhookUrl; }
            
            public String getChannel() { return channel; }
            public void setChannel(String channel) { this.channel = channel; }
        }
    }
}