package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.properties.RedisProperties;
import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.ClusterSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import jakarta.annotation.PreDestroy;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 基于 Redis 的集群同步服务实现
 */
@Service
public class RedisClusterSyncService implements ClusterSyncService {

    private static final Logger logger = LoggerFactory.getLogger(RedisClusterSyncService.class);

    private final ClusterStateManager clusterStateManager;
    private final RedisProperties redisProperties;
    private final String instanceId;
    private final int serverPort;
    
    private final AtomicBoolean running = new AtomicBoolean(false);
    private Disposable heartbeatDisposable;
    private Disposable cleanupDisposable;
    private Disposable messageListenerDisposable;

    public RedisClusterSyncService(
            ClusterStateManager clusterStateManager,
            RedisProperties redisProperties,
            @Value("${server.port:8080}") int serverPort) {
        this.clusterStateManager = clusterStateManager;
        this.redisProperties = redisProperties;
        this.serverPort = serverPort;
        this.instanceId = generateInstanceId();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        start().subscribe(
            result -> logger.info("Cluster sync service started successfully"),
            error -> logger.error("Failed to start cluster sync service", error)
        );
    }

    @PreDestroy
    public void onShutdown() {
        stop().subscribe(
            result -> logger.info("Cluster sync service stopped successfully"),
            error -> logger.error("Failed to stop cluster sync service", error)
        );
    }

    @Override
    public Mono<Void> start() {
        if (running.compareAndSet(false, true)) {
            return registerCurrentInstance()
                    .then(startHeartbeat())
                    .then(startCleanupTask())
                    .then(startMessageListener())
                    .doOnSuccess(result -> logger.info("Cluster sync service started for instance: {}", instanceId))
                    .doOnError(error -> {
                        logger.error("Failed to start cluster sync service", error);
                        running.set(false);
                    });
        }
        return Mono.empty();
    }

    @Override
    public Mono<Void> stop() {
        if (running.compareAndSet(true, false)) {
            return Mono.fromRunnable(() -> {
                        if (heartbeatDisposable != null && !heartbeatDisposable.isDisposed()) {
                            heartbeatDisposable.dispose();
                        }
                        if (cleanupDisposable != null && !cleanupDisposable.isDisposed()) {
                            cleanupDisposable.dispose();
                        }
                        if (messageListenerDisposable != null && !messageListenerDisposable.isDisposed()) {
                            messageListenerDisposable.dispose();
                        }
                    })
                    .then(clusterStateManager.unregisterInstance(instanceId))
                    .doOnSuccess(result -> logger.info("Cluster sync service stopped for instance: {}", instanceId))
                    .doOnError(error -> logger.error("Failed to stop cluster sync service", error));
        }
        return Mono.empty();
    }

    @Override
    public Mono<Void> syncClusterState() {
        return clusterStateManager.getActiveInstances()
                .flatMap(instances -> {
                    logger.debug("Current active instances: {}", instances);
                    return clusterStateManager.cleanupExpiredInstances();
                })
                .doOnSuccess(cleanedCount -> {
                    if (cleanedCount > 0) {
                        logger.info("Cleaned up {} expired instances during sync", cleanedCount);
                    }
                })
                .then();
    }

    @Override
    public Mono<Void> handleInstanceJoined(String instanceId) {
        return clusterStateManager.getInstanceInfo(instanceId)
                .doOnSuccess(instanceInfo -> {
                    if (instanceInfo != null) {
                        logger.info("Instance joined cluster: {} at {}:{}", 
                                instanceId, instanceInfo.host(), instanceInfo.port());
                        
                        // 广播实例加入事件
                        Map<String, Object> event = new HashMap<>();
                        event.put("type", "INSTANCE_JOINED");
                        event.put("instanceId", instanceId);
                        event.put("instanceInfo", instanceInfo);
                        
                        clusterStateManager.broadcastToCluster(event).subscribe();
                    }
                })
                .then();
    }

    @Override
    public Mono<Void> handleInstanceLeft(String instanceId) {
        return Mono.fromRunnable(() -> {
                    logger.info("Instance left cluster: {}", instanceId);
                    
                    // 广播实例离开事件
                    Map<String, Object> event = new HashMap<>();
                    event.put("type", "INSTANCE_LEFT");
                    event.put("instanceId", instanceId);
                    
                    clusterStateManager.broadcastToCluster(event).subscribe();
                });
    }

    @Override
    public String getCurrentInstanceId() {
        return instanceId;
    }

    /**
     * 注册当前实例
     */
    private Mono<Void> registerCurrentInstance() {
        ClusterStateManager.InstanceInfo instanceInfo = createCurrentInstanceInfo();
        return clusterStateManager.registerInstance(instanceId, instanceInfo);
    }

    /**
     * 启动心跳任务
     */
    private Mono<Void> startHeartbeat() {
        return Mono.fromRunnable(() -> {
            heartbeatDisposable = reactor.core.publisher.Flux.interval(redisProperties.cluster().heartbeatInterval())
                    .flatMap(tick -> clusterStateManager.updateHeartbeat(instanceId))
                    .doOnError(error -> logger.error("Heartbeat failed for instance: {}", instanceId, error))
                    .retry()
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe();
            
            logger.debug("Heartbeat task started for instance: {}", instanceId);
        });
    }

    /**
     * 启动清理任务
     */
    private Mono<Void> startCleanupTask() {
        return Mono.fromRunnable(() -> {
            cleanupDisposable = reactor.core.publisher.Flux.interval(redisProperties.cluster().stateSyncInterval())
                    .flatMap(tick -> syncClusterState())
                    .doOnError(error -> logger.error("Cleanup task failed", error))
                    .retry()
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe();
            
            logger.debug("Cleanup task started");
        });
    }

    /**
     * 启动消息监听器
     */
    private Mono<Void> startMessageListener() {
        return Mono.fromRunnable(() -> {
            messageListenerDisposable = clusterStateManager.listenClusterMessages()
                    .doOnNext(this::handleClusterMessage)
                    .doOnError(error -> logger.error("Message listener error", error))
                    .retry()
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe();
            
            logger.debug("Message listener started");
        });
    }

    /**
     * 处理集群消息
     */
    private void handleClusterMessage(ClusterStateManager.ClusterMessage message) {
        logger.debug("Received cluster message: {} from {}", 
                message.messageType(), message.sourceInstanceId());
        
        // 根据消息类型处理不同的集群事件
        if (message.payload() instanceof Map<?, ?> payload) {
            String eventType = (String) payload.get("type");
            
            switch (eventType) {
                case "INSTANCE_JOINED" -> {
                    String joinedInstanceId = (String) payload.get("instanceId");
                    logger.info("Received instance joined event: {}", joinedInstanceId);
                }
                case "INSTANCE_LEFT" -> {
                    String leftInstanceId = (String) payload.get("instanceId");
                    logger.info("Received instance left event: {}", leftInstanceId);
                }
                default -> logger.debug("Unknown cluster event type: {}", eventType);
            }
        }
    }

    /**
     * 创建当前实例信息
     */
    private ClusterStateManager.InstanceInfo createCurrentInstanceInfo() {
        String host;
        try {
            host = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            host = "localhost";
            logger.warn("Failed to get local host address, using localhost", e);
        }

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("startTime", Instant.now().toString());
        metadata.put("version", getClass().getPackage().getImplementationVersion());
        metadata.put("javaVersion", System.getProperty("java.version"));

        return new ClusterStateManager.InstanceInfo(
                instanceId,
                host,
                serverPort,
                Instant.now(),
                Instant.now(),
                metadata
        );
    }

    /**
     * 生成实例ID
     */
    private String generateInstanceId() {
        String hostName;
        try {
            hostName = InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            hostName = "unknown";
        }
        
        return String.format("%s-%d-%d", hostName, serverPort, System.currentTimeMillis());
    }
}