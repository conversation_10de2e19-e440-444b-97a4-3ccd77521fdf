package com.example.mqtt.websocket.controller;

import com.example.mqtt.websocket.service.MemoryMonitoringService;
import com.example.mqtt.websocket.service.WebSocketConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.HashMap;
import java.util.Map;

/**
 * 性能监控控制器
 * 提供系统性能指标的 REST API
 */
@RestController
@RequestMapping("/api/performance")
public class PerformanceMonitoringController {

    private final MemoryMonitoringService memoryMonitoringService;
    private final WebSocketConnectionManager connectionManager;
    private final ThreadMXBean threadMXBean;

    @Autowired
    public PerformanceMonitoringController(MemoryMonitoringService memoryMonitoringService,
                                         WebSocketConnectionManager connectionManager) {
        this.memoryMonitoringService = memoryMonitoringService;
        this.connectionManager = connectionManager;
        this.threadMXBean = ManagementFactory.getThreadMXBean();
    }

    /**
     * 获取内存使用统计
     */
    @GetMapping("/memory")
    public Mono<ResponseEntity<Map<String, Object>>> getMemoryStats() {
        return Mono.fromCallable(() -> {
            MemoryMonitoringService.MemoryStats stats = memoryMonitoringService.getMemoryStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("heapUsed", stats.getHeapUsed());
            response.put("heapMax", stats.getHeapMax());
            response.put("heapUsageRatio", stats.getHeapUsageRatio());
            response.put("nonHeapUsed", stats.getNonHeapUsed());
            response.put("nonHeapMax", stats.getNonHeapMax());
            response.put("nonHeapUsageRatio", stats.getNonHeapUsageRatio());
            response.put("totalGcTime", stats.getTotalGcTime());
            response.put("totalGcCount", stats.getTotalGcCount());
            response.put("averageGcTime", stats.getAverageGcTime());
            response.put("availableHeapMemory", memoryMonitoringService.getAvailableHeapMemory());
            
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 获取 WebSocket 连接统计
     */
    @GetMapping("/websocket")
    public Mono<ResponseEntity<Map<String, Object>>> getWebSocketStats() {
        return Mono.fromCallable(() -> {
            WebSocketConnectionManager.ConnectionStats stats = connectionManager.getConnectionStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("activeConnections", stats.getActiveConnections());
            response.put("maxConnections", stats.getMaxConnections());
            response.put("sessionCount", stats.getSessionCount());
            response.put("activityTrackingCount", stats.getActivityTrackingCount());
            response.put("connectionUtilization", stats.getConnectionUtilization());
            response.put("canAcceptConnection", connectionManager.canAcceptConnection());
            
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 获取线程统计
     */
    @GetMapping("/threads")
    public Mono<ResponseEntity<Map<String, Object>>> getThreadStats() {
        return Mono.fromCallable(() -> {
            Map<String, Object> response = new HashMap<>();
            response.put("threadCount", threadMXBean.getThreadCount());
            response.put("peakThreadCount", threadMXBean.getPeakThreadCount());
            response.put("daemonThreadCount", threadMXBean.getDaemonThreadCount());
            response.put("totalStartedThreadCount", threadMXBean.getTotalStartedThreadCount());
            
            // 获取死锁检测信息
            long[] deadlockedThreads = threadMXBean.findDeadlockedThreads();
            response.put("deadlockedThreadCount", deadlockedThreads != null ? deadlockedThreads.length : 0);
            
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 获取系统性能概览
     */
    @GetMapping("/overview")
    public Mono<ResponseEntity<Map<String, Object>>> getPerformanceOverview() {
        return Mono.fromCallable(() -> {
            Runtime runtime = Runtime.getRuntime();
            MemoryMonitoringService.MemoryStats memStats = memoryMonitoringService.getMemoryStats();
            WebSocketConnectionManager.ConnectionStats connStats = connectionManager.getConnectionStats();
            
            Map<String, Object> response = new HashMap<>();
            
            // 系统信息
            response.put("availableProcessors", runtime.availableProcessors());
            response.put("javaVersion", System.getProperty("java.version"));
            response.put("jvmName", System.getProperty("java.vm.name"));
            
            // 内存信息
            Map<String, Object> memory = new HashMap<>();
            memory.put("heapUsageRatio", memStats.getHeapUsageRatio());
            memory.put("heapUsedMB", memStats.getHeapUsed() / 1024 / 1024);
            memory.put("heapMaxMB", memStats.getHeapMax() / 1024 / 1024);
            memory.put("nonHeapUsedMB", memStats.getNonHeapUsed() / 1024 / 1024);
            memory.put("gcCount", memStats.getTotalGcCount());
            memory.put("averageGcTime", memStats.getAverageGcTime());
            response.put("memory", memory);
            
            // 连接信息
            Map<String, Object> connections = new HashMap<>();
            connections.put("active", connStats.getActiveConnections());
            connections.put("max", connStats.getMaxConnections());
            connections.put("utilization", connStats.getConnectionUtilization());
            response.put("websocket", connections);
            
            // 线程信息
            Map<String, Object> threads = new HashMap<>();
            threads.put("count", threadMXBean.getThreadCount());
            threads.put("peak", threadMXBean.getPeakThreadCount());
            threads.put("daemon", threadMXBean.getDaemonThreadCount());
            response.put("threads", threads);
            
            // 健康状态
            String healthStatus = "HEALTHY";
            if (memStats.getHeapUsageRatio() > 0.9) {
                healthStatus = "CRITICAL";
            } else if (memStats.getHeapUsageRatio() > 0.8 || connStats.getConnectionUtilization() > 0.9) {
                healthStatus = "WARNING";
            }
            response.put("healthStatus", healthStatus);
            
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 触发垃圾回收
     */
    @PostMapping("/gc")
    public Mono<ResponseEntity<Map<String, Object>>> triggerGarbageCollection() {
        return Mono.fromCallable(() -> {
            long beforeGc = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            
            memoryMonitoringService.triggerGc();
            
            // 等待 GC 完成
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            long afterGc = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            long freedMemory = beforeGc - afterGc;
            
            Map<String, Object> response = new HashMap<>();
            response.put("beforeGcMemoryMB", beforeGc / 1024 / 1024);
            response.put("afterGcMemoryMB", afterGc / 1024 / 1024);
            response.put("freedMemoryMB", freedMemory / 1024 / 1024);
            response.put("message", "Garbage collection triggered successfully");
            
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 获取 JVM 参数建议
     */
    @GetMapping("/jvm-recommendations")
    public Mono<ResponseEntity<Map<String, Object>>> getJvmRecommendations() {
        return Mono.fromCallable(() -> {
            Runtime runtime = Runtime.getRuntime();
            long maxMemoryMB = runtime.maxMemory() / 1024 / 1024;
            int processors = runtime.availableProcessors();
            
            Map<String, Object> response = new HashMap<>();
            
            // 基础建议
            Map<String, String> recommendations = new HashMap<>();
            recommendations.put("heapSize", "-Xmx" + Math.max(2048, maxMemoryMB) + "m -Xms" + Math.max(2048, maxMemoryMB) + "m");
            recommendations.put("gc", "-XX:+UseG1GC -XX:MaxGCPauseMillis=200");
            recommendations.put("directMemory", "-XX:MaxDirectMemorySize=" + (maxMemoryMB / 4) + "m");
            recommendations.put("compression", "-XX:+UseCompressedOops");
            recommendations.put("compilation", "-XX:+TieredCompilation");
            recommendations.put("gcLogging", "-Xlog:gc*:gc.log:time,tags");
            recommendations.put("heapDump", "-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./heapdump.hprof");
            
            response.put("recommendations", recommendations);
            
            // 当前设置分析
            Map<String, Object> currentSettings = new HashMap<>();
            currentSettings.put("maxMemoryMB", maxMemoryMB);
            currentSettings.put("totalMemoryMB", runtime.totalMemory() / 1024 / 1024);
            currentSettings.put("freeMemoryMB", runtime.freeMemory() / 1024 / 1024);
            currentSettings.put("processors", processors);
            
            response.put("currentSettings", currentSettings);
            
            // 性能建议
            Map<String, String> performanceTips = new HashMap<>();
            performanceTips.put("nettyThreads", "Set worker threads to " + (processors * 2));
            performanceTips.put("connectionPool", "Configure Redis max connections to " + Math.min(50, processors * 5));
            performanceTips.put("websocketLimit", "Set WebSocket max connections to " + Math.min(2000, maxMemoryMB * 2));
            
            response.put("performanceTips", performanceTips);
            
            return ResponseEntity.ok(response);
        });
    }

    /**
     * 重置性能统计
     */
    @PostMapping("/reset-stats")
    public Mono<ResponseEntity<Map<String, String>>> resetPerformanceStats() {
        return Mono.fromCallable(() -> {
            // 重置线程峰值计数
            threadMXBean.resetPeakThreadCount();
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Performance statistics reset successfully");
            response.put("timestamp", String.valueOf(System.currentTimeMillis()));
            
            return ResponseEntity.ok(response);
        });
    }
}