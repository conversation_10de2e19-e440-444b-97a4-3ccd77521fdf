package com.example.mqtt.websocket.service;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.List;

/**
 * 故障转移管理接口
 * 负责故障检测、自动转移和实例恢复
 */
public interface FailoverManager {

    /**
     * 启动故障检测
     * 
     * @return 启动结果
     */
    Mono<Void> startFailureDetection();

    /**
     * 停止故障检测
     * 
     * @return 停止结果
     */
    Mono<Void> stopFailureDetection();

    /**
     * 手动触发故障转移
     * 
     * @param failedInstanceId 故障实例ID
     * @return 转移结果
     */
    Mono<FailoverResult> triggerFailover(String failedInstanceId);

    /**
     * 检查实例健康状态
     * 
     * @param instanceId 实例ID
     * @return 健康检查结果
     */
    Mono<HealthCheckResult> checkInstanceHealth(String instanceId);

    /**
     * 获取故障实例列表
     * 
     * @return 故障实例列表
     */
    Mono<List<String>> getFailedInstances();

    /**
     * 尝试恢复故障实例
     * 
     * @param instanceId 实例ID
     * @return 恢复结果
     */
    Mono<RecoveryResult> attemptInstanceRecovery(String instanceId);

    /**
     * 监听故障转移事件
     * 
     * @return 故障转移事件流
     */
    Flux<FailoverEvent> listenFailoverEvents();

    /**
     * 获取故障转移配置
     * 
     * @return 配置信息
     */
    Mono<FailoverConfig> getFailoverConfig();

    /**
     * 更新故障转移配置
     * 
     * @param config 新配置
     * @return 更新结果
     */
    Mono<Void> updateFailoverConfig(FailoverConfig config);

    /**
     * 健康检查结果
     */
    record HealthCheckResult(
        String instanceId,
        boolean healthy,
        long responseTime,
        String errorMessage,
        Instant checkTime
    ) {}

    /**
     * 故障转移结果
     */
    record FailoverResult(
        String failedInstanceId,
        String targetInstanceId,
        boolean success,
        String reason,
        Instant failoverTime
    ) {}

    /**
     * 实例恢复结果
     */
    record RecoveryResult(
        String instanceId,
        boolean recovered,
        String reason,
        Instant recoveryTime
    ) {}

    /**
     * 故障转移事件
     */
    record FailoverEvent(
        String eventId,
        FailoverEventType type,
        String instanceId,
        String details,
        Instant timestamp
    ) {}

    /**
     * 故障转移事件类型
     */
    enum FailoverEventType {
        INSTANCE_FAILED,
        FAILOVER_TRIGGERED,
        FAILOVER_COMPLETED,
        INSTANCE_RECOVERED,
        HEALTH_CHECK_FAILED,
        HEALTH_CHECK_RECOVERED
    }

    /**
     * 故障转移配置
     */
    record FailoverConfig(
        int healthCheckInterval,
        int healthCheckTimeout,
        int maxRetries,
        int failureThreshold,
        int recoveryThreshold,
        boolean autoFailoverEnabled,
        boolean autoRecoveryEnabled
    ) {}
}