package com.example.mqtt.websocket.exception;

/**
 * WebSocket 异常
 */
public class WebSocketException extends RuntimeException {
    
    private final String sessionId;
    private final String userId;
    
    public WebSocketException(String sessionId, String message) {
        super(String.format("WebSocket error for session %s: %s", sessionId, message));
        this.sessionId = sessionId;
        this.userId = null;
    }
    
    public WebSocketException(String sessionId, String userId, String message) {
        super(String.format("WebSocket error for session %s (user %s): %s", sessionId, userId, message));
        this.sessionId = sessionId;
        this.userId = userId;
    }
    
    public WebSocketException(String sessionId, String message, Throwable cause) {
        super(String.format("WebSocket error for session %s: %s", sessionId, message), cause);
        this.sessionId = sessionId;
        this.userId = null;
    }
    
    public WebSocketException(String sessionId, String userId, String message, Throwable cause) {
        super(String.format("WebSocket error for session %s (user %s): %s", sessionId, userId, message), cause);
        this.sessionId = sessionId;
        this.userId = userId;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public String getUserId() {
        return userId;
    }
}