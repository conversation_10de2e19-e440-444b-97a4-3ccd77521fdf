package com.example.mqtt.websocket.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * WebSocket 配置属性
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "app.websocket")
public class WebSocketProperties {
    
    /**
     * WebSocket 端点路径
     */
    private String endpoint = "/ws";
    
    /**
     * 允许的跨域来源
     */
    private String allowedOrigins = "*";
    
    /**
     * 会话最大空闲超时时间（毫秒）
     */
    private int maxSessionIdleTimeout = 300000;
    
    /**
     * 文本消息缓冲区最大大小
     */
    private int maxTextMessageBufferSize = 8192;
    
    /**
     * 二进制消息缓冲区最大大小
     */
    private int maxBinaryMessageBufferSize = 8192;
    
    /**
     * 发送时间限制（毫秒）
     */
    private int sendTimeLimit = 10000;
    
    /**
     * 发送缓冲区大小限制
     */
    private int sendBufferSizeLimit = 524288;

    // Getters and Setters
    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAllowedOrigins() {
        return allowedOrigins;
    }

    public void setAllowedOrigins(String allowedOrigins) {
        this.allowedOrigins = allowedOrigins;
    }

    public int getMaxSessionIdleTimeout() {
        return maxSessionIdleTimeout;
    }

    public void setMaxSessionIdleTimeout(int maxSessionIdleTimeout) {
        this.maxSessionIdleTimeout = maxSessionIdleTimeout;
    }

    public int getMaxTextMessageBufferSize() {
        return maxTextMessageBufferSize;
    }

    public void setMaxTextMessageBufferSize(int maxTextMessageBufferSize) {
        this.maxTextMessageBufferSize = maxTextMessageBufferSize;
    }

    public int getMaxBinaryMessageBufferSize() {
        return maxBinaryMessageBufferSize;
    }

    public void setMaxBinaryMessageBufferSize(int maxBinaryMessageBufferSize) {
        this.maxBinaryMessageBufferSize = maxBinaryMessageBufferSize;
    }

    public int getSendTimeLimit() {
        return sendTimeLimit;
    }

    public void setSendTimeLimit(int sendTimeLimit) {
        this.sendTimeLimit = sendTimeLimit;
    }

    public int getSendBufferSizeLimit() {
        return sendBufferSizeLimit;
    }

    public void setSendBufferSizeLimit(int sendBufferSizeLimit) {
        this.sendBufferSizeLimit = sendBufferSizeLimit;
    }
}