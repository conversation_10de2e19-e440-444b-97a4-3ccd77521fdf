package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.service.HealthCheckService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认健康检查服务实现
 */
@Service
public class DefaultHealthCheckService implements HealthCheckService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultHealthCheckService.class);
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(5);

    private final Map<String, HealthChecker> healthCheckers = new ConcurrentHashMap<>();

    @Override
    public Mono<OverallHealthStatus> performHealthCheck() {
        Instant startTime = Instant.now();
        
        return getAllComponentsHealth()
            .map(componentsHealth -> {
                long totalResponseTime = Duration.between(startTime, Instant.now()).toMillis();
                HealthStatus overallStatus = determineOverallStatus(componentsHealth);
                
                return new OverallHealthStatus(
                    overallStatus,
                    componentsHealth,
                    Instant.now(),
                    totalResponseTime
                );
            })
            .doOnNext(status -> logger.debug("Overall health check completed: {}", status.status()))
            .doOnError(error -> logger.error("Health check failed", error));
    }

    @Override
    public Mono<ComponentHealth> checkComponentHealth(String componentName) {
        HealthChecker checker = healthCheckers.get(componentName);
        if (checker == null) {
            return Mono.just(new ComponentHealth(
                componentName,
                HealthStatus.UNKNOWN,
                "No health checker registered for component",
                Map.of(),
                Instant.now(),
                0L
            ));
        }

        Instant startTime = Instant.now();
        return checker.check()
            .timeout(DEFAULT_TIMEOUT)
            .map(health -> new ComponentHealth(
                componentName,
                health.status(),
                health.message(),
                health.details(),
                Instant.now(),
                Duration.between(startTime, Instant.now()).toMillis()
            ))
            .onErrorReturn(new ComponentHealth(
                componentName,
                HealthStatus.DOWN,
                "Health check failed or timed out",
                Map.of("error", "Timeout or exception occurred"),
                Instant.now(),
                Duration.between(startTime, Instant.now()).toMillis()
            ));
    }

    @Override
    public Mono<Map<String, ComponentHealth>> getAllComponentsHealth() {
        if (healthCheckers.isEmpty()) {
            return Mono.just(Map.of());
        }

        return Flux.fromIterable(healthCheckers.keySet())
            .flatMap(componentName -> 
                checkComponentHealth(componentName)
                    .map(health -> Map.entry(componentName, health))
            )
            .collectMap(Map.Entry::getKey, Map.Entry::getValue)
            .doOnNext(results -> logger.debug("Checked health for {} components", results.size()));
    }

    @Override
    public void registerHealthChecker(String componentName, HealthChecker checker) {
        if (checker == null) {
            throw new IllegalArgumentException("Health checker cannot be null");
        }
        
        healthCheckers.put(componentName, checker);
        logger.info("Registered health checker for component: {}", componentName);
    }

    @Override
    public void unregisterHealthChecker(String componentName) {
        HealthChecker removed = healthCheckers.remove(componentName);
        if (removed != null) {
            logger.info("Unregistered health checker for component: {}", componentName);
        }
    }

    /**
     * 确定整体健康状态
     */
    private HealthStatus determineOverallStatus(Map<String, ComponentHealth> componentsHealth) {
        if (componentsHealth.isEmpty()) {
            return HealthStatus.UNKNOWN;
        }

        boolean hasDown = false;
        boolean hasDegraded = false;
        boolean hasUnknown = false;

        for (ComponentHealth health : componentsHealth.values()) {
            switch (health.status()) {
                case DOWN -> hasDown = true;
                case DEGRADED -> hasDegraded = true;
                case UNKNOWN -> hasUnknown = true;
                case UP -> {
                    // Continue checking other components
                }
            }
        }

        // 如果有任何组件DOWN，整体状态为DOWN
        if (hasDown) {
            return HealthStatus.DOWN;
        }
        
        // 如果有组件DEGRADED，整体状态为DEGRADED
        if (hasDegraded) {
            return HealthStatus.DEGRADED;
        }
        
        // 如果有组件UNKNOWN，整体状态为DEGRADED
        if (hasUnknown) {
            return HealthStatus.DEGRADED;
        }
        
        // 所有组件都UP
        return HealthStatus.UP;
    }

    /**
     * 创建基本的组件健康状态
     */
    public static ComponentHealth createHealthyComponent(String componentName, String message) {
        return new ComponentHealth(
            componentName,
            HealthStatus.UP,
            message,
            Map.of(),
            Instant.now(),
            0L
        );
    }

    /**
     * 创建不健康的组件状态
     */
    public static ComponentHealth createUnhealthyComponent(String componentName, String message, Map<String, Object> details) {
        return new ComponentHealth(
            componentName,
            HealthStatus.DOWN,
            message,
            details != null ? details : Map.of(),
            Instant.now(),
            0L
        );
    }

    /**
     * 创建降级的组件状态
     */
    public static ComponentHealth createDegradedComponent(String componentName, String message, Map<String, Object> details) {
        return new ComponentHealth(
            componentName,
            HealthStatus.DEGRADED,
            message,
            details != null ? details : Map.of(),
            Instant.now(),
            0L
        );
    }
}