package com.example.mqtt.websocket.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import java.security.SecureRandom;
import java.util.Arrays;

/**
 * 加密服务实现
 * 提供 AES-256-GCM 加密和解密功能
 * 
 * <AUTHOR>
 */
@Service
public class EncryptionService {
    
    private static final Logger logger = LoggerFactory.getLogger(EncryptionService.class);
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12; // 96 bits
    private static final int GCM_TAG_LENGTH = 16; // 128 bits
    
    private final KeyManager keyManager;
    private final SecureRandom secureRandom;
    
    @Autowired
    public EncryptionService(KeyManager keyManager) {
        this.keyManager = keyManager;
        this.secureRandom = new SecureRandom();
    }
    
    /**
     * 加密数据
     * 
     * @param plaintext 明文数据
     * @param keyId 密钥ID
     * @return 加密后的数据（包含IV）
     */
    public byte[] encrypt(byte[] plaintext, String keyId) {
        try {
            SecretKey key = keyManager.getKey(keyId);
            
            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            secureRandom.nextBytes(iv);
            
            // 初始化加密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, key, gcmSpec);
            
            // 执行加密
            byte[] ciphertext = cipher.doFinal(plaintext);
            
            // 将IV和密文合并
            byte[] encryptedData = new byte[GCM_IV_LENGTH + ciphertext.length];
            System.arraycopy(iv, 0, encryptedData, 0, GCM_IV_LENGTH);
            System.arraycopy(ciphertext, 0, encryptedData, GCM_IV_LENGTH, ciphertext.length);
            
            logger.debug("Successfully encrypted data: keyId={}, plaintextSize={}, encryptedSize={}", 
                        keyId, plaintext.length, encryptedData.length);
            
            return encryptedData;
            
        } catch (Exception e) {
            logger.error("Failed to encrypt data: keyId={}, error={}", keyId, e.getMessage());
            throw new RuntimeException("Encryption failed", e);
        }
    }
    
    /**
     * 解密数据
     * 
     * @param encryptedData 加密的数据（包含IV）
     * @param keyId 密钥ID
     * @return 解密后的明文数据
     */
    public byte[] decrypt(byte[] encryptedData, String keyId) {
        try {
            if (encryptedData.length < GCM_IV_LENGTH) {
                throw new IllegalArgumentException("Encrypted data too short");
            }
            
            SecretKey key = keyManager.getKey(keyId);
            
            // 提取IV和密文
            byte[] iv = Arrays.copyOfRange(encryptedData, 0, GCM_IV_LENGTH);
            byte[] ciphertext = Arrays.copyOfRange(encryptedData, GCM_IV_LENGTH, encryptedData.length);
            
            // 初始化解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, key, gcmSpec);
            
            // 执行解密
            byte[] plaintext = cipher.doFinal(ciphertext);
            
            logger.debug("Successfully decrypted data: keyId={}, encryptedSize={}, plaintextSize={}", 
                        keyId, encryptedData.length, plaintext.length);
            
            return plaintext;
            
        } catch (Exception e) {
            logger.error("Failed to decrypt data: keyId={}, error={}", keyId, e.getMessage());
            throw new RuntimeException("Decryption failed", e);
        }
    }
    
    /**
     * 生成新的密钥ID
     * 
     * @return 密钥ID
     */
    public String generateKeyId() {
        return keyManager.generateKeyId();
    }
    
    /**
     * 验证密钥是否存在
     * 
     * @param keyId 密钥ID
     * @return 是否存在
     */
    public boolean keyExists(String keyId) {
        try {
            keyManager.getKey(keyId);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 轮换密钥
     * 
     * @param keyId 密钥ID
     */
    public void rotateKey(String keyId) {
        try {
            keyManager.rotateKey(keyId);
            logger.info("Successfully rotated key: keyId={}", keyId);
        } catch (Exception e) {
            logger.error("Failed to rotate key: keyId={}, error={}", keyId, e.getMessage());
            throw new RuntimeException("Key rotation failed", e);
        }
    }
    
    /**
     * 加密文本数据
     * 
     * @param plaintext 明文字符串
     * @param keyId 密钥ID
     * @return 加密后的数据
     */
    public byte[] encryptText(String plaintext, String keyId) {
        return encrypt(plaintext.getBytes(), keyId);
    }
    
    /**
     * 解密为文本数据
     * 
     * @param encryptedData 加密的数据
     * @param keyId 密钥ID
     * @return 解密后的字符串
     */
    public String decryptText(byte[] encryptedData, String keyId) {
        byte[] plaintext = decrypt(encryptedData, keyId);
        return new String(plaintext);
    }
    
    /**
     * 获取加密算法信息
     * 
     * @return 算法信息
     */
    public String getAlgorithmInfo() {
        return String.format("Algorithm: %s, Transformation: %s, IV Length: %d bits, Tag Length: %d bits", 
                           ALGORITHM, TRANSFORMATION, GCM_IV_LENGTH * 8, GCM_TAG_LENGTH * 8);
    }
}