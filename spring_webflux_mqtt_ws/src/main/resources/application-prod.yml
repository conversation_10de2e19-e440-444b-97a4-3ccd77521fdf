# 生产环境配置
server:
  port: 8080

spring:
  data:
    redis:
      host: ${REDIS_HOST:redis-cluster}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      ssl: ${REDIS_SSL:false}
      cluster:
        nodes: ${REDIS_CLUSTER_NODES:redis-cluster:6379}

app:
  mqtt:
    broker-url: ${MQTT_BROKER_URL:tcp://mqtt-broker:1883}
    username: ${MQTT_USERNAME:}
    password: ${MQTT_PASSWORD:}
    client-id: ${MQTT_CLIENT_ID:${spring.application.name}-${HOSTNAME:unknown}}
  
  websocket:
    allowed-origins: ${WEBSOCKET_ALLOWED_ORIGINS:https://yourdomain.com}
  
  encryption:
    master-key: ${ENCRYPTION_MASTER_KEY:}
    key-rotation-interval: ${KEY_ROTATION_INTERVAL:86400000}
  
  cluster:
    instance-id: ${CLUSTER_INSTANCE_ID:${spring.application.name}-${HOSTNAME:unknown}}

# 生产环境日志配置
logging:
  level:
    com.example.mqtt.websocket: INFO
    org.springframework.integration: WARN
    org.springframework.web.socket: WARN
  file:
    name: /var/log/spring-integration-mqtt-websocket.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30