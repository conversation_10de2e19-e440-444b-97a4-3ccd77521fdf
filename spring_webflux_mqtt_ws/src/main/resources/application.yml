server:
  port: 8080
  shutdown: graceful

spring:
  application:
    name: spring-integration-mqtt-websocket
  profiles:
    active: dev
  
  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # WebFlux 配置
  webflux:
    base-path: /api

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 应用程序自定义配置
app:
  # MQTT 配置
  mqtt:
    broker-url: tcp://localhost:1883
    client-id: ${spring.application.name}-${random.uuid}
    username: 
    password: 
    connection-timeout: 30
    keep-alive-interval: 60
    clean-session: true
    automatic-reconnect: true
    subscribe-topics:
      - "sensor/+/data"
      - "device/+/status"
      - "system/broadcast"
    publish-topics:
      - "response/+/data"
      - "notification/+"

  # WebSocket 配置
  websocket:
    endpoint: /ws
    allowed-origins: "*"
    max-session-idle-timeout: 300000
    max-text-message-buffer-size: 8192
    max-binary-message-buffer-size: 8192
    send-time-limit: 10000
    send-buffer-size-limit: 524288

  # 加密配置
  encryption:
    algorithm: AES
    key-length: 256
    transformation: AES/GCM/NoPadding
    key-rotation-interval: 86400000 # 24 hours in milliseconds
    
  # 集群配置
  cluster:
    instance-id: ${spring.application.name}-${server.port}-${random.uuid}
    health-check-interval: 30000
    failure-detection-timeout: 60000
    load-balancer-strategy: round-robin

  # 负载均衡配置
  loadbalancer:
    strategy: round_robin
    health-check-enabled: true
    health-check-interval: 30
    health-check-timeout: 5
    stats-enabled: true
    stats-reset-interval: 3600
    instance-weights:
      # 实例权重配置示例
      # instance1: 3
      # instance2: 2
      # instance3: 1

  # 故障转移配置
  failover:
    health-check-interval: 30
    health-check-timeout: 5
    max-retries: 3
    failure-threshold: 3
    recovery-threshold: 2
    auto-failover-enabled: true
    auto-recovery-enabled: true
    startup-delay: 60
    auto-start-detection: true
    notification:
      enabled: true
      webhook-url: 
      email-recipients: 
      slack-enabled: false
      slack-channel: "#alerts"

  # 重试配置
  retry:
    mqtt:
      max-attempts: 3
      initial-interval: 1000
      multiplier: 2.0
      max-interval: 10000
    encryption:
      max-attempts: 2
      back-off-period: 500

  # 告警配置
  alert:
    email:
      enabled: false
      smtp-host: smtp.example.com
      smtp-port: 587
      username: <EMAIL>
      password: password
      recipients:
        - <EMAIL>
        - <EMAIL>
    sms:
      enabled: false
      api-key: your-sms-api-key
      api-secret: your-sms-api-secret
      phone-numbers:
        - "+**********"
        - "+**********"
    slack:
      enabled: false
      webhook-url: https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
      channel: "#alerts"

# 日志配置
logging:
  level:
    com.example.mqtt.websocket: INFO
    org.springframework.integration: INFO
    org.springframework.web.socket: INFO
    org.eclipse.paho.client.mqttv3: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"