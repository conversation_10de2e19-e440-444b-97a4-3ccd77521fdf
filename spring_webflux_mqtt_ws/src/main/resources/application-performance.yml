# 性能优化配置文件
# 用于生产环境的性能调优设置

server:
  port: 8080
  netty:
    connection-timeout: 10s
    h2c-max-content-length: 10MB
    initial-buffer-size: 128
    max-chunk-size: 8192
    max-initial-line-length: 4096
    validate-headers: false

spring:
  profiles:
    active: performance
  
  # WebFlux 配置
  webflux:
    multipart:
      max-in-memory-size: 10MB
      max-disk-usage-per-part: 100MB
      max-parts: 128
    
  # 数据源配置（如果使用数据库）
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
      validation-timeout: 5000
      leak-detection-threshold: 60000

  # Jackson 配置
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null

# 应用性能配置
app:
  # Netty 配置
  netty:
    boss-threads: 1
    worker-threads: 16  # CPU 核心数 * 2
    max-connections: 2000
    pending-acquire-max-count: 4000
    pending-acquire-timeout: 45s
    max-idle-time: 30s
    max-life-time: 30m
    keep-alive: true
    tcp-no-delay: true
    so-backlog: 2048

  # WebSocket 配置
  websocket:
    max-connections: 2000
    session-timeout: 300  # 5分钟
    cleanup-interval: 60  # 1分钟
    endpoint: "/ws"
    allowed-origins: "*"
    max-session-idle-timeout: 300000  # 5分钟
    max-text-message-buffer-size: 65536  # 64KB
    max-binary-message-buffer-size: 65536  # 64KB

  # Redis 配置
  redis:
    host: localhost
    port: 6379
    database: 0
    cluster-enabled: false
    # 连接池配置
    max-active: 50
    max-idle: 20
    min-idle: 10
    max-wait: 10s
    time-between-eviction-runs: 30s
    min-evictable-idle-time: 5m
    # 超时配置
    connect-timeout: 10s
    command-timeout: 5s
    shutdown-timeout: 5s
    # 集群配置
    topology-refresh-period: 5m
    enable-periodic-refresh: true
    enable-adaptive-refresh: true

  # MQTT 配置
  mqtt:
    broker-url: tcp://localhost:1883
    client-id: spring-integration-client
    connection-timeout: 30
    keep-alive-interval: 60
    max-inflight: 100
    automatic-reconnect: true
    clean-session: true
    subscribe-topics:
      - "sensor/+/data"
      - "device/+/status"
      - "system/broadcast"

  # JVM 优化配置
  jvm:
    enable-g1gc: true
    max-gc-pause-millis: 200
    g1-heap-region-size: 16  # MB
    g1-mixed-gc-count-target: 8
    g1-old-cset-region-threshold: 10
    heap-size: "4g"
    new-ratio: "3"
    survivor-ratio: "8"
    max-direct-memory-size: 1024  # MB
    enable-compressed-oops: true
    enable-tiered-compilation: true
    compile-threshold: 10000
    enable-aggressive-opts: false

  # 加密配置
  encryption:
    algorithm: "AES"
    key-length: 256
    transformation: "AES/GCM/NoPadding"
    key-rotation-interval: 24h
    max-key-age: 168h  # 7天

  # 监控配置
  monitoring:
    memory-check-interval: 30s
    gc-log-enabled: true
    metrics-export-interval: 60s
    health-check-timeout: 5s

# Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
    tags:
      application: spring-integration-mqtt-websocket

# 日志配置
logging:
  level:
    com.example.mqtt.websocket: INFO
    org.springframework.integration: WARN
    org.springframework.web.reactive: WARN
    io.netty: WARN
    redis.clients.jedis: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30

# 性能调优建议的 JVM 参数
# 在启动时添加以下参数：
# -server
# -Xmx4g -Xms4g
# -XX:MaxDirectMemorySize=1024m
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:G1HeapRegionSize=16m
# -XX:G1MixedGCCountTarget=8
# -XX:G1OldCSetRegionThreshold=10
# -XX:NewRatio=3
# -XX:SurvivorRatio=8
# -XX:+UseCompressedOops
# -XX:+TieredCompilation
# -XX:CompileThreshold=10000
# -XX:+HeapDumpOnOutOfMemoryError
# -XX:HeapDumpPath=./heapdump.hprof
# -Xlog:gc*:gc.log:time,tags
# -XX:+UseGCLogFileRotation
# -XX:NumberOfGCLogFiles=5
# -XX:GCLogFileSize=10M