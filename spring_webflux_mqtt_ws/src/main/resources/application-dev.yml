# 开发环境配置
spring:
  data:
    redis:
      host: localhost
      port: 6379

app:
  mqtt:
    broker-url: tcp://localhost:1883
    username: dev_user
    password: dev_password
  
  websocket:
    allowed-origins: "http://localhost:3000,http://localhost:8080"
  
  encryption:
    # 开发环境使用固定密钥（生产环境应使用密钥管理服务）
    master-key: "dev-master-key-32-characters-long"

logging:
  level:
    com.example.mqtt.websocket: DEBUG
    org.springframework.integration: DEBUG
    org.springframework.web.socket: DEBUG