syntax = "proto3";

package com.example.mqtt.websocket.proto;

option java_package = "com.example.mqtt.websocket.model.proto";
option java_outer_classname = "Messages";
option java_multiple_files = true;

// 基础消息结构
message BaseMessage {
    string id = 1;
    int64 timestamp = 2;
    string type = 3;
    string source = 4;
    map<string, string> headers = 5;
    bytes payload = 6;
    bool encrypted = 7;
    string signature = 8;
}

// MQTT 消息结构
message MqttMessage {
    string topic = 1;
    int32 qos = 2;
    bool retained = 3;
    BaseMessage message = 4;
}

// WebSocket 消息结构
message WebSocketMessage {
    string session_id = 1;
    string user_id = 2;
    MessageType type = 3;
    BaseMessage message = 4;
    
    enum MessageType {
        BROADCAST = 0;
        UNICAST = 1;
        SYSTEM = 2;
    }
}