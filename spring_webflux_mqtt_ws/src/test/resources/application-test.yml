server:
  port: 0 # Random port for testing

spring:
  application:
    name: spring-integration-mqtt-websocket-test
  profiles:
    active: test
  
  # Redis 测试配置 - 使用嵌入式 Redis
  data:
    redis:
      host: localhost
      port: 6370 # 不同于默认端口避免冲突
      database: 1
      timeout: 1000ms
      lettuce:
        pool:
          max-active: 4
          max-idle: 4
          min-idle: 0
          max-wait: 1000ms

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 应用程序测试配置
app:
  # MQTT 测试配置
  mqtt:
    broker-url: tcp://localhost:1883
    client-id: ${spring.application.name}-test-${random.uuid}
    username: 
    password: 
    connection-timeout: 10
    keep-alive-interval: 30
    clean-session: true
    automatic-reconnect: true
    subscribe-topics:
      - "test/sensor/+/data"
      - "test/device/+/status"
      - "test/system/broadcast"
      - "test/encrypted/+/data"
      - "test/concurrent/traffic"
      - "test/recovery/+"
      - "test/load/+/data"
    publish-topics:
      - "test/response/+/data"
      - "test/notification/+"
      - "test/secure/+/response"

  # WebSocket 测试配置
  websocket:
    endpoint: /ws
    allowed-origins: "*"
    max-session-idle-timeout: 300000 # 5 minutes for load testing
    max-text-message-buffer-size: 8192
    max-binary-message-buffer-size: 8192
    send-time-limit: 10000
    send-buffer-size-limit: 524288

  # 加密测试配置
  encryption:
    enabled: true
    algorithm: AES
    key-length: 256
    transformation: AES/GCM/NoPadding
    key-rotation-interval: 300000 # 5 minutes for testing
    
  # 集群测试配置
  cluster:
    enabled: true
    instance-id: ${spring.application.name}-test-${server.port}-${random.uuid}
    health-check-interval: 5000
    failure-detection-timeout: 10000
    load-balancer-strategy: round-robin

# 测试日志配置
logging:
  level:
    com.example.mqtt.websocket: DEBUG
    org.springframework.integration: INFO
    org.springframework.web.socket: DEBUG
    org.eclipse.paho.client.mqttv3: WARN
    org.springframework.test: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"