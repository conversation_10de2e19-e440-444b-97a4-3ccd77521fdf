package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.FailoverManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 默认故障转移管理器测试
 */
@ExtendWith(MockitoExtension.class)
class DefaultFailoverManagerTest {

    @Mock
    private ClusterStateManager clusterStateManager;

    @Mock
    private LoadBalancer loadBalancer;

    private DefaultFailoverManager failoverManager;

    @BeforeEach
    void setUp() {
        failoverManager = new DefaultFailoverManager(clusterStateManager, loadBalancer);
        
        // 设置测试配置
        ReflectionTestUtils.setField(failoverManager, "healthCheckInterval", 1);
        ReflectionTestUtils.setField(failoverManager, "healthCheckTimeout", 1);
        ReflectionTestUtils.setField(failoverManager, "failureThreshold", 2);
        ReflectionTestUtils.setField(failoverManager, "recoveryThreshold", 2);
        ReflectionTestUtils.setField(failoverManager, "autoFailoverEnabled", true);
        ReflectionTestUtils.setField(failoverManager, "autoRecoveryEnabled", true);
    }

    @Test
    void shouldStartAndStopFailureDetection() {
        // When & Then - 启动故障检测
        StepVerifier.create(failoverManager.startFailureDetection())
            .expectComplete()
            .verify(Duration.ofSeconds(2));

        // 停止故障检测
        StepVerifier.create(failoverManager.stopFailureDetection())
            .verifyComplete();
    }

    @Test
    void shouldTriggerManualFailover() {
        // Given
        String failedInstanceId = "instance1";
        when(loadBalancer.updateInstanceHealth(anyString(), any(Boolean.class)))
            .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(failoverManager.triggerFailover(failedInstanceId))
            .assertNext(result -> {
                assertThat(result.failedInstanceId()).isEqualTo(failedInstanceId);
                assertThat(result.success()).isTrue();
                assertThat(result.reason()).contains("triggered successfully");
            })
            .verifyComplete();

        // 验证实例被标记为失败
        StepVerifier.create(failoverManager.getFailedInstances())
            .expectNextMatches(instances -> instances.contains(failedInstanceId))
            .verifyComplete();
    }

    @Test
    void shouldCheckInstanceHealth() {
        // Given
        String instanceId = "instance1";
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.just(Set.of(instanceId)));
        when(clusterStateManager.isInstanceHealthy(instanceId))
            .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(failoverManager.checkInstanceHealth(instanceId))
            .assertNext(result -> {
                assertThat(result.instanceId()).isEqualTo(instanceId);
                assertThat(result.healthy()).isTrue();
                assertThat(result.errorMessage()).isNull();
            })
            .verifyComplete();
    }

    @Test
    void shouldDetectUnhealthyInstance() {
        // Given
        String instanceId = "instance1";
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.just(Set.of(instanceId)));
        when(clusterStateManager.isInstanceHealthy(instanceId))
            .thenReturn(Mono.just(false));

        // When & Then
        StepVerifier.create(failoverManager.checkInstanceHealth(instanceId))
            .assertNext(result -> {
                assertThat(result.instanceId()).isEqualTo(instanceId);
                assertThat(result.healthy()).isFalse();
                assertThat(result.errorMessage()).contains("heartbeat check failed");
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleInstanceNotInCluster() {
        // Given
        String instanceId = "unknown-instance";
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.just(Set.of("instance1", "instance2")));

        // When & Then
        StepVerifier.create(failoverManager.checkInstanceHealth(instanceId))
            .assertNext(result -> {
                assertThat(result.instanceId()).isEqualTo(instanceId);
                assertThat(result.healthy()).isFalse();
                assertThat(result.errorMessage()).contains("not found in active instances");
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleHealthCheckTimeout() {
        // Given
        String instanceId = "instance1";
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.delay(Duration.ofSeconds(2)).then(Mono.just(Set.of(instanceId))));

        // When & Then
        StepVerifier.create(failoverManager.checkInstanceHealth(instanceId))
            .assertNext(result -> {
                assertThat(result.instanceId()).isEqualTo(instanceId);
                assertThat(result.healthy()).isFalse();
                assertThat(result.errorMessage()).contains("timeout");
            })
            .verifyComplete();
    }

    @Test
    void shouldAttemptInstanceRecovery() {
        // Given
        String instanceId = "instance1";
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.just(Set.of(instanceId)));
        when(clusterStateManager.isInstanceHealthy(instanceId))
            .thenReturn(Mono.just(true));
        when(loadBalancer.updateInstanceHealth(instanceId, true))
            .thenReturn(Mono.empty());

        // 先标记实例为失败
        StepVerifier.create(failoverManager.triggerFailover(instanceId))
            .expectNextCount(1)
            .verifyComplete();

        // When & Then - 尝试恢复
        StepVerifier.create(failoverManager.attemptInstanceRecovery(instanceId))
            .assertNext(result -> {
                assertThat(result.instanceId()).isEqualTo(instanceId);
                assertThat(result.recovered()).isTrue();
                assertThat(result.reason()).contains("health check passed");
            })
            .verifyComplete();

        // 验证实例不再在失败列表中
        StepVerifier.create(failoverManager.getFailedInstances())
            .expectNextMatches(instances -> !instances.contains(instanceId))
            .verifyComplete();
    }

    @Test
    void shouldFailRecoveryForUnhealthyInstance() {
        // Given
        String instanceId = "instance1";
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.just(Set.of(instanceId)));
        when(clusterStateManager.isInstanceHealthy(instanceId))
            .thenReturn(Mono.just(false));

        // When & Then
        StepVerifier.create(failoverManager.attemptInstanceRecovery(instanceId))
            .assertNext(result -> {
                assertThat(result.instanceId()).isEqualTo(instanceId);
                assertThat(result.recovered()).isFalse();
                assertThat(result.reason()).contains("heartbeat check failed");
            })
            .verifyComplete();
    }

    @Test
    void shouldListenToFailoverEvents() {
        // Given
        String instanceId = "instance1";
        when(loadBalancer.updateInstanceHealth(anyString(), any(Boolean.class)))
            .thenReturn(Mono.empty());

        // When - 订阅事件流
        StepVerifier.create(failoverManager.listenFailoverEvents().take(1))
            .then(() -> {
                // 触发故障转移以生成事件
                failoverManager.triggerFailover(instanceId).subscribe();
            })
            .assertNext(event -> {
                assertThat(event.instanceId()).isEqualTo(instanceId);
                assertThat(event.type()).isEqualTo(FailoverManager.FailoverEventType.FAILOVER_TRIGGERED);
            })
            .verifyComplete();
    }

    @Test
    void shouldGetAndUpdateFailoverConfig() {
        // When & Then - 获取配置
        StepVerifier.create(failoverManager.getFailoverConfig())
            .assertNext(config -> {
                assertThat(config.healthCheckInterval()).isEqualTo(1);
                assertThat(config.failureThreshold()).isEqualTo(2);
                assertThat(config.autoFailoverEnabled()).isTrue();
            })
            .verifyComplete();

        // 更新配置
        FailoverManager.FailoverConfig newConfig = new FailoverManager.FailoverConfig(
            5, 3, 2, 3, 1, false, false
        );

        StepVerifier.create(failoverManager.updateFailoverConfig(newConfig))
            .verifyComplete();

        // 验证配置已更新
        StepVerifier.create(failoverManager.getFailoverConfig())
            .assertNext(config -> {
                assertThat(config.healthCheckInterval()).isEqualTo(5);
                assertThat(config.healthCheckTimeout()).isEqualTo(3);
                assertThat(config.autoFailoverEnabled()).isFalse();
            })
            .verifyComplete();
    }

    @Test
    void shouldReturnEmptyFailedInstancesInitially() {
        // When & Then
        StepVerifier.create(failoverManager.getFailedInstances())
            .expectNext(java.util.List.of())
            .verifyComplete();
    }
}