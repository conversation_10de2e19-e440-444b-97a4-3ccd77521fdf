package com.example.mqtt.websocket.performance;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Redis 性能测试
 * 测试 Redis 连接池和操作性能
 */
@SpringBootTest
@ActiveProfiles("test")
public class RedisPerformanceTest {

    private static final Logger logger = LoggerFactory.getLogger(RedisPerformanceTest.class);

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired(required = false)
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;

    /**
     * Redis 同步操作性能测试
     */
    @Test
    public void testRedisSyncPerformance() throws Exception {
        if (redisTemplate == null) {
            logger.warn("RedisTemplate not available, skipping sync performance test");
            return;
        }

        logger.info("Starting Redis sync performance test");
        
        int operationCount = 1000;
        int concurrentThreads = 20;
        AtomicInteger completedOperations = new AtomicInteger(0);
        AtomicInteger failedOperations = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(concurrentThreads);
        
        try {
            CompletableFuture<?>[] futures = new CompletableFuture[operationCount];
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < operationCount; i++) {
                final int operationId = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    long opStartTime = System.currentTimeMillis();
                    
                    try {
                        String key = "perf-test-sync-" + operationId;
                        String value = "test-value-" + operationId + "-" + System.currentTimeMillis();
                        
                        // SET 操作
                        redisTemplate.opsForValue().set(key, value, Duration.ofMinutes(5));
                        
                        // GET 操作
                        Object retrievedValue = redisTemplate.opsForValue().get(key);
                        
                        // 验证数据
                        if (value.equals(retrievedValue)) {
                            completedOperations.incrementAndGet();
                        } else {
                            failedOperations.incrementAndGet();
                        }
                        
                        // DELETE 操作
                        redisTemplate.delete(key);
                        
                        long responseTime = System.currentTimeMillis() - opStartTime;
                        totalResponseTime.addAndGet(responseTime);
                        
                    } catch (Exception e) {
                        failedOperations.incrementAndGet();
                        logger.warn("Redis sync operation failed for operation {}: {}", 
                            operationId, e.getMessage());
                    }
                }, executor);
            }
            
            CompletableFuture.allOf(futures).get(60, TimeUnit.SECONDS);
            long totalTime = System.currentTimeMillis() - startTime;
            
            // 计算性能指标
            int completed = completedOperations.get();
            int failed = failedOperations.get();
            double successRate = (double) completed / (completed + failed) * 100;
            double avgResponseTime = completed > 0 ? (double) totalResponseTime.get() / completed : 0;
            double operationsPerSecond = (double) completed / (totalTime / 1000.0);
            
            logger.info("Redis Sync Performance Results:");
            logger.info("  Total Operations: {}", operationCount);
            logger.info("  Completed Operations: {}", completed);
            logger.info("  Failed Operations: {}", failed);
            logger.info("  Success Rate: {:.2f}%", successRate);
            logger.info("  Average Response Time: {:.2f} ms", avgResponseTime);
            logger.info("  Operations Per Second: {:.2f}", operationsPerSecond);
            logger.info("  Total Test Time: {} ms", totalTime);
            
            // 性能断言
            assert successRate > 95 : "Redis sync success rate should be greater than 95%";
            assert avgResponseTime < 100 : "Average response time should be less than 100ms";
            assert operationsPerSecond > 50 : "Should handle at least 50 operations per second";
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
        }
    }

    /**
     * Redis 响应式操作性能测试
     */
    @Test
    public void testRedisReactivePerformance() throws Exception {
        if (reactiveRedisTemplate == null) {
            logger.warn("ReactiveRedisTemplate not available, skipping reactive performance test");
            return;
        }

        logger.info("Starting Redis reactive performance test");
        
        int operationCount = 1000;
        AtomicInteger completedOperations = new AtomicInteger(0);
        AtomicInteger failedOperations = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        
        long startTime = System.currentTimeMillis();
        
        // 创建操作流
        Flux<Integer> operations = Flux.range(0, operationCount)
            .flatMap(i -> {
                long opStartTime = System.currentTimeMillis();
                String key = "perf-test-reactive-" + i;
                String value = "test-value-" + i + "-" + System.currentTimeMillis();
                
                return reactiveRedisTemplate.opsForValue().set(key, value, Duration.ofMinutes(5))
                    .then(reactiveRedisTemplate.opsForValue().get(key))
                    .cast(String.class)
                    .flatMap(retrievedValue -> {
                        if (value.equals(retrievedValue)) {
                            completedOperations.incrementAndGet();
                        } else {
                            failedOperations.incrementAndGet();
                        }
                        return reactiveRedisTemplate.delete(key);
                    })
                    .doOnSuccess(v -> {
                        long responseTime = System.currentTimeMillis() - opStartTime;
                        totalResponseTime.addAndGet(responseTime);
                    })
                    .doOnError(e -> {
                        failedOperations.incrementAndGet();
                        logger.warn("Redis reactive operation failed for operation {}: {}", i, e.getMessage());
                    })
                    .onErrorReturn(0L)
                    .thenReturn(i);
            }, 50); // 并发度限制为50
        
        // 执行所有操作
        operations.collectList()
            .timeout(Duration.ofSeconds(60))
            .block();
        
        long totalTime = System.currentTimeMillis() - startTime;
        
        // 计算性能指标
        int completed = completedOperations.get();
        int failed = failedOperations.get();
        double successRate = (double) completed / (completed + failed) * 100;
        double avgResponseTime = completed > 0 ? (double) totalResponseTime.get() / completed : 0;
        double operationsPerSecond = (double) completed / (totalTime / 1000.0);
        
        logger.info("Redis Reactive Performance Results:");
        logger.info("  Total Operations: {}", operationCount);
        logger.info("  Completed Operations: {}", completed);
        logger.info("  Failed Operations: {}", failed);
        logger.info("  Success Rate: {:.2f}%", successRate);
        logger.info("  Average Response Time: {:.2f} ms", avgResponseTime);
        logger.info("  Operations Per Second: {:.2f}", operationsPerSecond);
        logger.info("  Total Test Time: {} ms", totalTime);
        
        // 性能断言
        assert successRate > 95 : "Redis reactive success rate should be greater than 95%";
        assert avgResponseTime < 50 : "Average response time should be less than 50ms";
        assert operationsPerSecond > 100 : "Should handle at least 100 operations per second";
    }

    /**
     * Redis 连接池压力测试
     */
    @Test
    public void testRedisConnectionPoolStress() throws Exception {
        if (redisTemplate == null) {
            logger.warn("RedisTemplate not available, skipping connection pool stress test");
            return;
        }

        logger.info("Starting Redis connection pool stress test");
        
        int concurrentConnections = 100;
        int operationsPerConnection = 50;
        AtomicInteger totalOperations = new AtomicInteger(0);
        AtomicInteger failedOperations = new AtomicInteger(0);
        AtomicLong maxResponseTime = new AtomicLong(0);
        AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
        
        ExecutorService executor = Executors.newFixedThreadPool(concurrentConnections);
        
        try {
            CompletableFuture<?>[] futures = new CompletableFuture[concurrentConnections];
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < concurrentConnections; i++) {
                final int connectionId = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    for (int j = 0; j < operationsPerConnection; j++) {
                        long opStartTime = System.currentTimeMillis();
                        
                        try {
                            String key = "stress-test-" + connectionId + "-" + j;
                            String value = "value-" + connectionId + "-" + j;
                            
                            // 执行多种操作
                            redisTemplate.opsForValue().set(key, value);
                            redisTemplate.opsForValue().get(key);
                            redisTemplate.expire(key, Duration.ofMinutes(1));
                            redisTemplate.hasKey(key);
                            redisTemplate.delete(key);
                            
                            long responseTime = System.currentTimeMillis() - opStartTime;
                            maxResponseTime.updateAndGet(current -> Math.max(current, responseTime));
                            minResponseTime.updateAndGet(current -> Math.min(current, responseTime));
                            
                            totalOperations.incrementAndGet();
                            
                        } catch (Exception e) {
                            failedOperations.incrementAndGet();
                            logger.warn("Redis stress operation failed for connection {} operation {}: {}", 
                                connectionId, j, e.getMessage());
                        }
                    }
                }, executor);
            }
            
            CompletableFuture.allOf(futures).get(120, TimeUnit.SECONDS);
            long totalTime = System.currentTimeMillis() - startTime;
            
            // 计算性能指标
            int completed = totalOperations.get();
            int failed = failedOperations.get();
            double successRate = (double) completed / (completed + failed) * 100;
            double operationsPerSecond = (double) completed / (totalTime / 1000.0);
            
            logger.info("Redis Connection Pool Stress Test Results:");
            logger.info("  Concurrent Connections: {}", concurrentConnections);
            logger.info("  Operations Per Connection: {}", operationsPerConnection);
            logger.info("  Total Expected Operations: {}", concurrentConnections * operationsPerConnection);
            logger.info("  Completed Operations: {}", completed);
            logger.info("  Failed Operations: {}", failed);
            logger.info("  Success Rate: {:.2f}%", successRate);
            logger.info("  Operations Per Second: {:.2f}", operationsPerSecond);
            logger.info("  Max Response Time: {} ms", maxResponseTime.get());
            logger.info("  Min Response Time: {} ms", minResponseTime.get());
            logger.info("  Total Test Time: {} ms", totalTime);
            
            // 性能断言
            assert successRate > 90 : "Redis stress test success rate should be greater than 90%";
            assert operationsPerSecond > 200 : "Should handle at least 200 operations per second";
            assert maxResponseTime.get() < 5000 : "Max response time should be less than 5000ms";
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
        }
    }

    /**
     * Redis 大数据量测试
     */
    @Test
    public void testRedisLargeDataPerformance() throws Exception {
        if (redisTemplate == null) {
            logger.warn("RedisTemplate not available, skipping large data performance test");
            return;
        }

        logger.info("Starting Redis large data performance test");
        
        int[] dataSizes = {1024, 10240, 102400, 1048576}; // 1KB, 10KB, 100KB, 1MB
        
        for (int dataSize : dataSizes) {
            logger.info("Testing with data size: {} bytes", dataSize);
            
            // 创建测试数据
            StringBuilder dataBuilder = new StringBuilder();
            for (int i = 0; i < dataSize; i++) {
                dataBuilder.append((char) ('A' + (i % 26)));
            }
            String testData = dataBuilder.toString();
            
            AtomicLong totalSetTime = new AtomicLong(0);
            AtomicLong totalGetTime = new AtomicLong(0);
            int operationCount = 100;
            
            for (int i = 0; i < operationCount; i++) {
                String key = "large-data-test-" + dataSize + "-" + i;
                
                // SET 操作
                long setStartTime = System.currentTimeMillis();
                redisTemplate.opsForValue().set(key, testData, Duration.ofMinutes(5));
                long setTime = System.currentTimeMillis() - setStartTime;
                totalSetTime.addAndGet(setTime);
                
                // GET 操作
                long getStartTime = System.currentTimeMillis();
                Object retrievedData = redisTemplate.opsForValue().get(key);
                long getTime = System.currentTimeMillis() - getStartTime;
                totalGetTime.addAndGet(getTime);
                
                // 验证数据完整性
                assert testData.equals(retrievedData) : "Data integrity check failed";
                
                // 清理
                redisTemplate.delete(key);
            }
            
            double avgSetTime = (double) totalSetTime.get() / operationCount;
            double avgGetTime = (double) totalGetTime.get() / operationCount;
            double setThroughput = (double) dataSize * operationCount / (totalSetTime.get() / 1000.0) / 1024 / 1024; // MB/s
            double getThroughput = (double) dataSize * operationCount / (totalGetTime.get() / 1000.0) / 1024 / 1024; // MB/s
            
            logger.info("Large Data Performance Results for {} bytes:", dataSize);
            logger.info("  Average SET Time: {:.2f} ms", avgSetTime);
            logger.info("  Average GET Time: {:.2f} ms", avgGetTime);
            logger.info("  SET Throughput: {:.2f} MB/s", setThroughput);
            logger.info("  GET Throughput: {:.2f} MB/s", getThroughput);
            
            // 性能断言（根据数据大小调整期望值）
            if (dataSize <= 10240) { // 10KB 以下
                assert avgSetTime < 50 : "SET time for small data should be less than 50ms";
                assert avgGetTime < 50 : "GET time for small data should be less than 50ms";
            } else if (dataSize <= 102400) { // 100KB 以下
                assert avgSetTime < 200 : "SET time for medium data should be less than 200ms";
                assert avgGetTime < 200 : "GET time for medium data should be less than 200ms";
            } else { // 1MB
                assert avgSetTime < 1000 : "SET time for large data should be less than 1000ms";
                assert avgGetTime < 1000 : "GET time for large data should be less than 1000ms";
            }
        }
    }
}