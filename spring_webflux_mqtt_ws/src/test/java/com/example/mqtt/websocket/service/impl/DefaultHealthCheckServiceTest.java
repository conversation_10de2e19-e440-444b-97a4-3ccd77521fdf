package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.service.HealthCheckService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 默认健康检查服务测试
 */
class DefaultHealthCheckServiceTest {

    private DefaultHealthCheckService healthCheckService;

    @BeforeEach
    void setUp() {
        healthCheckService = new DefaultHealthCheckService();
    }

    @Test
    void shouldRegisterAndCheckHealthChecker() {
        // Given
        String componentName = "test-component";
        HealthCheckService.HealthChecker checker = () -> Mono.just(
            DefaultHealthCheckService.createHealthyComponent(componentName, "Component is healthy")
        );

        // When
        healthCheckService.registerHealthChecker(componentName, checker);

        // Then
        StepVerifier.create(healthCheckService.checkComponentHealth(componentName))
            .assertNext(health -> {
                assertThat(health.componentName()).isEqualTo(componentName);
                assertThat(health.status()).isEqualTo(HealthCheckService.HealthStatus.UP);
                assertThat(health.message()).isEqualTo("Component is healthy");
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleUnregisteredComponent() {
        // Given
        String unknownComponent = "unknown-component";

        // When & Then
        StepVerifier.create(healthCheckService.checkComponentHealth(unknownComponent))
            .assertNext(health -> {
                assertThat(health.componentName()).isEqualTo(unknownComponent);
                assertThat(health.status()).isEqualTo(HealthCheckService.HealthStatus.UNKNOWN);
                assertThat(health.message()).contains("No health checker registered");
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleHealthCheckTimeout() {
        // Given
        String componentName = "slow-component";
        HealthCheckService.HealthChecker slowChecker = () -> 
            Mono.delay(Duration.ofSeconds(10))
                .then(Mono.just(DefaultHealthCheckService.createHealthyComponent(componentName, "Slow response")));

        healthCheckService.registerHealthChecker(componentName, slowChecker);

        // When & Then
        StepVerifier.create(healthCheckService.checkComponentHealth(componentName))
            .assertNext(health -> {
                assertThat(health.componentName()).isEqualTo(componentName);
                assertThat(health.status()).isEqualTo(HealthCheckService.HealthStatus.DOWN);
                assertThat(health.message()).contains("failed or timed out");
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleHealthCheckError() {
        // Given
        String componentName = "error-component";
        HealthCheckService.HealthChecker errorChecker = () -> 
            Mono.error(new RuntimeException("Health check failed"));

        healthCheckService.registerHealthChecker(componentName, errorChecker);

        // When & Then
        StepVerifier.create(healthCheckService.checkComponentHealth(componentName))
            .assertNext(health -> {
                assertThat(health.componentName()).isEqualTo(componentName);
                assertThat(health.status()).isEqualTo(HealthCheckService.HealthStatus.DOWN);
                assertThat(health.message()).contains("failed or timed out");
            })
            .verifyComplete();
    }

    @Test
    void shouldGetAllComponentsHealth() {
        // Given
        healthCheckService.registerHealthChecker("component1", () -> 
            Mono.just(DefaultHealthCheckService.createHealthyComponent("component1", "OK")));
        healthCheckService.registerHealthChecker("component2", () -> 
            Mono.just(DefaultHealthCheckService.createUnhealthyComponent("component2", "Failed", Map.of())));

        // When & Then
        StepVerifier.create(healthCheckService.getAllComponentsHealth())
            .assertNext(healthMap -> {
                assertThat(healthMap).hasSize(2);
                assertThat(healthMap.get("component1").status()).isEqualTo(HealthCheckService.HealthStatus.UP);
                assertThat(healthMap.get("component2").status()).isEqualTo(HealthCheckService.HealthStatus.DOWN);
            })
            .verifyComplete();
    }

    @Test
    void shouldPerformOverallHealthCheck() {
        // Given
        healthCheckService.registerHealthChecker("healthy-component", () -> 
            Mono.just(DefaultHealthCheckService.createHealthyComponent("healthy-component", "OK")));
        healthCheckService.registerHealthChecker("degraded-component", () -> 
            Mono.just(DefaultHealthCheckService.createDegradedComponent("degraded-component", "Degraded", Map.of())));

        // When & Then
        StepVerifier.create(healthCheckService.performHealthCheck())
            .assertNext(overallHealth -> {
                assertThat(overallHealth.status()).isEqualTo(HealthCheckService.HealthStatus.DEGRADED);
                assertThat(overallHealth.components()).hasSize(2);
                assertThat(overallHealth.totalResponseTime()).isGreaterThan(0);
            })
            .verifyComplete();
    }

    @Test
    void shouldDetermineOverallStatusAsUp() {
        // Given - 所有组件都健康
        healthCheckService.registerHealthChecker("component1", () -> 
            Mono.just(DefaultHealthCheckService.createHealthyComponent("component1", "OK")));
        healthCheckService.registerHealthChecker("component2", () -> 
            Mono.just(DefaultHealthCheckService.createHealthyComponent("component2", "OK")));

        // When & Then
        StepVerifier.create(healthCheckService.performHealthCheck())
            .assertNext(overallHealth -> {
                assertThat(overallHealth.status()).isEqualTo(HealthCheckService.HealthStatus.UP);
            })
            .verifyComplete();
    }

    @Test
    void shouldDetermineOverallStatusAsDown() {
        // Given - 有组件DOWN
        healthCheckService.registerHealthChecker("healthy-component", () -> 
            Mono.just(DefaultHealthCheckService.createHealthyComponent("healthy-component", "OK")));
        healthCheckService.registerHealthChecker("down-component", () -> 
            Mono.just(DefaultHealthCheckService.createUnhealthyComponent("down-component", "Failed", Map.of())));

        // When & Then
        StepVerifier.create(healthCheckService.performHealthCheck())
            .assertNext(overallHealth -> {
                assertThat(overallHealth.status()).isEqualTo(HealthCheckService.HealthStatus.DOWN);
            })
            .verifyComplete();
    }

    @Test
    void shouldUnregisterHealthChecker() {
        // Given
        String componentName = "test-component";
        healthCheckService.registerHealthChecker(componentName, () -> 
            Mono.just(DefaultHealthCheckService.createHealthyComponent(componentName, "OK")));

        // When
        healthCheckService.unregisterHealthChecker(componentName);

        // Then
        StepVerifier.create(healthCheckService.checkComponentHealth(componentName))
            .assertNext(health -> {
                assertThat(health.status()).isEqualTo(HealthCheckService.HealthStatus.UNKNOWN);
                assertThat(health.message()).contains("No health checker registered");
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleEmptyHealthCheckers() {
        // When & Then
        StepVerifier.create(healthCheckService.getAllComponentsHealth())
            .expectNext(Map.of())
            .verifyComplete();

        StepVerifier.create(healthCheckService.performHealthCheck())
            .assertNext(overallHealth -> {
                assertThat(overallHealth.status()).isEqualTo(HealthCheckService.HealthStatus.UNKNOWN);
                assertThat(overallHealth.components()).isEmpty();
            })
            .verifyComplete();
    }

    @Test
    void shouldRejectNullHealthChecker() {
        // When & Then
        org.junit.jupiter.api.Assertions.assertThrows(
            IllegalArgumentException.class,
            () -> healthCheckService.registerHealthChecker("test", null)
        );
    }

    @Test
    void shouldCreateHealthyComponent() {
        // When
        HealthCheckService.ComponentHealth health = 
            DefaultHealthCheckService.createHealthyComponent("test", "All good");

        // Then
        assertThat(health.componentName()).isEqualTo("test");
        assertThat(health.status()).isEqualTo(HealthCheckService.HealthStatus.UP);
        assertThat(health.message()).isEqualTo("All good");
        assertThat(health.details()).isEmpty();
    }

    @Test
    void shouldCreateUnhealthyComponent() {
        // Given
        Map<String, Object> details = Map.of("error", "Connection failed", "code", 500);

        // When
        HealthCheckService.ComponentHealth health = 
            DefaultHealthCheckService.createUnhealthyComponent("test", "Failed", details);

        // Then
        assertThat(health.componentName()).isEqualTo("test");
        assertThat(health.status()).isEqualTo(HealthCheckService.HealthStatus.DOWN);
        assertThat(health.message()).isEqualTo("Failed");
        assertThat(health.details()).isEqualTo(details);
    }

    @Test
    void shouldCreateDegradedComponent() {
        // Given
        Map<String, Object> details = Map.of("warning", "High latency", "latency", 2000);

        // When
        HealthCheckService.ComponentHealth health = 
            DefaultHealthCheckService.createDegradedComponent("test", "Slow", details);

        // Then
        assertThat(health.componentName()).isEqualTo("test");
        assertThat(health.status()).isEqualTo(HealthCheckService.HealthStatus.DEGRADED);
        assertThat(health.message()).isEqualTo("Slow");
        assertThat(health.details()).isEqualTo(details);
    }
}