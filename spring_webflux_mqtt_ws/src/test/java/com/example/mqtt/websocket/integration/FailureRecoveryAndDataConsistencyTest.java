package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.eclipse.paho.client.mqttv3.*;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.socket.*;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 故障恢复和数据一致性测试
 * 测试系统在各种故障场景下的恢复能力和数据一致性保证
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.cluster.enabled=true",
    "app.cluster.health-check-interval=5000",
    "app.cluster.failure-detection-timeout=10000",
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.mqtt.automatic-reconnect=true",
    "app.websocket.endpoint=/ws",
    "app.redis.host=localhost",
    "app.redis.port=6370",
    "logging.level.com.example.mqtt.websocket=DEBUG"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class FailureRecoveryAndDataConsistencyTest {
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private ClusterStateManager clusterStateManager;
    
    @Autowired
    private LoadBalancer loadBalancer;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private ObjectMapper objectMapper;
    private ExecutorService executorService;
    private List<WebSocketSession> webSocketSessions;
    private List<MqttClient> mqttClients;
    
    // 数据一致性跟踪
    private Map<String, Object> expectedDataState;
    private AtomicInteger messageSequenceNumber;
    private Set<String> processedMessageIds;
    
    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        executorService = Executors.newFixedThreadPool(20);
        webSocketSessions = Collections.synchronizedList(new ArrayList<>());
        mqttClients = Collections.synchronizedList(new ArrayList<>());
        
        expectedDataState = new ConcurrentHashMap<>();
        messageSequenceNumber = new AtomicInteger(0);
        processedMessageIds = Collections.synchronizedSet(new HashSet<>());
        
        // 注册测试实例
        ClusterStateManager.InstanceInfo primaryInfo = createTestInstanceInfo("test-instance-primary");
        ClusterStateManager.InstanceInfo backupInfo = createTestInstanceInfo("test-instance-backup");
        clusterStateManager.registerInstance("test-instance-primary", primaryInfo).block();
        clusterStateManager.registerInstance("test-instance-backup", backupInfo).block();
        
        // 等待集群状态稳定
        Thread.sleep(2000);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        // 清理连接
        for (WebSocketSession session : webSocketSessions) {
            try {
                if (session.isOpen()) {
                    session.close();
                }
            } catch (Exception e) {
                System.err.println("Error closing WebSocket session: " + e.getMessage());
            }
        }
        
        for (MqttClient client : mqttClients) {
            try {
                if (client.isConnected()) {
                    client.disconnect();
                    client.close();
                }
            } catch (Exception e) {
                System.err.println("Error closing MQTT client: " + e.getMessage());
            }
        }
        
        if (executorService != null) {
            executorService.shutdown();
            executorService.awaitTermination(10, TimeUnit.SECONDS);
        }
        
        // 清理 Redis 测试数据
        try {
            redisTemplate.getConnectionFactory().getConnection().flushDb();
        } catch (Exception e) {
            System.err.println("Error cleaning Redis: " + e.getMessage());
        }
    }
    
    @Test
    @Order(1)
    void testWebSocketConnectionFailureRecovery() throws Exception {
        System.out.println("Testing WebSocket connection failure recovery");
        
        // 建立初始连接
        int connectionCount = 10;
        CountDownLatch initialConnectionLatch = new CountDownLatch(connectionCount);
        AtomicInteger successfulConnections = new AtomicInteger(0);
        
        for (int i = 0; i < connectionCount; i++) {
            final int clientId = i;
            
            executorService.submit(() -> {
                try {
                    WebSocketSession session = createWebSocketConnection("recoveryTestUser" + clientId);
                    webSocketSessions.add(session);
                    successfulConnections.incrementAndGet();
                } catch (Exception e) {
                    System.err.println("Failed to create initial connection " + clientId + ": " + e.getMessage());
                } finally {
                    initialConnectionLatch.countDown();
                }
            });
        }
        
        assertTrue(initialConnectionLatch.await(30, TimeUnit.SECONDS), "Initial connections should be established");
        System.out.println("Initial connections established: " + successfulConnections.get());
        
        // 模拟连接故障（强制关闭一些连接）
        int failureCount = connectionCount / 2;
        List<WebSocketSession> failedSessions = new ArrayList<>();
        
        for (int i = 0; i < failureCount; i++) {
            WebSocketSession session = webSocketSessions.get(i);
            if (session.isOpen()) {
                session.close(CloseStatus.GOING_AWAY);
                failedSessions.add(session);
            }
        }
        
        System.out.println("Simulated " + failureCount + " connection failures");
        Thread.sleep(2000);
        
        // 验证故障检测
        int activeConnections = 0;
        for (WebSocketSession session : webSocketSessions) {
            if (session.isOpen()) {
                activeConnections++;
            }
        }
        
        assertEquals(connectionCount - failureCount, activeConnections, 
            "Active connections should match expected count after failures");
        
        // 模拟连接恢复
        CountDownLatch recoveryLatch = new CountDownLatch(failureCount);
        AtomicInteger recoveredConnections = new AtomicInteger(0);
        
        for (int i = 0; i < failureCount; i++) {
            final int clientId = connectionCount + i; // 新的客户端ID
            
            executorService.submit(() -> {
                try {
                    WebSocketSession newSession = createWebSocketConnection("recoveredUser" + clientId);
                    webSocketSessions.add(newSession);
                    recoveredConnections.incrementAndGet();
                } catch (Exception e) {
                    System.err.println("Failed to recover connection " + clientId + ": " + e.getMessage());
                } finally {
                    recoveryLatch.countDown();
                }
            });
        }
        
        assertTrue(recoveryLatch.await(30, TimeUnit.SECONDS), "Connection recovery should complete");
        System.out.println("Recovered connections: " + recoveredConnections.get());
        
        // 验证恢复后的连接功能
        String testMessage = objectMapper.writeValueAsString(Map.of(
            "type", "recovery_test",
            "timestamp", System.currentTimeMillis(),
            "message", "Testing recovered connections"
        ));
        
        int workingConnections = 0;
        for (WebSocketSession session : webSocketSessions) {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(testMessage));
                    workingConnections++;
                } catch (Exception e) {
                    System.err.println("Failed to send test message: " + e.getMessage());
                }
            }
        }
        
        assertTrue(workingConnections >= connectionCount * 0.8, 
            "At least 80% of connections should be working after recovery");
        
        System.out.println("WebSocket connection failure recovery test completed successfully");
    }
    
    @Test
    @Order(2)
    void testMqttConnectionFailureRecovery() throws Exception {
        System.out.println("Testing MQTT connection failure recovery");
        
        // 建立初始 MQTT 连接
        int mqttConnectionCount = 5;
        CountDownLatch mqttConnectionLatch = new CountDownLatch(mqttConnectionCount);
        AtomicInteger successfulMqttConnections = new AtomicInteger(0);
        
        for (int i = 0; i < mqttConnectionCount; i++) {
            final int clientId = i;
            
            executorService.submit(() -> {
                try {
                    MqttClient client = createMqttConnection("mqttRecoveryTest" + clientId);
                    mqttClients.add(client);
                    successfulMqttConnections.incrementAndGet();
                } catch (Exception e) {
                    System.err.println("Failed to create MQTT connection " + clientId + ": " + e.getMessage());
                } finally {
                    mqttConnectionLatch.countDown();
                }
            });
        }
        
        assertTrue(mqttConnectionLatch.await(30, TimeUnit.SECONDS), "MQTT connections should be established");
        System.out.println("Initial MQTT connections: " + successfulMqttConnections.get());
        
        // 模拟 MQTT 连接故障
        int mqttFailureCount = mqttConnectionCount / 2;
        for (int i = 0; i < mqttFailureCount; i++) {
            MqttClient client = mqttClients.get(i);
            if (client.isConnected()) {
                client.disconnect();
            }
        }
        
        System.out.println("Simulated " + mqttFailureCount + " MQTT connection failures");
        Thread.sleep(3000);
        
        // 验证故障后状态
        int activeMqttConnections = 0;
        for (MqttClient client : mqttClients) {
            if (client.isConnected()) {
                activeMqttConnections++;
            }
        }
        
        assertEquals(mqttConnectionCount - mqttFailureCount, activeMqttConnections, 
            "Active MQTT connections should match expected count");
        
        // 测试自动重连（模拟）
        CountDownLatch mqttRecoveryLatch = new CountDownLatch(mqttFailureCount);
        AtomicInteger recoveredMqttConnections = new AtomicInteger(0);
        
        for (int i = 0; i < mqttFailureCount; i++) {
            final int clientId = mqttConnectionCount + i;
            
            executorService.submit(() -> {
                try {
                    MqttClient newClient = createMqttConnection("mqttRecovered" + clientId);
                    mqttClients.add(newClient);
                    recoveredMqttConnections.incrementAndGet();
                } catch (Exception e) {
                    System.err.println("Failed to recover MQTT connection " + clientId + ": " + e.getMessage());
                } finally {
                    mqttRecoveryLatch.countDown();
                }
            });
        }
        
        assertTrue(mqttRecoveryLatch.await(30, TimeUnit.SECONDS), "MQTT recovery should complete");
        System.out.println("Recovered MQTT connections: " + recoveredMqttConnections.get());
        
        // 验证恢复后的 MQTT 功能
        String testPayload = objectMapper.writeValueAsString(Map.of(
            "type", "mqtt_recovery_test",
            "timestamp", System.currentTimeMillis(),
            "message", "Testing recovered MQTT connections"
        ));
        
        int workingMqttConnections = 0;
        for (MqttClient client : mqttClients) {
            if (client.isConnected()) {
                try {
                    MqttMessage message = new MqttMessage(testPayload.getBytes(StandardCharsets.UTF_8));
                    message.setQos(1);
                    client.publish("test/recovery/mqtt", message);
                    workingMqttConnections++;
                } catch (Exception e) {
                    System.err.println("Failed to publish test message: " + e.getMessage());
                }
            }
        }
        
        assertTrue(workingMqttConnections >= mqttConnectionCount * 0.8, 
            "At least 80% of MQTT connections should be working after recovery");
        
        System.out.println("MQTT connection failure recovery test completed successfully");
    }
    
    @Test
    @Order(3)
    void testClusterInstanceFailover() throws Exception {
        System.out.println("Testing cluster instance failover");
        
        // 验证初始集群状态
        Set<String> initialInstances = clusterStateManager.getActiveInstances();
        assertTrue(initialInstances.size() >= 2, "Should have at least 2 instances for failover test");
        System.out.println("Initial cluster instances: " + initialInstances);
        
        // 选择一个实例进行故障模拟
        String primaryInstance = "test-instance-primary";
        String backupInstance = "test-instance-backup";
        
        assertTrue(initialInstances.contains(primaryInstance), "Primary instance should be active");
        assertTrue(initialInstances.contains(backupInstance), "Backup instance should be active");
        
        // 建立连接到主实例（模拟）
        int connectionCount = 10;
        setupWebSocketConnections(connectionCount);
        
        // 记录故障前的负载分布
        Map<String, Integer> preFailureDistribution = new HashMap<>();
        for (int i = 0; i < 50; i++) {
            String selectedInstance = loadBalancer.selectInstance("pre-failure-test-" + i);
            preFailureDistribution.merge(selectedInstance, 1, Integer::sum);
        }
        System.out.println("Pre-failure load distribution: " + preFailureDistribution);
        
        // 模拟主实例故障
        System.out.println("Simulating failure of primary instance: " + primaryInstance);
        loadBalancer.updateInstanceHealth(primaryInstance, false);
        
        // 等待故障检测和切换
        Thread.sleep(5000);
        
        // 验证故障转移
        Map<String, Integer> postFailureDistribution = new HashMap<>();
        for (int i = 0; i < 50; i++) {
            String selectedInstance = loadBalancer.selectInstance("post-failure-test-" + i);
            postFailureDistribution.merge(selectedInstance, 1, Integer::sum);
        }
        System.out.println("Post-failure load distribution: " + postFailureDistribution);
        
        // 验证故障实例不再被选择
        assertFalse(postFailureDistribution.containsKey(primaryInstance), 
            "Failed instance should not receive traffic");
        assertTrue(postFailureDistribution.containsKey(backupInstance), 
            "Backup instance should receive traffic");
        
        // 测试故障期间的消息处理
        CountDownLatch failoverMessageLatch = new CountDownLatch(connectionCount);
        AtomicInteger successfulMessages = new AtomicInteger(0);
        
        for (int i = 0; i < connectionCount; i++) {
            final int messageIndex = i;
            
            executorService.submit(() -> {
                try {
                    if (messageIndex < webSocketSessions.size()) {
                        WebSocketSession session = webSocketSessions.get(messageIndex);
                        if (session.isOpen()) {
                            String message = objectMapper.writeValueAsString(Map.of(
                                "type", "failover_test",
                                "messageId", messageIndex,
                                "timestamp", System.currentTimeMillis(),
                                "instanceFailure", primaryInstance
                            ));
                            
                            session.sendMessage(new TextMessage(message));
                            successfulMessages.incrementAndGet();
                        }
                    }
                } catch (Exception e) {
                    System.err.println("Failover message error: " + e.getMessage());
                } finally {
                    failoverMessageLatch.countDown();
                }
            });
        }
        
        assertTrue(failoverMessageLatch.await(30, TimeUnit.SECONDS), "Failover messages should be processed");
        System.out.println("Messages processed during failover: " + successfulMessages.get());
        
        // 模拟主实例恢复
        System.out.println("Simulating recovery of primary instance: " + primaryInstance);
        loadBalancer.updateInstanceHealth(primaryInstance, true);
        
        Thread.sleep(3000);
        
        // 验证实例恢复后重新参与负载均衡
        Map<String, Integer> postRecoveryDistribution = new HashMap<>();
        for (int i = 0; i < 100; i++) {
            String selectedInstance = loadBalancer.selectInstance("post-recovery-test-" + i);
            postRecoveryDistribution.merge(selectedInstance, 1, Integer::sum);
        }
        System.out.println("Post-recovery load distribution: " + postRecoveryDistribution);
        
        assertTrue(postRecoveryDistribution.containsKey(primaryInstance), 
            "Recovered instance should participate in load balancing");
        assertTrue(postRecoveryDistribution.containsKey(backupInstance), 
            "Backup instance should continue to participate");
        
        System.out.println("Cluster instance failover test completed successfully");
    }
    
    @Test
    @Order(4)
    void testDataConsistencyDuringFailures() throws Exception {
        System.out.println("Testing data consistency during failures");
        
        // 建立多个连接用于数据一致性测试
        int connectionCount = 8;
        setupWebSocketConnections(connectionCount);
        
        // 初始化数据状态
        String dataKey = "test-data-consistency";
        Map<String, Object> initialData = Map.of(
            "counter", 0,
            "lastUpdate", System.currentTimeMillis(),
            "version", 1
        );
        
        expectedDataState.put(dataKey, initialData);
        redisTemplate.opsForValue().set(dataKey, initialData);
        
        // 并发更新数据
        int updateCount = 20;
        CountDownLatch updateLatch = new CountDownLatch(updateCount);
        AtomicInteger successfulUpdates = new AtomicInteger(0);
        List<Integer> updateSequence = Collections.synchronizedList(new ArrayList<>());
        
        for (int i = 0; i < updateCount; i++) {
            final int updateIndex = i;
            
            executorService.submit(() -> {
                try {
                    // 模拟数据更新操作
                    Map<String, Object> updateData = Map.of(
                        "type", "data_update",
                        "key", dataKey,
                        "updateId", updateIndex,
                        "newValue", Map.of(
                            "counter", updateIndex,
                            "lastUpdate", System.currentTimeMillis(),
                            "version", updateIndex + 1
                        ),
                        "timestamp", System.currentTimeMillis()
                    );
                    
                    // 随机选择连接发送更新
                    WebSocketSession session = webSocketSessions.get(updateIndex % webSocketSessions.size());
                    if (session.isOpen()) {
                        session.sendMessage(new TextMessage(objectMapper.writeValueAsString(updateData)));
                        updateSequence.add(updateIndex);
                        successfulUpdates.incrementAndGet();
                        
                        // 模拟在更新过程中发生故障
                        if (updateIndex == updateCount / 2) {
                            System.out.println("Simulating failure during data update " + updateIndex);
                            // 模拟网络延迟或临时故障
                            Thread.sleep(1000);
                        }
                    }
                    
                } catch (Exception e) {
                    System.err.println("Data update error: " + e.getMessage());
                } finally {
                    updateLatch.countDown();
                }
            });
            
            // 添加小延迟确保更新顺序
            Thread.sleep(50);
        }
        
        assertTrue(updateLatch.await(60, TimeUnit.SECONDS), "All data updates should complete");
        System.out.println("Successful updates: " + successfulUpdates.get());
        System.out.println("Update sequence: " + updateSequence);
        
        // 验证数据一致性
        Thread.sleep(2000); // 等待所有更新处理完成
        
        // 检查 Redis 中的最终数据状态
        Object finalData = redisTemplate.opsForValue().get(dataKey);
        assertNotNull(finalData, "Final data should exist in Redis");
        
        System.out.println("Final data state: " + finalData);
        
        // 验证数据版本一致性
        if (finalData instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = (Map<String, Object>) finalData;
            
            Object version = dataMap.get("version");
            assertNotNull(version, "Data should have version information");
            
            // 版本应该反映最后一次成功更新
            assertTrue(version instanceof Number, "Version should be a number");
            int finalVersion = ((Number) version).intValue();
            assertTrue(finalVersion > 1, "Final version should be greater than initial version");
        }
        
        System.out.println("Data consistency during failures test completed successfully");
    }
    
    @Test
    @Order(5)
    void testMessageOrderingAndDeduplication() throws Exception {
        System.out.println("Testing message ordering and deduplication");
        
        // 建立连接
        setupWebSocketConnections(5);
        
        // 发送有序消息序列
        int messageCount = 30;
        CountDownLatch orderingLatch = new CountDownLatch(messageCount);
        List<Integer> sentOrder = Collections.synchronizedList(new ArrayList<>());
        List<Integer> receivedOrder = Collections.synchronizedList(new ArrayList<>());
        
        // 发送消息
        for (int i = 0; i < messageCount; i++) {
            final int sequenceNumber = i;
            
            executorService.submit(() -> {
                try {
                    String messageId = "msg-" + sequenceNumber + "-" + System.currentTimeMillis();
                    
                    Map<String, Object> orderedMessage = Map.of(
                        "type", "ordered_message",
                        "messageId", messageId,
                        "sequenceNumber", sequenceNumber,
                        "timestamp", System.currentTimeMillis(),
                        "data", "Ordered message content " + sequenceNumber
                    );
                    
                    // 记录发送顺序
                    sentOrder.add(sequenceNumber);
                    processedMessageIds.add(messageId);
                    
                    // 随机选择连接发送
                    WebSocketSession session = webSocketSessions.get(sequenceNumber % webSocketSessions.size());
                    if (session.isOpen()) {
                        session.sendMessage(new TextMessage(objectMapper.writeValueAsString(orderedMessage)));
                    }
                    
                    // 模拟网络延迟
                    Thread.sleep(10 + (sequenceNumber % 50));
                    
                } catch (Exception e) {
                    System.err.println("Ordering test error: " + e.getMessage());
                } finally {
                    orderingLatch.countDown();
                }
            });
        }
        
        assertTrue(orderingLatch.await(60, TimeUnit.SECONDS), "All ordered messages should be sent");
        
        // 测试重复消息检测
        System.out.println("Testing message deduplication");
        
        // 发送一些重复消息
        int duplicateCount = 5;
        CountDownLatch deduplicationLatch = new CountDownLatch(duplicateCount);
        
        for (int i = 0; i < duplicateCount; i++) {
            final int duplicateIndex = i;
            
            executorService.submit(() -> {
                try {
                    // 重用之前的消息ID
                    String duplicateMessageId = "msg-" + duplicateIndex + "-" + (System.currentTimeMillis() - 10000);
                    
                    Map<String, Object> duplicateMessage = Map.of(
                        "type", "duplicate_message",
                        "messageId", duplicateMessageId,
                        "sequenceNumber", duplicateIndex,
                        "timestamp", System.currentTimeMillis(),
                        "isDuplicate", true
                    );
                    
                    WebSocketSession session = webSocketSessions.get(duplicateIndex % webSocketSessions.size());
                    if (session.isOpen()) {
                        session.sendMessage(new TextMessage(objectMapper.writeValueAsString(duplicateMessage)));
                    }
                    
                } catch (Exception e) {
                    System.err.println("Deduplication test error: " + e.getMessage());
                } finally {
                    deduplicationLatch.countDown();
                }
            });
        }
        
        assertTrue(deduplicationLatch.await(30, TimeUnit.SECONDS), "Duplicate messages should be sent");
        
        Thread.sleep(3000); // 等待处理完成
        
        System.out.println("Sent message order: " + sentOrder.subList(0, Math.min(10, sentOrder.size())));
        System.out.println("Processed message IDs count: " + processedMessageIds.size());
        
        // 验证消息处理
        assertTrue(sentOrder.size() == messageCount, "All messages should be sent");
        assertTrue(processedMessageIds.size() >= messageCount, "All unique messages should be processed");
        
        System.out.println("Message ordering and deduplication test completed successfully");
    }
    
    @Test
    @Order(6)
    void testSystemRecoveryAfterCompleteFailure() throws Exception {
        System.out.println("Testing system recovery after complete failure");
        
        // 建立初始系统状态
        int initialConnections = 10;
        setupWebSocketConnections(initialConnections);
        
        // 设置一些数据状态
        String recoveryDataKey = "recovery-test-data";
        Map<String, Object> preFailureData = Map.of(
            "activeConnections", initialConnections,
            "lastHeartbeat", System.currentTimeMillis(),
            "systemStatus", "operational"
        );
        
        redisTemplate.opsForValue().set(recoveryDataKey, preFailureData);
        expectedDataState.put(recoveryDataKey, preFailureData);
        
        // 验证系统正常运行
        String healthCheckMessage = objectMapper.writeValueAsString(Map.of(
            "type", "health_check",
            "timestamp", System.currentTimeMillis()
        ));
        
        int workingConnections = 0;
        for (WebSocketSession session : webSocketSessions) {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(healthCheckMessage));
                    workingConnections++;
                } catch (Exception e) {
                    System.err.println("Health check failed: " + e.getMessage());
                }
            }
        }
        
        System.out.println("Pre-failure working connections: " + workingConnections);
        assertTrue(workingConnections >= initialConnections * 0.9, "Most connections should be working");
        
        // 模拟完全系统故障
        System.out.println("Simulating complete system failure");
        
        // 关闭所有连接
        for (WebSocketSession session : webSocketSessions) {
            if (session.isOpen()) {
                session.close(CloseStatus.GOING_AWAY);
            }
        }
        
        // 标记所有实例为不健康
        Set<String> instances = clusterStateManager.getActiveInstances();
        for (String instance : instances) {
            loadBalancer.updateInstanceHealth(instance, false);
        }
        
        Thread.sleep(5000); // 模拟故障持续时间
        
        // 开始系统恢复
        System.out.println("Starting system recovery");
        
        // 恢复实例健康状态
        for (String instance : instances) {
            loadBalancer.updateInstanceHealth(instance, true);
        }
        
        // 重新建立连接
        webSocketSessions.clear();
        CountDownLatch recoveryLatch = new CountDownLatch(initialConnections);
        AtomicInteger recoveredConnections = new AtomicInteger(0);
        
        for (int i = 0; i < initialConnections; i++) {
            final int clientId = i;
            
            executorService.submit(() -> {
                try {
                    WebSocketSession newSession = createWebSocketConnection("recoveredUser" + clientId);
                    webSocketSessions.add(newSession);
                    recoveredConnections.incrementAndGet();
                } catch (Exception e) {
                    System.err.println("Recovery connection error: " + e.getMessage());
                } finally {
                    recoveryLatch.countDown();
                }
            });
        }
        
        assertTrue(recoveryLatch.await(60, TimeUnit.SECONDS), "System recovery should complete");
        System.out.println("Recovered connections: " + recoveredConnections.get());
        
        // 验证数据状态恢复
        Object recoveredData = redisTemplate.opsForValue().get(recoveryDataKey);
        assertNotNull(recoveredData, "Data should be preserved after recovery");
        
        // 验证系统功能恢复
        String postRecoveryMessage = objectMapper.writeValueAsString(Map.of(
            "type", "post_recovery_test",
            "timestamp", System.currentTimeMillis(),
            "recoveryTest", true
        ));
        
        int postRecoveryWorkingConnections = 0;
        for (WebSocketSession session : webSocketSessions) {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(postRecoveryMessage));
                    postRecoveryWorkingConnections++;
                } catch (Exception e) {
                    System.err.println("Post-recovery test failed: " + e.getMessage());
                }
            }
        }
        
        System.out.println("Post-recovery working connections: " + postRecoveryWorkingConnections);
        assertTrue(postRecoveryWorkingConnections >= initialConnections * 0.8, 
            "At least 80% of connections should work after recovery");
        
        // 更新恢复后的数据状态
        Map<String, Object> postRecoveryData = Map.of(
            "activeConnections", postRecoveryWorkingConnections,
            "lastHeartbeat", System.currentTimeMillis(),
            "systemStatus", "recovered",
            "recoveryTime", System.currentTimeMillis()
        );
        
        redisTemplate.opsForValue().set(recoveryDataKey, postRecoveryData);
        
        System.out.println("System recovery after complete failure test completed successfully");
    }
    
    private WebSocketSession createWebSocketConnection(String userId) throws Exception {
        WebSocketClient client = new StandardWebSocketClient();
        URI uri = URI.create("ws://localhost:" + port + "/ws?userId=" + userId);
        
        CountDownLatch connectionLatch = new CountDownLatch(1);
        AtomicReference<WebSocketSession> sessionRef = new AtomicReference<>();
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        
        WebSocketHandler handler = new WebSocketHandler() {
            @Override
            public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                sessionRef.set(session);
                connectionLatch.countDown();
            }
            
            @Override
            public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                // 处理接收到的消息用于测试验证
            }
            
            @Override
            public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                errorRef.set(new Exception("Transport error", exception));
                connectionLatch.countDown();
            }
            
            @Override
            public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {}
            
            @Override
            public boolean supportsPartialMessages() {
                return false;
            }
        };
        
        client.doHandshake(handler, null, uri);
        
        if (!connectionLatch.await(15, TimeUnit.SECONDS)) {
            throw new Exception("Connection timeout for user: " + userId);
        }
        
        if (errorRef.get() != null) {
            throw errorRef.get();
        }
        
        WebSocketSession session = sessionRef.get();
        if (session == null) {
            throw new Exception("Failed to establish connection for user: " + userId);
        }
        
        return session;
    }
    
    private MqttClient createMqttConnection(String clientId) throws Exception {
        String fullClientId = clientId + "-" + System.currentTimeMillis();
        MqttClient client = new MqttClient("tcp://localhost:1883", fullClientId);
        
        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(true);
        options.setConnectionTimeout(10);
        options.setKeepAliveInterval(30);
        options.setAutomaticReconnect(true);
        
        client.setCallback(new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                System.out.println("MQTT connection lost for " + fullClientId + ": " + cause.getMessage());
            }
            
            @Override
            public void messageArrived(String topic, MqttMessage message) throws Exception {
                // 处理接收到的消息
            }
            
            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {}
        });
        
        client.connect(options);
        client.subscribe("test/recovery/+", 1);
        
        return client;
    }
    
    private void setupWebSocketConnections(int count) throws Exception {
        CountDownLatch setupLatch = new CountDownLatch(count);
        
        for (int i = 0; i < count; i++) {
            final int clientId = i;
            
            executorService.submit(() -> {
                try {
                    WebSocketSession session = createWebSocketConnection("testUser" + clientId);
                    webSocketSessions.add(session);
                } catch (Exception e) {
                    System.err.println("Failed to setup connection " + clientId + ": " + e.getMessage());
                } finally {
                    setupLatch.countDown();
                }
            });
        }
        
        assertTrue(setupLatch.await(60, TimeUnit.SECONDS), "All connections should be established");
    }
}