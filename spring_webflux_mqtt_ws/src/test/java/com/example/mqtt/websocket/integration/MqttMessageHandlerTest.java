package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.service.EncryptionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.MessageChannel;

import java.time.Instant;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * MQTT 消息处理器测试类
 */
@ExtendWith(MockitoExtension.class)
class MqttMessageHandlerTest {

    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private EncryptionService encryptionService;
    
    @Mock
    private MessageChannel webSocketChannel;
    
    @Mock
    private MessageChannel httpApiChannel;
    
    @Mock
    private MessageChannel deadLetterChannel;

    private MqttMessageHandler mqttMessageHandler;

    private byte[] testMessage;
    private String testTopic;
    private BaseMessage testBaseMessage;

    @BeforeEach
    void setUp() {
        mqttMessageHandler = new MqttMessageHandler(
            objectMapper, encryptionService, webSocketChannel, httpApiChannel, deadLetterChannel);
        
        testMessage = "Test MQTT message".getBytes();
        testTopic = "test/topic";
        
        testBaseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("text")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom(testMessage))
                .setEncrypted(false)
                .build();
    }

    @Test
    void shouldHandleValidMqttMessage() {
        // When & Then
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, testTopic, 1, false, false)
        );
    }

    @Test
    void shouldHandleRetainedMessage() {
        // When & Then
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, testTopic, 1, true, false)
        );
    }

    @Test
    void shouldSkipDuplicateMessage() {
        // Given
        String duplicateTopic = "normal/topic";
        
        // When & Then - 第一次处理应该成功
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, duplicateTopic, 1, false, false)
        );
        
        // 第二次处理重复消息应该被跳过（但不抛异常）
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, duplicateTopic, 1, false, true)
        );
    }

    @Test
    void shouldNotSkipDuplicateForCriticalTopic() {
        // Given
        String criticalTopic = "critical/alert";
        
        // When & Then - 即使是重复消息，关键主题也应该处理
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, criticalTopic, 1, false, true)
        );
    }

    @Test
    void shouldHandleEmptyMessage() {
        // Given
        byte[] emptyMessage = new byte[0];

        // When & Then
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(emptyMessage, testTopic, 0, false, false)
        );
    }

    @Test
    void shouldHandleNullMessage() {
        // When & Then
        assertThatThrownBy(() -> 
            mqttMessageHandler.handleMqttMessage(null, testTopic, 1, false, false)
        ).isInstanceOf(Exception.class);
    }

    @Test
    void shouldHandleDifferentQosLevels() {
        // Test QoS 0
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, testTopic, 0, false, false)
        );

        // Test QoS 1
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, testTopic, 1, false, false)
        );

        // Test QoS 2
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, testTopic, 2, false, false)
        );
    }

    @Test
    void shouldRouteWebSocketMessage() {
        // Given
        String webSocketTopic = "ws/realtime/data";
        when(webSocketChannel.send(any())).thenReturn(true);
        
        // When
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, webSocketTopic, 1, false, false)
        );
        
        // Then
        verify(webSocketChannel, times(1)).send(any());
    }

    @Test
    void shouldRouteApiMessage() {
        // Given
        String apiTopic = "api/users/update";
        when(httpApiChannel.send(any())).thenReturn(true);
        
        // When
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, apiTopic, 1, false, false)
        );
        
        // Then
        verify(httpApiChannel, times(1)).send(any());
    }

    @Test
    void shouldHandleEncryptedMessage() throws Exception {
        // Given
        BaseMessage encryptedMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("encrypted")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom("encrypted_data".getBytes()))
                .setEncrypted(true)
                .putHeaders("keyId", "test-key")
                .build();
        
        byte[] encryptedData = encryptedMessage.toByteArray();
        byte[] decryptedData = "decrypted_data".getBytes();
        
        when(encryptionService.decrypt(any(byte[].class), anyString())).thenReturn(decryptedData);
        
        // When & Then
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(encryptedData, testTopic, 1, false, false)
        );
        
        verify(encryptionService, times(1)).decrypt(any(byte[].class), eq("test-key"));
    }

    @Test
    void shouldHandleDecryptionFailure() throws Exception {
        // Given
        BaseMessage encryptedMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("encrypted")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom("encrypted_data".getBytes()))
                .setEncrypted(true)
                .putHeaders("keyId", "invalid-key")
                .build();
        
        byte[] encryptedData = encryptedMessage.toByteArray();
        
        when(encryptionService.decrypt(any(byte[].class), anyString()))
                .thenThrow(new RuntimeException("Decryption failed"));
        when(deadLetterChannel.send(any())).thenReturn(true);
        
        // When
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(encryptedData, testTopic, 1, false, false)
        );
        
        // Then
        verify(deadLetterChannel, times(1)).send(any());
    }

    @Test
    void shouldHandleJsonMessage() throws Exception {
        // Given
        String jsonMessage = "{\"id\":\"test-123\",\"type\":\"json\",\"data\":\"test data\"}";
        byte[] jsonData = jsonMessage.getBytes();
        
        // When & Then
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(jsonData, testTopic, 1, false, false)
        );
    }

    @Test
    void shouldHandleProtobufMessage() {
        // Given
        byte[] protobufData = testBaseMessage.toByteArray();
        
        // When & Then
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(protobufData, testTopic, 1, false, false)
        );
    }

    @Test
    void shouldHandleProcessingError() {
        // Given
        String invalidTopic = null; // 这会导致处理错误
        when(deadLetterChannel.send(any())).thenReturn(true);
        
        // When
        assertDoesNotThrow(() -> 
            mqttMessageHandler.handleMqttMessage(testMessage, invalidTopic, 1, false, false)
        );
        
        // Then
        verify(deadLetterChannel, times(1)).send(any());
    }

    @Test
    void shouldRouteByTopicPattern() {
        // Test WebSocket topic routing
        String result1 = mqttMessageHandler.routeByTopic("ws/test");
        assert "webSocketChannel".equals(result1);
        
        // Test API topic routing  
        String result2 = mqttMessageHandler.routeByTopic("api/test");
        assert "httpApiChannel".equals(result2);
        
        // Test system topic routing
        String result3 = mqttMessageHandler.routeByTopic("system/test");
        assert "systemChannel".equals(result3);
        
        // Test default routing
        String result4 = mqttMessageHandler.routeByTopic("other/test");
        assert "defaultProcessingChannel".equals(result4);
    }
}