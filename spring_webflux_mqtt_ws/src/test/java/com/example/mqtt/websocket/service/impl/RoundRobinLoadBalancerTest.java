package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * 轮询负载均衡器测试
 */
@ExtendWith(MockitoExtension.class)
class RoundRobinLoadBalancerTest {

    @Mock
    private ClusterStateManager clusterStateManager;

    private RoundRobinLoadBalancer loadBalancer;

    @BeforeEach
    void setUp() {
        loadBalancer = new RoundRobinLoadBalancer(clusterStateManager);
    }

    @Test
    void shouldSelectInstanceInRoundRobinOrder() {
        // Given
        Set<String> activeInstances = Set.of("instance1", "instance2", "instance3");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        // When & Then - 测试轮询顺序
        StepVerifier.create(loadBalancer.selectInstance())
            .expectNextMatches(instance -> activeInstances.contains(instance))
            .verifyComplete();

        StepVerifier.create(loadBalancer.selectInstance())
            .expectNextMatches(instance -> activeInstances.contains(instance))
            .verifyComplete();

        StepVerifier.create(loadBalancer.selectInstance())
            .expectNextMatches(instance -> activeInstances.contains(instance))
            .verifyComplete();
    }

    @Test
    void shouldReturnEmptyWhenNoHealthyInstances() {
        // Given
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(Set.of()));

        // When & Then
        StepVerifier.create(loadBalancer.selectInstance())
            .verifyComplete();
    }

    @Test
    void shouldUpdateInstanceHealth() {
        // Given
        String instanceId = "instance1";
        boolean healthy = false;

        // When & Then
        StepVerifier.create(loadBalancer.updateInstanceHealth(instanceId, healthy))
            .verifyComplete();

        // 验证健康状态已更新
        Set<String> activeInstances = Set.of("instance1", "instance2");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        StepVerifier.create(loadBalancer.getHealthyInstances())
            .expectNext(List.of("instance2"))
            .verifyComplete();
    }

    @Test
    void shouldGetAndSetInstanceWeight() {
        // Given
        String instanceId = "instance1";
        int weight = 5;

        // When & Then - 设置权重
        StepVerifier.create(loadBalancer.setInstanceWeight(instanceId, weight))
            .verifyComplete();

        // 获取权重
        StepVerifier.create(loadBalancer.getInstanceWeight(instanceId))
            .expectNext(weight)
            .verifyComplete();
    }

    @Test
    void shouldRejectInvalidWeight() {
        // Given
        String instanceId = "instance1";
        int invalidWeight = 0;

        // When & Then
        StepVerifier.create(loadBalancer.setInstanceWeight(instanceId, invalidWeight))
            .expectError(IllegalArgumentException.class)
            .verify();
    }

    @Test
    void shouldProvideLoadBalancerStats() {
        // Given
        Set<String> activeInstances = Set.of("instance1", "instance2");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        // 执行一些请求以生成统计信息
        StepVerifier.create(loadBalancer.selectInstance())
            .expectNextMatches(instance -> activeInstances.contains(instance))
            .verifyComplete();

        // When & Then
        StepVerifier.create(loadBalancer.getStats())
            .assertNext(stats -> {
                assertThat(stats.totalRequests()).isEqualTo(1);
                assertThat(stats.currentStrategy()).isEqualTo(LoadBalancer.Strategy.ROUND_ROBIN);
                assertThat(stats.requestCounts()).isNotEmpty();
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleClusterStateManagerError() {
        // Given
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.error(new RuntimeException("Cluster error")));

        // When & Then
        StepVerifier.create(loadBalancer.selectInstance())
            .expectError(RuntimeException.class)
            .verify();
    }

    @Test
    void shouldResetStats() {
        // Given
        Set<String> activeInstances = Set.of("instance1");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        // 执行请求生成统计信息
        StepVerifier.create(loadBalancer.selectInstance())
            .expectNext("instance1")
            .verifyComplete();

        // When
        loadBalancer.resetStats();

        // Then
        StepVerifier.create(loadBalancer.getStats())
            .assertNext(stats -> {
                assertThat(stats.totalRequests()).isEqualTo(0);
                assertThat(stats.requestCounts()).isEmpty();
            })
            .verifyComplete();
    }

    @Test
    void shouldSelectInstanceWithKey() {
        // Given
        Set<String> activeInstances = Set.of("instance1", "instance2");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        // When & Then - 使用key选择实例应该和不使用key的结果一致（轮询策略）
        StepVerifier.create(loadBalancer.selectInstance("test-key"))
            .expectNextMatches(instance -> activeInstances.contains(instance))
            .verifyComplete();
    }
}