package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.exception.MqttConnectionException;
import com.example.mqtt.websocket.exception.TemporaryEncryptionException;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;

import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RetryConfiguration 单元测试
 */
@ExtendWith(MockitoExtension.class)
class RetryConfigurationTest {
    
    private final RetryConfiguration retryConfiguration = new RetryConfiguration();
    
    @Test
    void shouldCreateMqttRetryTemplate() {
        // When
        RetryTemplate retryTemplate = retryConfiguration.mqttRetryTemplate();
        
        // Then
        assertNotNull(retryTemplate);
    }
    
    @Test
    void shouldCreateEncryptionRetryTemplate() {
        // When
        RetryTemplate retryTemplate = retryConfiguration.encryptionRetryTemplate();
        
        // Then
        assertNotNull(retryTemplate);
    }
    
    @Test
    void shouldCreateGeneralRetryTemplate() {
        // When
        RetryTemplate retryTemplate = retryConfiguration.generalRetryTemplate();
        
        // Then
        assertNotNull(retryTemplate);
    }
    
    @Test
    void shouldRetryMqttExceptions() {
        // Given
        RetryTemplate retryTemplate = retryConfiguration.mqttRetryTemplate();
        AtomicInteger attempts = new AtomicInteger(0);
        
        // When & Then
        assertThrows(MqttException.class, () -> {
            retryTemplate.execute((RetryCallback<Void, MqttException>) context -> {
                attempts.incrementAndGet();
                throw new MqttException(MqttException.REASON_CODE_CONNECTION_LOST);
            });
        });
        
        // Should retry 3 times (initial + 2 retries)
        assertEquals(3, attempts.get());
    }
    
    @Test
    void shouldRetryMqttConnectionExceptions() {
        // Given
        RetryTemplate retryTemplate = retryConfiguration.mqttRetryTemplate();
        AtomicInteger attempts = new AtomicInteger(0);
        
        // When & Then
        assertThrows(MqttConnectionException.class, () -> {
            retryTemplate.execute((RetryCallback<Void, MqttConnectionException>) context -> {
                attempts.incrementAndGet();
                throw new MqttConnectionException("tcp://localhost:1883", "test-client", "Connection failed");
            });
        });
        
        // Should retry 3 times (initial + 2 retries)
        assertEquals(3, attempts.get());
    }
    
    @Test
    void shouldRetryTemporaryEncryptionExceptions() {
        // Given
        RetryTemplate retryTemplate = retryConfiguration.encryptionRetryTemplate();
        AtomicInteger attempts = new AtomicInteger(0);
        
        // When & Then
        assertThrows(TemporaryEncryptionException.class, () -> {
            retryTemplate.execute((RetryCallback<Void, TemporaryEncryptionException>) context -> {
                attempts.incrementAndGet();
                throw new TemporaryEncryptionException("Temporary encryption failure");
            });
        });
        
        // Should retry 2 times (initial + 1 retry)
        assertEquals(2, attempts.get());
    }
    
    @Test
    void shouldNotRetryNonRetryableExceptions() {
        // Given
        RetryTemplate retryTemplate = retryConfiguration.mqttRetryTemplate();
        AtomicInteger attempts = new AtomicInteger(0);
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            retryTemplate.execute((RetryCallback<Void, RuntimeException>) context -> {
                attempts.incrementAndGet();
                throw new RuntimeException("Non-retryable exception");
            });
        });
        
        // Should not retry
        assertEquals(1, attempts.get());
    }
    
    @Test
    void shouldSucceedAfterRetry() {
        // Given
        RetryTemplate retryTemplate = retryConfiguration.mqttRetryTemplate();
        AtomicInteger attempts = new AtomicInteger(0);
        
        // When
        String result = retryTemplate.execute((RetryCallback<String, MqttException>) context -> {
            int attempt = attempts.incrementAndGet();
            if (attempt < 3) {
                throw new MqttException(MqttException.REASON_CODE_CONNECTION_LOST);
            }
            return "Success";
        });
        
        // Then
        assertEquals("Success", result);
        assertEquals(3, attempts.get());
    }
    
    @Test
    void shouldRespectMaxAttempts() {
        // Given
        RetryTemplate retryTemplate = retryConfiguration.encryptionRetryTemplate();
        AtomicInteger attempts = new AtomicInteger(0);
        
        // When & Then
        assertThrows(TemporaryEncryptionException.class, () -> {
            retryTemplate.execute((RetryCallback<Void, TemporaryEncryptionException>) context -> {
                attempts.incrementAndGet();
                throw new TemporaryEncryptionException("Always fails");
            });
        });
        
        // Should respect max attempts (2)
        assertEquals(2, attempts.get());
    }
    
    @Test
    void shouldCreateRetryPropertiesWithDefaults() {
        // When
        RetryConfiguration.RetryProperties properties = new RetryConfiguration.RetryProperties();
        
        // Then
        assertNotNull(properties.getMqtt());
        assertNotNull(properties.getEncryption());
        
        // Check MQTT defaults
        assertEquals(3, properties.getMqtt().getMaxAttempts());
        assertEquals(1000, properties.getMqtt().getInitialInterval());
        assertEquals(2.0, properties.getMqtt().getMultiplier());
        assertEquals(10000, properties.getMqtt().getMaxInterval());
        
        // Check Encryption defaults
        assertEquals(2, properties.getEncryption().getMaxAttempts());
        assertEquals(500, properties.getEncryption().getBackOffPeriod());
    }
    
    @Test
    void shouldAllowRetryPropertiesCustomization() {
        // Given
        RetryConfiguration.RetryProperties properties = new RetryConfiguration.RetryProperties();
        
        // When
        properties.getMqtt().setMaxAttempts(5);
        properties.getMqtt().setInitialInterval(2000);
        properties.getEncryption().setMaxAttempts(3);
        properties.getEncryption().setBackOffPeriod(1000);
        
        // Then
        assertEquals(5, properties.getMqtt().getMaxAttempts());
        assertEquals(2000, properties.getMqtt().getInitialInterval());
        assertEquals(3, properties.getEncryption().getMaxAttempts());
        assertEquals(1000, properties.getEncryption().getBackOffPeriod());
    }
}