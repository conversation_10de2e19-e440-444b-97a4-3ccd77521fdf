package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * WebSocketMessageService 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class WebSocketMessageServiceTest {
    
    @Mock
    private WebSocketSessionManager sessionManager;
    
    private WebSocketMessageService messageService;
    
    @BeforeEach
    void setUp() {
        messageService = new WebSocketMessageService(sessionManager);
    }
    
    @Test
    void shouldBroadcastMessage() {
        // Given
        String messageType = "test-message";
        String payload = "test payload";
        String source = "test-source";
        
        when(sessionManager.broadcastMessage(any(WebSocketMessage.class)))
                .thenReturn(Mono.empty());
        
        // When & Then
        StepVerifier.create(messageService.broadcastMessage(messageType, payload, source))
                .verifyComplete();
        
        verify(sessionManager).broadcastMessage(any(WebSocketMessage.class));
    }
    
    @Test
    void shouldSendToUser() {
        // Given
        String targetUserId = "user-123";
        String messageType = "private-message";
        String payload = "private payload";
        String source = "sender";
        
        when(sessionManager.sendToUser(eq(targetUserId), any(WebSocketMessage.class)))
                .thenReturn(Mono.empty());
        
        // When & Then
        StepVerifier.create(messageService.sendToUser(targetUserId, messageType, payload, source))
                .verifyComplete();
        
        verify(sessionManager).sendToUser(eq(targetUserId), any(WebSocketMessage.class));
    }
    
    @Test
    void shouldSendSystemMessage() {
        // Given
        String sessionId = "session-123";
        String messageType = "system-info";
        String payload = "system payload";
        
        when(sessionManager.sendToSession(eq(sessionId), any(WebSocketMessage.class)))
                .thenReturn(Mono.empty());
        
        // When & Then
        StepVerifier.create(messageService.sendSystemMessage(sessionId, messageType, payload))
                .verifyComplete();
        
        verify(sessionManager).sendToSession(eq(sessionId), any(WebSocketMessage.class));
    }
    
    @Test
    void shouldSendNotification() {
        // Given
        String title = "Test Notification";
        String content = "This is a test notification";
        String level = "info";
        
        when(sessionManager.broadcastMessage(any(WebSocketMessage.class)))
                .thenReturn(Mono.empty());
        
        // When & Then
        StepVerifier.create(messageService.sendNotification(title, content, level))
                .verifyComplete();
        
        verify(sessionManager).broadcastMessage(any(WebSocketMessage.class));
    }
    
    @Test
    void shouldGetSystemStatus() {
        // Given
        int activeConnections = 5;
        when(sessionManager.getActiveSessionCount()).thenReturn(activeConnections);
        
        // When
        WebSocketMessage statusMessage = messageService.getSystemStatus();
        
        // Then
        assertNotNull(statusMessage);
        assertEquals(WebSocketMessage.MessageType.SYSTEM, statusMessage.getType());
        assertEquals("system_status", statusMessage.getMessage().getType());
        assertEquals("system", statusMessage.getMessage().getSource());
        assertEquals(String.valueOf(activeConnections), 
                    statusMessage.getMessage().getHeadersMap().get("activeConnections"));
    }
    
    @Test
    void shouldValidateValidMessage() {
        // Given
        WebSocketMessage validMessage = WebSocketMessage.newBuilder()
                .setType(WebSocketMessage.MessageType.BROADCAST)
                .setMessage(com.example.mqtt.websocket.model.proto.BaseMessage.newBuilder()
                        .setId("msg-1")
                        .setTimestamp(System.currentTimeMillis())
                        .setType("test")
                        .setSource("test-source")
                        .build())
                .build();
        
        // When & Then
        assertTrue(messageService.isValidMessage(validMessage));
    }
    
    @Test
    void shouldValidateInvalidMessage() {
        // Given - message without required fields
        WebSocketMessage invalidMessage = WebSocketMessage.newBuilder()
                .setType(WebSocketMessage.MessageType.BROADCAST)
                .setMessage(com.example.mqtt.websocket.model.proto.BaseMessage.newBuilder()
                        .setId("msg-1")
                        // Missing timestamp, type, and source
                        .build())
                .build();
        
        // When & Then
        assertFalse(messageService.isValidMessage(invalidMessage));
    }
    
    @Test
    void shouldValidateNullMessage() {
        // When & Then
        assertFalse(messageService.isValidMessage(null));
    }
    
    @Test
    void shouldGetActiveConnectionCount() {
        // Given
        int expectedCount = 10;
        when(sessionManager.getActiveSessionCount()).thenReturn(expectedCount);
        
        // When
        int actualCount = messageService.getActiveConnectionCount();
        
        // Then
        assertEquals(expectedCount, actualCount);
        verify(sessionManager).getActiveSessionCount();
    }
}