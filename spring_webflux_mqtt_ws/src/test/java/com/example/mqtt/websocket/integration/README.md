# Integration and End-to-End Tests

This directory contains comprehensive integration and end-to-end tests for the Spring Integration MQTT WebSocket system.

## Test Overview

The integration test suite covers the following areas:

### 1. Complete Message Flow Integration Tests (`CompleteMessageFlowIntegrationTest`)
- **MQTT to WebSocket message flow**: Tests message routing from MQTT broker to WebSocket clients
- **WebSocket to MQTT message flow**: Tests message publishing from WebSocket clients to MQTT broker
- **Encrypted message flow**: Tests end-to-end encryption/decryption during message transmission
- **Bidirectional message exchange**: Tests full duplex communication
- **Message validation and error handling**: Tests system behavior with invalid messages
- **High throughput message processing**: Tests system performance under high message volume

### 2. Multi-Instance Cluster Tests (`MultiInstanceClusterTest`)
- **Cluster instance registration**: Tests service discovery and instance registration
- **Load balancer distribution**: Tests traffic distribution across cluster instances
- **Failover mechanism**: Tests automatic failover when instances become unavailable
- **Cluster message broadcast**: Tests cluster-wide message propagation
- **Concurrent client connections**: Tests handling multiple simultaneous connections
- **Data consistency across instances**: Tests data synchronization in cluster environment
- **Cluster health monitoring**: Tests health check and monitoring capabilities
- **Cluster scalability**: Tests dynamic scaling of cluster instances

### 3. Encrypted Message Transmission Tests (`EncryptedMessageEndToEndTest`)
- **Basic encryption/decryption**: Tests core encryption functionality
- **Encrypted MQTT to WebSocket flow**: Tests encrypted message routing
- **Encrypted WebSocket to MQTT flow**: Tests encrypted message publishing
- **Key rotation during transmission**: Tests key management and rotation
- **Multiple encryption algorithms**: Tests support for different encryption methods
- **Encryption performance under load**: Tests encryption performance at scale
- **Encryption error handling**: Tests error scenarios in encryption/decryption
- **End-to-end encrypted communication**: Tests complete encrypted communication flows

### 4. Load Testing and Concurrency Tests (`LoadTestingAndConcurrencyTest`)
- **High concurrent WebSocket connections**: Tests system capacity for simultaneous connections
- **High throughput message processing**: Tests message processing performance
- **Concurrent MQTT and WebSocket traffic**: Tests mixed protocol traffic handling
- **Memory usage under load**: Tests memory efficiency under high load
- **Response time under load**: Tests response time performance
- **Connection stability under load**: Tests connection reliability over time

### 5. Failure Recovery and Data Consistency Tests (`FailureRecoveryAndDataConsistencyTest`)
- **WebSocket connection failure recovery**: Tests connection recovery mechanisms
- **MQTT connection failure recovery**: Tests MQTT reconnection capabilities
- **Cluster instance failover**: Tests cluster-level failover scenarios
- **Data consistency during failures**: Tests data integrity during failures
- **Message ordering and deduplication**: Tests message ordering guarantees
- **System recovery after complete failure**: Tests disaster recovery capabilities

## Prerequisites

Before running the integration tests, ensure the following services are available:

1. **MQTT Broker**: Mosquitto or similar MQTT broker running on `localhost:1883`
2. **Redis Server**: Redis server running on `localhost:6370` (test configuration)
3. **Java 17+**: Required for running the Spring Boot application
4. **Maven**: For building and running tests

### Setting up Test Environment

#### 1. Install and Start MQTT Broker (Mosquitto)

**On Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install mosquitto mosquitto-clients
sudo systemctl start mosquitto
sudo systemctl enable mosquitto
```

**On macOS:**
```bash
brew install mosquitto
brew services start mosquitto
```

**On Windows:**
Download and install from [Eclipse Mosquitto](https://mosquitto.org/download/)

#### 2. Install and Start Redis Server

**On Ubuntu/Debian:**
```bash
sudo apt-get install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**On macOS:**
```bash
brew install redis
brew services start redis
```

**On Windows:**
Download and install from [Redis Downloads](https://redis.io/download)

#### 3. Configure Redis for Testing
```bash
# Start Redis on test port
redis-server --port 6370 --daemonize yes
```

## Running the Tests

### Option 1: Run All Integration Tests
```bash
# Run the comprehensive test suite
mvn test -Dtest=ComprehensiveIntegrationTestSuite

# Or run the test runner for detailed reporting
mvn test -Dtest=IntegrationTestRunner
```

### Option 2: Run Individual Test Classes
```bash
# Run message flow tests
mvn test -Dtest=CompleteMessageFlowIntegrationTest

# Run cluster tests
mvn test -Dtest=MultiInstanceClusterTest

# Run encryption tests
mvn test -Dtest=EncryptedMessageEndToEndTest

# Run load tests
mvn test -Dtest=LoadTestingAndConcurrencyTest

# Run failure recovery tests
mvn test -Dtest=FailureRecoveryAndDataConsistencyTest
```

### Option 3: Run Specific Test Methods
```bash
# Run specific test method
mvn test -Dtest=CompleteMessageFlowIntegrationTest#testMqttToWebSocketMessageFlow
```

## Test Configuration

The tests use the following configuration (defined in `src/test/resources/application-test.yml`):

```yaml
# Key test configurations
app:
  mqtt:
    broker-url: tcp://localhost:1883
    automatic-reconnect: true
  websocket:
    endpoint: /ws
    max-session-idle-timeout: 300000
  encryption:
    enabled: true
    algorithm: AES
    key-length: 256
  cluster:
    enabled: true
    health-check-interval: 5000
```

## Performance Benchmarks

The tests verify the following performance benchmarks:

| Metric | Minimum Requirement | Test Verification |
|--------|-------------------|------------------|
| Message Throughput | ≥100 messages/second | ✅ Load tests |
| Encryption Performance | ≥100 operations/second | ✅ Encryption tests |
| Concurrent Connections | ≥100 simultaneous | ✅ Concurrency tests |
| Average Response Time | <1000ms | ✅ Performance tests |
| 95th Percentile Response Time | <2000ms | ✅ Performance tests |
| Connection Success Rate | ≥95% | ✅ Reliability tests |
| Connection Stability Rate | ≥90% | ✅ Stability tests |
| Memory Usage per Connection | <1MB | ✅ Memory tests |

## Test Reports

After running the tests, detailed reports are generated including:

- **Execution Summary**: Test counts, success rates, execution times
- **Functional Coverage**: Verification of all required features
- **Performance Metrics**: Benchmark verification results
- **Failure Details**: Detailed information about any test failures
- **Requirements Verification**: Mapping to original requirements

## Troubleshooting

### Common Issues

1. **MQTT Connection Failed**
   ```
   Solution: Ensure Mosquitto broker is running on localhost:1883
   Check: netstat -an | grep 1883
   ```

2. **Redis Connection Failed**
   ```
   Solution: Ensure Redis server is running on localhost:6370
   Check: redis-cli -p 6370 ping
   ```

3. **WebSocket Connection Timeout**
   ```
   Solution: Check if application is running and port is available
   Increase timeout in test configuration if needed
   ```

4. **Memory Issues During Load Tests**
   ```
   Solution: Increase JVM heap size
   Run with: mvn test -Dtest=LoadTestingAndConcurrencyTest -Xmx2g
   ```

5. **Test Execution Timeout**
   ```
   Solution: Some tests may take longer on slower systems
   Increase test timeouts in individual test classes if needed
   ```

### Debug Mode

To run tests with debug logging:
```bash
mvn test -Dtest=IntegrationTestRunner -Dlogging.level.com.example.mqtt.websocket=DEBUG
```

### Test Data Cleanup

Tests automatically clean up their data, but if manual cleanup is needed:
```bash
# Clear Redis test data
redis-cli -p 6370 FLUSHDB

# Clear MQTT retained messages (if any)
mosquitto_pub -h localhost -t 'test/+' -n -r -d
```

## Requirements Verification

The integration tests verify the following requirements from the original specification:

- **Requirement 1.1**: 集群部署支持 ✅
- **Requirement 2.1**: MQTT消息处理 ✅  
- **Requirement 3.2**: WebSocket实时通信 ✅
- **Requirement 5.2**: 消息加密传输 ✅

## Contributing

When adding new integration tests:

1. Follow the existing test structure and naming conventions
2. Include comprehensive error handling and cleanup
3. Add performance benchmarks where applicable
4. Update this README with new test descriptions
5. Ensure tests are deterministic and can run in any order

## Support

For issues with the integration tests:

1. Check the troubleshooting section above
2. Review test logs for detailed error information
3. Verify all prerequisites are properly installed and configured
4. Check that all required services are running and accessible