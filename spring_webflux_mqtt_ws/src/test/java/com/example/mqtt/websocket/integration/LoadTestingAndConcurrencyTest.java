package com.example.mqtt.websocket.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.eclipse.paho.client.mqttv3.*;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.socket.*;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 负载测试和并发测试套件
 * 测试系统在高负载和高并发情况下的性能和稳定性
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.websocket.endpoint=/ws",
    "app.websocket.max-session-idle-timeout=300000", // 5 minutes
    "app.websocket.max-text-message-buffer-size=8192",
    "server.tomcat.threads.max=200",
    "server.tomcat.accept-count=100",
    "logging.level.com.example.mqtt.websocket=INFO" // 减少日志输出
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class LoadTestingAndConcurrencyTest {
    
    @LocalServerPort
    private int port;
    
    private ObjectMapper objectMapper;
    private ExecutorService executorService;
    private List<WebSocketSession> webSocketSessions;
    private List<MqttClient> mqttClients;
    
    // 性能统计
    private AtomicInteger totalMessagesSent;
    private AtomicInteger totalMessagesReceived;
    private AtomicInteger totalConnectionsEstablished;
    private AtomicInteger totalConnectionsFailed;
    private AtomicLong totalResponseTime;
    private AtomicInteger responseCount;
    
    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        executorService = Executors.newFixedThreadPool(50);
        webSocketSessions = Collections.synchronizedList(new ArrayList<>());
        mqttClients = Collections.synchronizedList(new ArrayList<>());
        
        // 初始化性能统计
        totalMessagesSent = new AtomicInteger(0);
        totalMessagesReceived = new AtomicInteger(0);
        totalConnectionsEstablished = new AtomicInteger(0);
        totalConnectionsFailed = new AtomicInteger(0);
        totalResponseTime = new AtomicLong(0);
        responseCount = new AtomicInteger(0);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        // 关闭所有 WebSocket 连接
        for (WebSocketSession session : webSocketSessions) {
            try {
                if (session.isOpen()) {
                    session.close();
                }
            } catch (Exception e) {
                System.err.println("Error closing WebSocket session: " + e.getMessage());
            }
        }
        webSocketSessions.clear();
        
        // 关闭所有 MQTT 客户端
        for (MqttClient client : mqttClients) {
            try {
                if (client.isConnected()) {
                    client.disconnect();
                    client.close();
                }
            } catch (Exception e) {
                System.err.println("Error closing MQTT client: " + e.getMessage());
            }
        }
        mqttClients.clear();
        
        if (executorService != null) {
            executorService.shutdown();
            executorService.awaitTermination(10, TimeUnit.SECONDS);
        }
        
        // 打印性能统计
        printPerformanceStatistics();
    }
    
    private void printPerformanceStatistics() {
        System.out.println("\n=== Performance Statistics ===");
        System.out.println("Total messages sent: " + totalMessagesSent.get());
        System.out.println("Total messages received: " + totalMessagesReceived.get());
        System.out.println("Total connections established: " + totalConnectionsEstablished.get());
        System.out.println("Total connection failures: " + totalConnectionsFailed.get());
        
        if (responseCount.get() > 0) {
            double avgResponseTime = (double) totalResponseTime.get() / responseCount.get();
            System.out.println("Average response time: " + String.format("%.2f ms", avgResponseTime));
        }
        
        double successRate = totalConnectionsFailed.get() == 0 ? 100.0 : 
            (double) totalConnectionsEstablished.get() / 
            (totalConnectionsEstablished.get() + totalConnectionsFailed.get()) * 100;
        System.out.println("Connection success rate: " + String.format("%.2f%%", successRate));
        System.out.println("===============================\n");
    }
    
    @Test
    @Order(1)
    void testHighConcurrentWebSocketConnections() throws Exception {
        System.out.println("Testing high concurrent WebSocket connections");
        
        int connectionCount = 100;
        CountDownLatch connectionLatch = new CountDownLatch(connectionCount);
        AtomicInteger successfulConnections = new AtomicInteger(0);
        AtomicReference<Exception> connectionError = new AtomicReference<>();
        
        long startTime = System.currentTimeMillis();
        
        // 并发建立大量 WebSocket 连接
        for (int i = 0; i < connectionCount; i++) {
            final int clientId = i;
            
            executorService.submit(() -> {
                try {
                    WebSocketClient client = new StandardWebSocketClient();
                    URI uri = URI.create("ws://localhost:" + port + "/ws?userId=loadTestUser" + clientId);
                    
                    WebSocketHandler handler = new WebSocketHandler() {
                        @Override
                        public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                            successfulConnections.incrementAndGet();
                            totalConnectionsEstablished.incrementAndGet();
                            connectionLatch.countDown();
                        }
                        
                        @Override
                        public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                            totalMessagesReceived.incrementAndGet();
                        }
                        
                        @Override
                        public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                            totalConnectionsFailed.incrementAndGet();
                            connectionError.set(new Exception("Transport error for client " + clientId, exception));
                            connectionLatch.countDown();
                        }
                        
                        @Override
                        public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {}
                        
                        @Override
                        public boolean supportsPartialMessages() {
                            return false;
                        }
                    };
                    
                    WebSocketSession session = client.doHandshake(handler, null, uri).get(30, TimeUnit.SECONDS);
                    webSocketSessions.add(session);
                    
                } catch (Exception e) {
                    totalConnectionsFailed.incrementAndGet();
                    connectionError.set(e);
                    connectionLatch.countDown();
                }
            });
        }
        
        // 等待所有连接完成
        boolean allConnected = connectionLatch.await(60, TimeUnit.SECONDS);
        long connectionTime = System.currentTimeMillis() - startTime;
        
        System.out.println("Connection establishment time: " + connectionTime + " ms");
        System.out.println("Successful connections: " + successfulConnections.get() + "/" + connectionCount);
        
        assertTrue(allConnected, "All connections should be established within timeout");
        assertTrue(successfulConnections.get() >= connectionCount * 0.95, 
            "At least 95% of connections should be successful");
        
        if (connectionError.get() != null && successfulConnections.get() < connectionCount * 0.95) {
            throw connectionError.get();
        }
        
        System.out.println("High concurrent WebSocket connections test completed successfully");
    }
    
    @Test
    @Order(2)
    void testHighThroughputMessageProcessing() throws Exception {
        System.out.println("Testing high throughput message processing");
        
        // 首先建立一些连接
        int connectionCount = 20;
        setupWebSocketConnections(connectionCount);
        
        int messagesPerConnection = 50;
        int totalMessages = connectionCount * messagesPerConnection;
        CountDownLatch messageLatch = new CountDownLatch(totalMessages);
        
        long startTime = System.currentTimeMillis();
        
        // 并发发送大量消息
        for (int i = 0; i < connectionCount; i++) {
            final int connectionIndex = i;
            final WebSocketSession session = webSocketSessions.get(i);
            
            executorService.submit(() -> {
                for (int j = 0; j < messagesPerConnection; j++) {
                    try {
                        String message = objectMapper.writeValueAsString(Map.of(
                            "type", "throughput_test",
                            "connectionId", connectionIndex,
                            "messageId", j,
                            "timestamp", System.currentTimeMillis(),
                            "data", "Load test message " + connectionIndex + "-" + j
                        ));
                        
                        session.sendMessage(new TextMessage(message));
                        totalMessagesSent.incrementAndGet();
                        messageLatch.countDown();
                        
                        // 小延迟避免过快发送
                        Thread.sleep(10);
                        
                    } catch (Exception e) {
                        System.err.println("Error sending message: " + e.getMessage());
                        messageLatch.countDown();
                    }
                }
            });
        }
        
        // 等待所有消息发送完成
        boolean allSent = messageLatch.await(120, TimeUnit.SECONDS);
        long processingTime = System.currentTimeMillis() - startTime;
        
        assertTrue(allSent, "All messages should be sent within timeout");
        
        double messagesPerSecond = (double) totalMessages / (processingTime / 1000.0);
        System.out.println("Message throughput: " + String.format("%.2f messages/second", messagesPerSecond));
        System.out.println("Total processing time: " + processingTime + " ms");
        
        // 性能基准：至少每秒处理100条消息
        assertTrue(messagesPerSecond >= 100, "Message throughput should be at least 100 messages/second");
        
        System.out.println("High throughput message processing test completed successfully");
    }
    
    @Test
    @Order(3)
    void testConcurrentMqttAndWebSocketTraffic() throws Exception {
        System.out.println("Testing concurrent MQTT and WebSocket traffic");
        
        // 建立 WebSocket 连接
        int wsConnectionCount = 15;
        setupWebSocketConnections(wsConnectionCount);
        
        // 建立 MQTT 连接
        int mqttConnectionCount = 10;
        setupMqttConnections(mqttConnectionCount);
        
        int messagesPerProtocol = 30;
        CountDownLatch trafficLatch = new CountDownLatch(messagesPerProtocol * 2); // MQTT + WebSocket
        
        long startTime = System.currentTimeMillis();
        
        // 并发 WebSocket 流量
        executorService.submit(() -> {
            for (int i = 0; i < messagesPerProtocol; i++) {
                try {
                    for (WebSocketSession session : webSocketSessions) {
                        if (session.isOpen()) {
                            String message = objectMapper.writeValueAsString(Map.of(
                                "type", "concurrent_ws_test",
                                "messageId", i,
                                "timestamp", System.currentTimeMillis(),
                                "protocol", "websocket"
                            ));
                            
                            session.sendMessage(new TextMessage(message));
                            totalMessagesSent.incrementAndGet();
                        }
                    }
                    trafficLatch.countDown();
                    Thread.sleep(50);
                } catch (Exception e) {
                    System.err.println("WebSocket traffic error: " + e.getMessage());
                    trafficLatch.countDown();
                }
            }
        });
        
        // 并发 MQTT 流量
        executorService.submit(() -> {
            for (int i = 0; i < messagesPerProtocol; i++) {
                try {
                    for (MqttClient client : mqttClients) {
                        if (client.isConnected()) {
                            String message = objectMapper.writeValueAsString(Map.of(
                                "type", "concurrent_mqtt_test",
                                "messageId", i,
                                "timestamp", System.currentTimeMillis(),
                                "protocol", "mqtt"
                            ));
                            
                            MqttMessage mqttMessage = new MqttMessage(message.getBytes(StandardCharsets.UTF_8));
                            mqttMessage.setQos(0); // 使用 QoS 0 提高性能
                            client.publish("test/concurrent/traffic", mqttMessage);
                            totalMessagesSent.incrementAndGet();
                        }
                    }
                    trafficLatch.countDown();
                    Thread.sleep(50);
                } catch (Exception e) {
                    System.err.println("MQTT traffic error: " + e.getMessage());
                    trafficLatch.countDown();
                }
            }
        });
        
        // 等待所有流量完成
        boolean allTrafficCompleted = trafficLatch.await(180, TimeUnit.SECONDS);
        long trafficTime = System.currentTimeMillis() - startTime;
        
        assertTrue(allTrafficCompleted, "All concurrent traffic should complete within timeout");
        
        System.out.println("Concurrent traffic processing time: " + trafficTime + " ms");
        System.out.println("Total messages sent: " + totalMessagesSent.get());
        
        System.out.println("Concurrent MQTT and WebSocket traffic test completed successfully");
    }
    
    @Test
    @Order(4)
    void testMemoryUsageUnderLoad() throws Exception {
        System.out.println("Testing memory usage under load");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存使用
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("Initial memory usage: " + (initialMemory / 1024 / 1024) + " MB");
        
        // 建立大量连接
        int connectionCount = 50;
        setupWebSocketConnections(connectionCount);
        
        // 记录连接后内存使用
        runtime.gc(); // 建议垃圾回收
        Thread.sleep(1000);
        long afterConnectionsMemory = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("Memory after connections: " + (afterConnectionsMemory / 1024 / 1024) + " MB");
        
        // 发送大量消息
        int messageCount = 1000;
        CountDownLatch memoryTestLatch = new CountDownLatch(messageCount);
        
        for (int i = 0; i < messageCount; i++) {
            final int messageIndex = i;
            
            executorService.submit(() -> {
                try {
                    // 创建较大的消息内容
                    StringBuilder largeContent = new StringBuilder();
                    for (int j = 0; j < 100; j++) {
                        largeContent.append("Memory test data ").append(j).append(" ");
                    }
                    
                    String message = objectMapper.writeValueAsString(Map.of(
                        "type", "memory_test",
                        "messageId", messageIndex,
                        "timestamp", System.currentTimeMillis(),
                        "largeContent", largeContent.toString()
                    ));
                    
                    // 随机选择一个连接发送消息
                    if (!webSocketSessions.isEmpty()) {
                        WebSocketSession session = webSocketSessions.get(messageIndex % webSocketSessions.size());
                        if (session.isOpen()) {
                            session.sendMessage(new TextMessage(message));
                            totalMessagesSent.incrementAndGet();
                        }
                    }
                    
                } catch (Exception e) {
                    System.err.println("Memory test message error: " + e.getMessage());
                } finally {
                    memoryTestLatch.countDown();
                }
            });
        }
        
        // 等待所有消息处理完成
        boolean allProcessed = memoryTestLatch.await(120, TimeUnit.SECONDS);
        assertTrue(allProcessed, "All memory test messages should be processed");
        
        // 记录峰值内存使用
        long peakMemory = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("Peak memory usage: " + (peakMemory / 1024 / 1024) + " MB");
        
        // 清理并检查内存回收
        webSocketSessions.clear();
        runtime.gc();
        Thread.sleep(2000);
        
        long afterCleanupMemory = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("Memory after cleanup: " + (afterCleanupMemory / 1024 / 1024) + " MB");
        
        // 验证内存使用合理
        long memoryIncrease = peakMemory - initialMemory;
        long memoryPerConnection = memoryIncrease / connectionCount;
        
        System.out.println("Memory increase per connection: " + (memoryPerConnection / 1024) + " KB");
        
        // 内存使用应该合理（每个连接不超过1MB）
        assertTrue(memoryPerConnection < 1024 * 1024, "Memory usage per connection should be reasonable");
        
        System.out.println("Memory usage under load test completed successfully");
    }
    
    @Test
    @Order(5)
    void testResponseTimeUnderLoad() throws Exception {
        System.out.println("Testing response time under load");
        
        // 建立连接
        int connectionCount = 20;
        setupWebSocketConnections(connectionCount);
        
        int requestCount = 100;
        CountDownLatch responseLatch = new CountDownLatch(requestCount);
        List<Long> responseTimes = Collections.synchronizedList(new ArrayList<>());
        
        // 发送请求并测量响应时间
        for (int i = 0; i < requestCount; i++) {
            final int requestIndex = i;
            
            executorService.submit(() -> {
                try {
                    long requestStartTime = System.currentTimeMillis();
                    
                    String request = objectMapper.writeValueAsString(Map.of(
                        "type", "response_time_test",
                        "requestId", requestIndex,
                        "timestamp", requestStartTime,
                        "expectResponse", true
                    ));
                    
                    // 选择一个连接发送请求
                    WebSocketSession session = webSocketSessions.get(requestIndex % webSocketSessions.size());
                    if (session.isOpen()) {
                        session.sendMessage(new TextMessage(request));
                        totalMessagesSent.incrementAndGet();
                        
                        // 模拟响应时间（在实际场景中，这会通过消息处理器测量）
                        Thread.sleep(10 + (requestIndex % 50)); // 模拟10-60ms的处理时间
                        
                        long responseTime = System.currentTimeMillis() - requestStartTime;
                        responseTimes.add(responseTime);
                        totalResponseTime.addAndGet(responseTime);
                        responseCount.incrementAndGet();
                    }
                    
                } catch (Exception e) {
                    System.err.println("Response time test error: " + e.getMessage());
                } finally {
                    responseLatch.countDown();
                }
            });
        }
        
        // 等待所有请求完成
        boolean allCompleted = responseLatch.await(60, TimeUnit.SECONDS);
        assertTrue(allCompleted, "All response time tests should complete");
        
        // 计算响应时间统计
        Collections.sort(responseTimes);
        
        double averageResponseTime = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        long medianResponseTime = responseTimes.get(responseTimes.size() / 2);
        long p95ResponseTime = responseTimes.get((int) (responseTimes.size() * 0.95));
        long maxResponseTime = responseTimes.get(responseTimes.size() - 1);
        
        System.out.println("Response time statistics:");
        System.out.println("  Average: " + String.format("%.2f ms", averageResponseTime));
        System.out.println("  Median: " + medianResponseTime + " ms");
        System.out.println("  95th percentile: " + p95ResponseTime + " ms");
        System.out.println("  Maximum: " + maxResponseTime + " ms");
        
        // 性能基准验证
        assertTrue(averageResponseTime < 1000, "Average response time should be under 1 second");
        assertTrue(p95ResponseTime < 2000, "95th percentile response time should be under 2 seconds");
        
        System.out.println("Response time under load test completed successfully");
    }
    
    @Test
    @Order(6)
    void testConnectionStabilityUnderLoad() throws Exception {
        System.out.println("Testing connection stability under load");
        
        int connectionCount = 30;
        int testDurationMinutes = 2; // 2分钟稳定性测试
        
        // 建立连接
        setupWebSocketConnections(connectionCount);
        
        AtomicInteger activeConnections = new AtomicInteger(connectionCount);
        AtomicInteger reconnectionAttempts = new AtomicInteger(0);
        
        long testStartTime = System.currentTimeMillis();
        long testEndTime = testStartTime + (testDurationMinutes * 60 * 1000);
        
        // 持续发送消息测试连接稳定性
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(5);
        
        scheduler.scheduleAtFixedRate(() -> {
            if (System.currentTimeMillis() < testEndTime) {
                for (int i = 0; i < webSocketSessions.size(); i++) {
                    WebSocketSession session = webSocketSessions.get(i);
                    
                    try {
                        if (session.isOpen()) {
                            String heartbeat = objectMapper.writeValueAsString(Map.of(
                                "type", "stability_heartbeat",
                                "sessionId", i,
                                "timestamp", System.currentTimeMillis()
                            ));
                            
                            session.sendMessage(new TextMessage(heartbeat));
                            totalMessagesSent.incrementAndGet();
                        } else {
                            activeConnections.decrementAndGet();
                            // 在实际场景中，这里会尝试重连
                            reconnectionAttempts.incrementAndGet();
                        }
                    } catch (Exception e) {
                        System.err.println("Stability test error for session " + i + ": " + e.getMessage());
                        activeConnections.decrementAndGet();
                    }
                }
            }
        }, 0, 1, TimeUnit.SECONDS);
        
        // 等待测试完成
        Thread.sleep(testDurationMinutes * 60 * 1000);
        scheduler.shutdown();
        
        long actualTestDuration = System.currentTimeMillis() - testStartTime;
        
        System.out.println("Stability test results:");
        System.out.println("  Test duration: " + (actualTestDuration / 1000) + " seconds");
        System.out.println("  Initial connections: " + connectionCount);
        System.out.println("  Active connections at end: " + activeConnections.get());
        System.out.println("  Reconnection attempts: " + reconnectionAttempts.get());
        System.out.println("  Total messages sent: " + totalMessagesSent.get());
        
        // 稳定性验证
        double connectionStabilityRate = (double) activeConnections.get() / connectionCount * 100;
        System.out.println("  Connection stability rate: " + String.format("%.2f%%", connectionStabilityRate));
        
        assertTrue(connectionStabilityRate >= 90, "Connection stability should be at least 90%");
        
        System.out.println("Connection stability under load test completed successfully");
    }
    
    private void setupWebSocketConnections(int count) throws Exception {
        CountDownLatch setupLatch = new CountDownLatch(count);
        
        for (int i = 0; i < count; i++) {
            final int clientId = i;
            
            executorService.submit(() -> {
                try {
                    WebSocketClient client = new StandardWebSocketClient();
                    URI uri = URI.create("ws://localhost:" + port + "/ws?userId=loadTestUser" + clientId);
                    
                    WebSocketHandler handler = new WebSocketHandler() {
                        @Override
                        public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                            totalConnectionsEstablished.incrementAndGet();
                            setupLatch.countDown();
                        }
                        
                        @Override
                        public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                            totalMessagesReceived.incrementAndGet();
                        }
                        
                        @Override
                        public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                            totalConnectionsFailed.incrementAndGet();
                        }
                        
                        @Override
                        public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {}
                        
                        @Override
                        public boolean supportsPartialMessages() {
                            return false;
                        }
                    };
                    
                    WebSocketSession session = client.doHandshake(handler, null, uri).get(30, TimeUnit.SECONDS);
                    webSocketSessions.add(session);
                    
                } catch (Exception e) {
                    totalConnectionsFailed.incrementAndGet();
                    setupLatch.countDown();
                    System.err.println("Failed to setup WebSocket connection " + clientId + ": " + e.getMessage());
                }
            });
        }
        
        boolean allSetup = setupLatch.await(60, TimeUnit.SECONDS);
        assertTrue(allSetup, "All WebSocket connections should be established");
    }
    
    private void setupMqttConnections(int count) throws Exception {
        CountDownLatch setupLatch = new CountDownLatch(count);
        
        for (int i = 0; i < count; i++) {
            final int clientId = i;
            
            executorService.submit(() -> {
                try {
                    String mqttClientId = "load-test-mqtt-client-" + clientId + "-" + System.currentTimeMillis();
                    MqttClient client = new MqttClient("tcp://localhost:1883", mqttClientId);
                    
                    MqttConnectOptions options = new MqttConnectOptions();
                    options.setCleanSession(true);
                    options.setConnectionTimeout(10);
                    options.setKeepAliveInterval(30);
                    
                    client.setCallback(new MqttCallback() {
                        @Override
                        public void connectionLost(Throwable cause) {
                            totalConnectionsFailed.incrementAndGet();
                        }
                        
                        @Override
                        public void messageArrived(String topic, MqttMessage message) throws Exception {
                            totalMessagesReceived.incrementAndGet();
                        }
                        
                        @Override
                        public void deliveryComplete(IMqttDeliveryToken token) {}
                    });
                    
                    client.connect(options);
                    client.subscribe("test/load/+/data", 0);
                    
                    mqttClients.add(client);
                    totalConnectionsEstablished.incrementAndGet();
                    setupLatch.countDown();
                    
                } catch (Exception e) {
                    totalConnectionsFailed.incrementAndGet();
                    setupLatch.countDown();
                    System.err.println("Failed to setup MQTT connection " + clientId + ": " + e.getMessage());
                }
            });
        }
        
        boolean allSetup = setupLatch.await(60, TimeUnit.SECONDS);
        assertTrue(allSetup, "All MQTT connections should be established");
    }
}