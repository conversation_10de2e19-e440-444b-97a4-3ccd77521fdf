package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WebSocketSessionManager 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class WebSocketSessionManagerTest {
    
    @Mock
    private WebSocketSession mockSession1;
    
    @Mock
    private WebSocketSession mockSession2;
    
    private WebSocketSessionManager sessionManager;
    
    @BeforeEach
    void setUp() {
        sessionManager = new WebSocketSessionManager();
    }
    
    @Test
    void shouldRegisterSession() {
        // Given
        String sessionId = "session-1";
        String userId = "user-1";
        when(mockSession1.getId()).thenReturn(sessionId);
        
        // When
        sessionManager.registerSession(mockSession1, userId);
        
        // Then
        assertEquals(1, sessionManager.getActiveSessionCount());
        assertTrue(sessionManager.hasSession(sessionId));
        assertEquals(mockSession1, sessionManager.getSessionByUserId(userId));
    }
    
    @Test
    void shouldRegisterSessionWithoutUserId() {
        // Given
        String sessionId = "session-1";
        when(mockSession1.getId()).thenReturn(sessionId);
        
        // When
        sessionManager.registerSession(mockSession1, null);
        
        // Then
        assertEquals(1, sessionManager.getActiveSessionCount());
        assertTrue(sessionManager.hasSession(sessionId));
        assertNull(sessionManager.getSessionByUserId("any-user"));
    }
    
    @Test
    void shouldUnregisterSession() {
        // Given
        String sessionId = "session-1";
        String userId = "user-1";
        when(mockSession1.getId()).thenReturn(sessionId);
        
        sessionManager.registerSession(mockSession1, userId);
        assertEquals(1, sessionManager.getActiveSessionCount());
        
        // When
        sessionManager.unregisterSession(sessionId);
        
        // Then
        assertEquals(0, sessionManager.getActiveSessionCount());
        assertFalse(sessionManager.hasSession(sessionId));
        assertNull(sessionManager.getSessionByUserId(userId));
    }
    
    @Test
    void shouldHandleMultipleSessions() {
        // Given
        String sessionId1 = "session-1";
        String sessionId2 = "session-2";
        String userId1 = "user-1";
        String userId2 = "user-2";
        
        when(mockSession1.getId()).thenReturn(sessionId1);
        when(mockSession2.getId()).thenReturn(sessionId2);
        
        // When
        sessionManager.registerSession(mockSession1, userId1);
        sessionManager.registerSession(mockSession2, userId2);
        
        // Then
        assertEquals(2, sessionManager.getActiveSessionCount());
        assertTrue(sessionManager.hasSession(sessionId1));
        assertTrue(sessionManager.hasSession(sessionId2));
        assertEquals(mockSession1, sessionManager.getSessionByUserId(userId1));
        assertEquals(mockSession2, sessionManager.getSessionByUserId(userId2));
    }
    
    @Test
    void shouldBroadcastMessage() {
        // Given
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId("msg-1")
                .setTimestamp(System.currentTimeMillis())
                .setType("test")
                .setSource("test-source")
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setType(WebSocketMessage.MessageType.BROADCAST)
                .setMessage(baseMessage)
                .build();
        
        // When & Then
        StepVerifier.create(sessionManager.broadcastMessage(wsMessage))
                .verifyComplete();
        
        // Verify broadcast stream receives the message
        StepVerifier.create(sessionManager.getBroadcastStream().take(1))
                .expectNext(wsMessage)
                .verifyComplete();
    }
    
    @Test
    void shouldSendToUserWhenSessionExists() {
        // Given
        String sessionId = "session-1";
        String userId = "user-1";
        when(mockSession1.getId()).thenReturn(sessionId);
        when(mockSession1.send(any())).thenReturn(Mono.empty());
        when(mockSession1.binaryMessage(any())).thenReturn(mock(org.springframework.web.reactive.socket.WebSocketMessage.class));
        
        sessionManager.registerSession(mockSession1, userId);
        
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId("msg-1")
                .setTimestamp(System.currentTimeMillis())
                .setType("test")
                .setSource("test-source")
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setUserId(userId)
                .setType(WebSocketMessage.MessageType.UNICAST)
                .setMessage(baseMessage)
                .build();
        
        // When & Then
        StepVerifier.create(sessionManager.sendToUser(userId, wsMessage))
                .verifyComplete();
        
        verify(mockSession1).send(any());
    }
    
    @Test
    void shouldFailToSendToNonExistentUser() {
        // Given
        String userId = "non-existent-user";
        
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId("msg-1")
                .setTimestamp(System.currentTimeMillis())
                .setType("test")
                .setSource("test-source")
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setUserId(userId)
                .setType(WebSocketMessage.MessageType.UNICAST)
                .setMessage(baseMessage)
                .build();
        
        // When & Then
        StepVerifier.create(sessionManager.sendToUser(userId, wsMessage))
                .expectError(RuntimeException.class)
                .verify();
    }
    
    @Test
    void shouldClearAllSessions() {
        // Given
        String sessionId1 = "session-1";
        String sessionId2 = "session-2";
        when(mockSession1.getId()).thenReturn(sessionId1);
        when(mockSession2.getId()).thenReturn(sessionId2);
        
        sessionManager.registerSession(mockSession1, "user-1");
        sessionManager.registerSession(mockSession2, "user-2");
        assertEquals(2, sessionManager.getActiveSessionCount());
        
        // When
        sessionManager.clearAllSessions();
        
        // Then
        assertEquals(0, sessionManager.getActiveSessionCount());
        assertFalse(sessionManager.hasSession(sessionId1));
        assertFalse(sessionManager.hasSession(sessionId2));
    }
}