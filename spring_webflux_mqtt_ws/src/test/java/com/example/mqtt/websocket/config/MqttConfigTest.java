package com.example.mqtt.websocket.config;

import com.example.mqtt.websocket.config.properties.MqttProperties;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.messaging.MessageChannel;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * MQTT 配置测试类
 */
@ExtendWith(MockitoExtension.class)
class MqttConfigTest {

    @Mock
    private MqttProperties mqttProperties;

    @InjectMocks
    private MqttConfig mqttConfig;

    @BeforeEach
    void setUp() {
        // 设置默认的 MQTT 属性
        when(mqttProperties.getBrokerUrl()).thenReturn("tcp://localhost:1883");
        when(mqttProperties.getConnectionTimeout()).thenReturn(30);
        when(mqttProperties.getKeepAliveInterval()).thenReturn(60);
        when(mqttProperties.isCleanSession()).thenReturn(true);
        when(mqttProperties.isAutomaticReconnect()).thenReturn(true);
        when(mqttProperties.getUsername()).thenReturn("testuser");
        when(mqttProperties.getPassword()).thenReturn("testpass");
        when(mqttProperties.getClientId()).thenReturn("test-client");
        when(mqttProperties.getSubscribeTopics()).thenReturn(Arrays.asList("test/topic1", "test/topic2"));
    }

    @Test
    void shouldCreateMqttClientFactory() {
        // When
        MqttPahoClientFactory factory = mqttConfig.mqttClientFactory();

        // Then
        assertThat(factory).isNotNull();
        assertThat(factory).isInstanceOf(DefaultMqttPahoClientFactory.class);
    }

    @Test
    void shouldCreateMqttInputChannel() {
        // When
        MessageChannel channel = mqttConfig.mqttInputChannel();

        // Then
        assertThat(channel).isNotNull();
    }

    @Test
    void shouldCreateMqttOutboundChannel() {
        // When
        MessageChannel channel = mqttConfig.mqttOutboundChannel();

        // Then
        assertThat(channel).isNotNull();
    }

    @Test
    void shouldCreateMqttErrorChannel() {
        // When
        MessageChannel channel = mqttConfig.mqttErrorChannel();

        // Then
        assertThat(channel).isNotNull();
    }

    @Test
    void shouldCreateMqttInboundAdapter() {
        // When
        var adapter = mqttConfig.mqttInbound();

        // Then
        assertThat(adapter).isNotNull();
    }

    @Test
    void shouldCreateMqttOutboundHandler() {
        // When
        var handler = mqttConfig.mqttOutbound();

        // Then
        assertThat(handler).isNotNull();
    }
}