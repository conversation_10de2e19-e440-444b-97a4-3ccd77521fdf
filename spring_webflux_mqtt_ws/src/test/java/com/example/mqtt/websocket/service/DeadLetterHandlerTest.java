package com.example.mqtt.websocket.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.ReactiveHashOperations;
import org.springframework.data.redis.core.ReactiveListOperations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.GenericMessage;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DeadLetterHandler 单元测试
 */
@ExtendWith(MockitoExtension.class)
class DeadLetterHandlerTest {
    
    @Mock
    private ReactiveRedisTemplate<String, Object> redisTemplate;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private MeterRegistry meterRegistry;
    
    @Mock
    private ReactiveListOperations<String, Object> listOperations;
    
    @Mock
    private ReactiveHashOperations<String, Object, Object> hashOperations;
    
    @Mock
    private Counter mockCounter;
    
    @InjectMocks
    private DeadLetterHandler deadLetterHandler;
    
    @BeforeEach
    void setUp() {
        when(meterRegistry.counter(anyString())).thenReturn(mockCounter);
        when(meterRegistry.counter(anyString(), anyString(), anyString())).thenReturn(mockCounter);
        when(redisTemplate.opsForList()).thenReturn(listOperations);
        when(redisTemplate.opsForHash()).thenReturn(hashOperations);
        
        deadLetterHandler.initMetrics();
    }
    
    @Test
    void shouldHandleDeadLetterMessage() throws Exception {
        // Given
        String payload = "test message";
        Map<String, Object> headers = new HashMap<>();
        headers.put("messageId", "msg-123");
        headers.put("errorCause", new RuntimeException("Processing failed"));
        
        Message<String> failedMessage = new GenericMessage<>(payload, headers);
        
        when(listOperations.leftPush(anyString(), anyString())).thenReturn(Mono.just(1L));
        when(hashOperations.putAll(anyString(), anyMap())).thenReturn(Mono.just(true));
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"id\":\"test\"}");
        
        // When
        deadLetterHandler.handleDeadLetter(failedMessage);
        
        // Then
        verify(mockCounter).increment();
        verify(objectMapper).writeValueAsString(any(DeadLetterHandler.DeadLetterRecord.class));
        verify(listOperations).leftPush(eq("dead_letter_queue"), anyString());
        verify(hashOperations).putAll(eq("dead_letter_stats"), anyMap());
    }
    
    @Test
    void shouldHandleDeadLetterMessageWithoutError() throws Exception {
        // Given
        String payload = "test message";
        Map<String, Object> headers = new HashMap<>();
        headers.put("messageId", "msg-123");
        
        Message<String> failedMessage = new GenericMessage<>(payload, headers);
        
        when(listOperations.leftPush(anyString(), anyString())).thenReturn(Mono.just(1L));
        when(hashOperations.putAll(anyString(), anyMap())).thenReturn(Mono.just(true));
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"id\":\"test\"}");
        
        // When
        deadLetterHandler.handleDeadLetter(failedMessage);
        
        // Then
        verify(mockCounter).increment();
        verify(objectMapper).writeValueAsString(any(DeadLetterHandler.DeadLetterRecord.class));
    }
    
    @Test
    void shouldGetNextDeadLetterMessage() throws Exception {
        // Given
        String recordJson = "{\"id\":\"test-id\",\"type\":\"TestException\"}";
        DeadLetterHandler.DeadLetterRecord expectedRecord = new DeadLetterHandler.DeadLetterRecord();
        expectedRecord.setId("test-id");
        
        when(listOperations.rightPop(anyString())).thenReturn(Mono.just(recordJson));
        when(objectMapper.readValue(recordJson, DeadLetterHandler.DeadLetterRecord.class))
            .thenReturn(expectedRecord);
        
        // When
        Mono<DeadLetterHandler.DeadLetterRecord> result = deadLetterHandler.getNextDeadLetterMessage();
        
        // Then
        StepVerifier.create(result)
            .expectNext(expectedRecord)
            .verifyComplete();
        
        verify(listOperations).rightPop("dead_letter_queue");
        verify(objectMapper).readValue(recordJson, DeadLetterHandler.DeadLetterRecord.class);
    }
    
    @Test
    void shouldGetDeadLetterQueueSize() {
        // Given
        when(listOperations.size(anyString())).thenReturn(Mono.just(5L));
        
        // When
        Mono<Long> result = deadLetterHandler.getDeadLetterQueueSize();
        
        // Then
        StepVerifier.create(result)
            .expectNext(5L)
            .verifyComplete();
        
        verify(listOperations).size("dead_letter_queue");
    }
    
    @Test
    void shouldReprocessDeadLetterMessage() {
        // Given
        String deadLetterId = "DL_123";
        
        // When
        Mono<Boolean> result = deadLetterHandler.reprocessDeadLetterMessage(deadLetterId);
        
        // Then
        StepVerifier.create(result)
            .expectNext(false) // Returns false because getDeadLetterMessageById returns empty
            .verifyComplete();
    }
    
    @Test
    void shouldHandleSerializationError() throws Exception {
        // Given
        String payload = "test message";
        Message<String> failedMessage = new GenericMessage<>(payload);
        
        when(objectMapper.writeValueAsString(any())).thenThrow(new RuntimeException("Serialization failed"));
        
        // When
        deadLetterHandler.handleDeadLetter(failedMessage);
        
        // Then
        verify(mockCounter).increment();
        verify(objectMapper).writeValueAsString(any(DeadLetterHandler.DeadLetterRecord.class));
        // Should not call Redis operations when serialization fails
        verify(listOperations, never()).leftPush(anyString(), anyString());
    }
    
    @Test
    void shouldHandleDeserializationError() throws Exception {
        // Given
        String invalidJson = "invalid json";
        
        when(listOperations.rightPop(anyString())).thenReturn(Mono.just(invalidJson));
        when(objectMapper.readValue(anyString(), eq(DeadLetterHandler.DeadLetterRecord.class)))
            .thenThrow(new RuntimeException("Deserialization failed"));
        
        // When
        Mono<DeadLetterHandler.DeadLetterRecord> result = deadLetterHandler.getNextDeadLetterMessage();
        
        // Then
        StepVerifier.create(result)
            .verifyComplete(); // Should complete empty when deserialization fails
    }
    
    @Test
    void shouldHandleRedisError() throws Exception {
        // Given
        String payload = "test message";
        Message<String> failedMessage = new GenericMessage<>(payload);
        
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"id\":\"test\"}");
        when(listOperations.leftPush(anyString(), anyString())).thenReturn(Mono.error(new RuntimeException("Redis error")));
        when(hashOperations.putAll(anyString(), anyMap())).thenReturn(Mono.just(true));
        
        // When
        deadLetterHandler.handleDeadLetter(failedMessage);
        
        // Then
        verify(mockCounter).increment();
        // Should handle Redis errors gracefully
    }
    
    @Test
    void shouldCreateDeadLetterRecordWithAllFields() {
        // Given
        String payload = "test message";
        Map<String, Object> headers = new HashMap<>();
        headers.put("messageId", "msg-123");
        headers.put("timestamp", System.currentTimeMillis());
        headers.put("errorCause", new RuntimeException("Test error"));
        
        Message<String> failedMessage = new GenericMessage<>(payload, headers);
        
        // When
        deadLetterHandler.handleDeadLetter(failedMessage);
        
        // Then
        verify(mockCounter).increment();
        // Verify that the record creation doesn't throw exceptions
    }
}