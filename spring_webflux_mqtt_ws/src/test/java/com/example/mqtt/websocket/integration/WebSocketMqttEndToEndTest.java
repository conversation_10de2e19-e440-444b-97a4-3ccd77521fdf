package com.example.mqtt.websocket.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.socket.*;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebSocket 和 MQTT 端到端集成测试
 * 测试完整的消息流转链路
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.websocket.endpoint=/ws",
    "app.websocket.allowed-origins=*",
    "logging.level.com.example.mqtt.websocket=DEBUG"
})
class WebSocketMqttEndToEndTest {
    
    @LocalServerPort
    private int port;
    
    private WebSocketSession webSocketSession;
    private ObjectMapper objectMapper;
    private CountDownLatch messageLatch;
    private AtomicReference<String> receivedMessage;
    
    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        messageLatch = new CountDownLatch(1);
        receivedMessage = new AtomicReference<>();
        
        // 建立 WebSocket 连接
        WebSocketClient client = new org.springframework.web.socket.client.standard.StandardWebSocketClient();
        URI uri = URI.create("ws://localhost:" + port + "/ws?userId=testUser123");
        
        WebSocketHandler handler = new WebSocketHandler() {
            @Override
            public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                System.out.println("WebSocket connection established: " + session.getId());
            }
            
            @Override
            public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                String payload = message.getPayload().toString();
                System.out.println("Received WebSocket message: " + payload);
                receivedMessage.set(payload);
                messageLatch.countDown();
            }
            
            @Override
            public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                System.err.println("WebSocket transport error: " + exception.getMessage());
                exception.printStackTrace();
            }
            
            @Override
            public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
                System.out.println("WebSocket connection closed: " + closeStatus);
            }
            
            @Override
            public boolean supportsPartialMessages() {
                return false;
            }
        };
        
        webSocketSession = client.doHandshake(handler, null, uri).get(5, TimeUnit.SECONDS);
        
        // 等待连接确认消息
        assertTrue(messageLatch.await(5, TimeUnit.SECONDS), "Connection confirmation not received");
        
        // 验证连接确认消息
        String confirmationMessage = receivedMessage.get();
        assertNotNull(confirmationMessage);
        JsonNode confirmationJson = objectMapper.readTree(confirmationMessage);
        assertEquals("connection_confirmed", confirmationJson.get("type").asText());
        
        // 重置计数器用于后续测试
        messageLatch = new CountDownLatch(1);
        receivedMessage.set(null);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (webSocketSession != null && webSocketSession.isOpen()) {
            webSocketSession.close();
        }
    }
    
    @Test
    void testWebSocketToMqttMessageFlow() throws Exception {
        // 发送 WebSocket 消息，期望转发到 MQTT
        String testMessage = "{\n" +
            "  \"type\": \"device_command\",\n" +
            "  \"content\": \"{\\\"action\\\":\\\"turn_on\\\",\\\"deviceId\\\":\\\"device-123\\\"}\",\n" +
            "  \"headers\": {\n" +
            "    \"mqtt.targetTopic\": \"device/command/device-123\"\n" +
            "  }\n" +
            "}";
        
        // 发送消息
        webSocketSession.sendMessage(new TextMessage(testMessage));
        
        // 由于这是单向测试（WebSocket -> MQTT），我们主要验证消息发送成功
        // 在实际环境中，可以通过 MQTT 客户端订阅相应主题来验证消息是否到达
        Thread.sleep(1000); // 给消息处理一些时间
        
        assertTrue(webSocketSession.isOpen(), "WebSocket session should remain open");
    }
    
    @Test
    void testMqttToWebSocketMessageFlow() throws Exception {
        // 这个测试需要模拟 MQTT 消息到达
        // 由于测试环境限制，我们通过直接调用消息处理逻辑来模拟
        
        // 模拟从 MQTT 接收到的消息（通常由 MQTT 适配器触发）
        String mqttPayload = "{\n" +
            "  \"id\": \"msg-456\",\n" +
            "  \"timestamp\": " + System.currentTimeMillis() + ",\n" +
            "  \"type\": \"sensor_data\",\n" +
            "  \"source\": \"mqtt\",\n" +
            "  \"payload\": \"eyJ0ZW1wZXJhdHVyZSI6MjUuNSwiaHVtaWRpdHkiOjYwLjB9\"\n" +
            "}";
        
        // 注意：在实际测试中，这个消息应该通过 MQTT 代理发送
        // 这里我们直接验证 WebSocket 连接的稳定性
        assertTrue(webSocketSession.isOpen(), "WebSocket session should be open for receiving MQTT messages");
        
        // 等待可能的消息接收
        Thread.sleep(2000);
    }
    
    @Test
    void testBidirectionalMessageExchange() throws Exception {
        // 测试双向消息交换
        
        // 1. 发送 WebSocket 消息
        String outgoingMessage = "{\n" +
            "  \"type\": \"ping\",\n" +
            "  \"content\": \"Hello from WebSocket\",\n" +
            "  \"timestamp\": " + System.currentTimeMillis() + "\n" +
            "}";
        
        webSocketSession.sendMessage(new TextMessage(outgoingMessage));
        
        // 2. 等待可能的响应或确认
        Thread.sleep(1000);
        
        // 3. 验证连接状态
        assertTrue(webSocketSession.isOpen(), "WebSocket session should remain open after message exchange");
    }
    
    @Test
    void testMessageValidationAndErrorHandling() throws Exception {
        // 测试无效消息的处理
        
        // 发送无效 JSON
        String invalidMessage = "invalid json format";
        webSocketSession.sendMessage(new TextMessage(invalidMessage));
        
        // 等待错误响应
        boolean errorReceived = messageLatch.await(3, TimeUnit.SECONDS);
        
        if (errorReceived) {
            String errorResponse = receivedMessage.get();
            assertNotNull(errorResponse);
            
            JsonNode errorJson = objectMapper.readTree(errorResponse);
            assertEquals("error", errorJson.get("type").asText());
            assertTrue(errorJson.get("error").asText().contains("Invalid message format"));
        }
        
        // 验证连接仍然活跃
        assertTrue(webSocketSession.isOpen(), "WebSocket session should remain open after error");
    }
    
    @Test
    void testConcurrentMessageHandling() throws Exception {
        // 测试并发消息处理
        int messageCount = 5;
        CountDownLatch sendLatch = new CountDownLatch(messageCount);
        
        // 并发发送多个消息
        for (int i = 0; i < messageCount; i++) {
            final int messageIndex = i;
            
            new Thread(() -> {
                try {
                    String message = String.format(
                        "{\"type\":\"concurrent_test\",\"content\":\"Message %d\",\"index\":%d}", 
                        messageIndex, messageIndex
                    );
                    
                    webSocketSession.sendMessage(new TextMessage(message));
                    sendLatch.countDown();
                    
                } catch (Exception e) {
                    e.printStackTrace();
                    sendLatch.countDown();
                }
            }).start();
        }
        
        // 等待所有消息发送完成
        assertTrue(sendLatch.await(5, TimeUnit.SECONDS), "Not all messages were sent");
        
        // 给消息处理一些时间
        Thread.sleep(2000);
        
        // 验证连接状态
        assertTrue(webSocketSession.isOpen(), "WebSocket session should handle concurrent messages");
    }
    
    @Test
    void testLargeMessageHandling() throws Exception {
        // 测试大消息处理
        StringBuilder largeContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            largeContent.append("This is a large message content part ").append(i).append(". ");
        }
        
        String largeMessage = String.format(
            "{\"type\":\"large_message\",\"content\":\"%s\"}", 
            largeContent.toString().replace("\"", "\\\"")
        );
        
        // 发送大消息
        webSocketSession.sendMessage(new TextMessage(largeMessage));
        
        // 等待处理完成
        Thread.sleep(2000);
        
        // 验证连接状态
        assertTrue(webSocketSession.isOpen(), "WebSocket session should handle large messages");
    }
    
    @Test
    void testConnectionRecovery() throws Exception {
        // 测试连接恢复（模拟网络中断）
        String sessionId = webSocketSession.getId();
        assertNotNull(sessionId);
        
        // 发送消息确认连接正常
        String testMessage = "{\"type\":\"connection_test\",\"content\":\"Testing connection\"}";
        webSocketSession.sendMessage(new TextMessage(testMessage));
        
        Thread.sleep(1000);
        
        // 验证连接状态
        assertTrue(webSocketSession.isOpen(), "WebSocket connection should be stable");
        
        // 在实际场景中，这里可以测试连接断开和重连逻辑
        // 由于测试环境限制，我们主要验证连接的稳定性
    }
}