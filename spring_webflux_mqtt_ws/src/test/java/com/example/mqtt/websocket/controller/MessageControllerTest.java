package com.example.mqtt.websocket.controller;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.MqttMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import com.example.mqtt.websocket.service.MessageBridgeService;
import com.example.mqtt.websocket.service.MessageConverter;
import com.example.mqtt.websocket.service.MqttPublishService;
import com.example.mqtt.websocket.service.WebSocketSessionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * MessageController 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class MessageControllerTest {
    
    @Mock
    private MessageBridgeService messageBridgeService;
    
    @Mock
    private MessageConverter messageConverter;
    
    @Mock
    private MqttPublishService mqttPublishService;
    
    @Mock
    private WebSocketSessionManager sessionManager;
    
    private WebTestClient webTestClient;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        MessageController controller = new MessageController(
            messageBridgeService, messageConverter, mqttPublishService, sessionManager);
        
        webTestClient = WebTestClient.bindToController(controller).build();
        objectMapper = new ObjectMapper();
    }
    
    @Test
    void shouldSendMqttMessageSuccessfully() throws Exception {
        // Given
        MessageController.MqttSendRequest request = new MessageController.MqttSendRequest();
        request.setTopic("test/topic");
        request.setMessageType("test");
        request.setPayload("test payload");
        request.setQos(1);
        request.setRetained(false);
        request.setHeaders(Map.of("key", "value"));
        
        when(mqttPublishService.publishMessage(any(MqttMessage.class)))
            .thenReturn(Mono.empty());
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/mqtt/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.message").isEqualTo("Message sent successfully")
            .jsonPath("$.success").isEqualTo(true);
    }
    
    @Test
    void shouldReturnErrorWhenMqttSendFails() throws Exception {
        // Given
        MessageController.MqttSendRequest request = new MessageController.MqttSendRequest();
        request.setTopic("test/topic");
        request.setMessageType("test");
        request.setPayload("test payload");
        
        when(mqttPublishService.publishMessage(any(MqttMessage.class)))
            .thenReturn(Mono.error(new RuntimeException("MQTT connection failed")));
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/mqtt/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().is5xxServerError()
            .expectBody()
            .jsonPath("$.success").isEqualTo(false)
            .jsonPath("$.message").value(org.hamcrest.Matchers.containsString("Failed to send message"));
    }
    
    @Test
    void shouldSendWebSocketBroadcastMessageSuccessfully() throws Exception {
        // Given
        MessageController.WebSocketSendRequest request = new MessageController.WebSocketSendRequest();
        request.setMessageType("broadcast");
        request.setPayload("broadcast message");
        request.setHeaders(Map.of("priority", "high"));
        
        when(sessionManager.broadcastMessage(any(WebSocketMessage.class)))
            .thenReturn(Mono.empty());
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/websocket/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.message").isEqualTo("Message sent successfully")
            .jsonPath("$.success").isEqualTo(true);
    }
    
    @Test
    void shouldSendWebSocketUnicastMessageSuccessfully() throws Exception {
        // Given
        MessageController.WebSocketSendRequest request = new MessageController.WebSocketSendRequest();
        request.setMessageType("unicast");
        request.setPayload("private message");
        request.setTargetUserId("user123");
        
        when(sessionManager.sendToUser(any(String.class), any(WebSocketMessage.class)))
            .thenReturn(Mono.empty());
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/websocket/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.message").isEqualTo("Message sent successfully")
            .jsonPath("$.success").isEqualTo(true);
    }
    
    @Test
    void shouldReturnValidationErrorForInvalidMqttRequest() {
        // Given
        MessageController.MqttSendRequest request = new MessageController.MqttSendRequest();
        // Missing required fields
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/mqtt/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isBadRequest();
    }
    
    @Test
    void shouldReturnValidationErrorForInvalidWebSocketRequest() {
        // Given
        MessageController.WebSocketSendRequest request = new MessageController.WebSocketSendRequest();
        // Missing required fields
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/websocket/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isBadRequest();
    }
    
    @Test
    void shouldReturnMessageStreamSuccessfully() {
        // When & Then
        webTestClient.get()
            .uri("/api/messages/stream")
            .accept(MediaType.TEXT_EVENT_STREAM)
            .exchange()
            .expectStatus().isOk()
            .expectHeader().contentType(MediaType.TEXT_EVENT_STREAM)
            .expectBodyList(BaseMessage.class)
            .hasSize(0); // 由于是流式响应，这里不会立即有数据
    }
    
    @Test
    void shouldReturnMessageStreamWithFilters() {
        // When & Then
        webTestClient.get()
            .uri(uriBuilder -> uriBuilder
                .path("/api/messages/stream")
                .queryParam("messageType", "sensor")
                .queryParam("source", "device")
                .build())
            .accept(MediaType.TEXT_EVENT_STREAM)
            .exchange()
            .expectStatus().isOk()
            .expectHeader().contentType(MediaType.TEXT_EVENT_STREAM);
    }
    
    @Test
    void shouldReturnMessageHistorySuccessfully() {
        // When & Then
        webTestClient.get()
            .uri("/api/messages/history")
            .exchange()
            .expectStatus().isOk()
            .expectBodyList(BaseMessage.class)
            .hasSize(10); // 默认限制为 10
    }
    
    @Test
    void shouldReturnMessageHistoryWithParameters() {
        // When & Then
        webTestClient.get()
            .uri(uriBuilder -> uriBuilder
                .path("/api/messages/history")
                .queryParam("messageType", "sensor")
                .queryParam("limit", "5")
                .queryParam("offset", "10")
                .build())
            .exchange()
            .expectStatus().isOk()
            .expectBodyList(BaseMessage.class)
            .hasSize(5);
    }
    
    @Test
    void shouldReturnMessageStatsSuccessfully() {
        // Given
        when(sessionManager.getActiveSessionCount()).thenReturn(5);
        
        // When & Then
        webTestClient.get()
            .uri("/api/messages/stats")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.totalMessages").isEqualTo(1000)
            .jsonPath("$.mqttMessages").isEqualTo(600)
            .jsonPath("$.webSocketMessages").isEqualTo(400)
            .jsonPath("$.activeConnections").isEqualTo(5)
            .jsonPath("$.errorCount").isEqualTo(5);
    }
    
    @Test
    void shouldReturnHealthCheckSuccessfully() {
        // Given
        when(sessionManager.getActiveSessionCount()).thenReturn(3);
        
        // When & Then
        webTestClient.get()
            .uri("/api/messages/health")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.status").isEqualTo("UP")
            .jsonPath("$.activeWebSocketSessions").isEqualTo(3)
            .jsonPath("$.mqttConnected").isEqualTo(true)
            .jsonPath("$.timestamp").isNumber();
    }
}