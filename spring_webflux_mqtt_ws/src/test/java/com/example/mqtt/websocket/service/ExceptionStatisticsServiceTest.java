package com.example.mqtt.websocket.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import reactor.core.publisher.Mono;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ExceptionStatisticsService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class ExceptionStatisticsServiceTest {
    
    @Mock
    private ReactiveRedisTemplate<String, Object> redisTemplate;
    
    @Mock
    private MeterRegistry meterRegistry;
    
    @Mock
    private AlertService alertService;
    
    @Mock
    private Counter mockCounter;
    
    @Mock
    private Gauge.Builder<Object> gaugeBuilder;
    
    @Mock
    private Timer.Builder timerBuilder;
    
    @Mock
    private Timer mockTimer;
    
    @Mock
    private Timer.Sample mockSample;
    
    @InjectMocks
    private ExceptionStatisticsService statisticsService;
    
    @BeforeEach
    void setUp() {
        when(meterRegistry.counter(anyString())).thenReturn(mockCounter);
        when(meterRegistry.counter(anyString(), anyString(), anyString())).thenReturn(mockCounter);
        
        // Mock Gauge builder
        when(Gauge.builder(anyString())).thenReturn(gaugeBuilder);
        when(gaugeBuilder.description(anyString())).thenReturn(gaugeBuilder);
        when(gaugeBuilder.register(any(MeterRegistry.class), any(), any())).thenReturn(mock(Gauge.class));
        
        // Mock Timer builder
        when(Timer.builder(anyString())).thenReturn(timerBuilder);
        when(timerBuilder.description(anyString())).thenReturn(timerBuilder);
        when(timerBuilder.register(any(MeterRegistry.class))).thenReturn(mockTimer);
        
        // Mock Timer.start
        when(Timer.start(any(MeterRegistry.class))).thenReturn(mockSample);
        
        statisticsService.initMetrics();
    }
    
    @Test
    void shouldRecordException() {
        // Given
        String exceptionType = "TestException";
        String message = "Test error message";
        RuntimeException throwable = new RuntimeException(message);
        
        when(redisTemplate.opsForHash()).thenReturn(mock(org.springframework.data.redis.core.ReactiveHashOperations.class));
        when(redisTemplate.opsForHash().putAll(anyString(), anyMap())).thenReturn(Mono.just(true));
        
        // When
        statisticsService.recordException(exceptionType, message, throwable);
        
        // Then
        verify(mockCounter).increment();
        verify(mockSample).stop(mockTimer);
    }
    
    @Test
    void shouldGetExceptionStatistics() {
        // Given
        statisticsService.recordException("TestException1", "Error 1", new RuntimeException());
        statisticsService.recordException("TestException2", "Error 2", new RuntimeException());
        statisticsService.recordException("TestException1", "Error 3", new RuntimeException());
        
        // When
        ExceptionStatisticsService.ExceptionStatistics stats = statisticsService.getExceptionStatistics();
        
        // Then
        assertNotNull(stats);
        assertTrue(stats.getTotalExceptions() >= 0);
        assertNotNull(stats.getExceptionsByType());
    }
    
    @Test
    void shouldResetStatistics() {
        // Given
        when(redisTemplate.delete(anyString())).thenReturn(Mono.just(1L));
        
        statisticsService.recordException("TestException", "Error", new RuntimeException());
        
        // When
        statisticsService.resetStatistics();
        
        // Then
        ExceptionStatisticsService.ExceptionStatistics stats = statisticsService.getExceptionStatistics();
        assertEquals(0, stats.getTotalExceptions());
        assertEquals(0, stats.getExceptionsInLastMinute());
        assertEquals(0, stats.getExceptionsInLastHour());
        assertTrue(stats.getExceptionsByType().isEmpty());
    }
    
    @Test
    void shouldResetMinuteCounter() {
        // Given
        statisticsService.recordException("TestException", "Error", new RuntimeException());
        
        // When
        statisticsService.resetMinuteCounter();
        
        // Then
        ExceptionStatisticsService.ExceptionStatistics stats = statisticsService.getExceptionStatistics();
        assertEquals(0, stats.getExceptionsInLastMinute());
    }
    
    @Test
    void shouldResetHourCounter() {
        // Given
        statisticsService.recordException("TestException", "Error", new RuntimeException());
        
        // When
        statisticsService.resetHourCounter();
        
        // Then
        ExceptionStatisticsService.ExceptionStatistics stats = statisticsService.getExceptionStatistics();
        assertEquals(0, stats.getExceptionsInLastHour());
    }
    
    @Test
    void shouldGenerateExceptionReport() {
        // Given
        // Record some exceptions to trigger report generation
        for (int i = 0; i < 5; i++) {
            statisticsService.recordException("TestException", "Error " + i, new RuntimeException());
        }
        
        // When
        statisticsService.generateExceptionReport();
        
        // Then
        // Verify that the method executes without throwing exceptions
        // In a real scenario, this would check log output or alert service calls
    }
    
    @Test
    void shouldTriggerAlertOnHighExceptionCount() {
        // Given
        String exceptionType = "TestException";
        
        // When - Record 10 exceptions to trigger threshold
        for (int i = 0; i < 10; i++) {
            statisticsService.recordException(exceptionType, "Error " + i, new RuntimeException());
        }
        
        // Then
        verify(alertService, atLeastOnce()).sendAlert(
            eq(AlertService.AlertLevel.HIGH),
            eq("Exception Threshold Exceeded"),
            contains("Exception type TestException has occurred 10 times"),
            eq("EXCEPTION_THRESHOLD")
        );
    }
    
    @Test
    void shouldTriggerAlertOnTotalExceptionThreshold() {
        // Given
        // When - Record 50 exceptions to trigger total threshold
        for (int i = 0; i < 50; i++) {
            statisticsService.recordException("TestException" + (i % 5), "Error " + i, new RuntimeException());
        }
        
        // Then
        verify(alertService, atLeastOnce()).sendAlert(
            eq(AlertService.AlertLevel.CRITICAL),
            eq("Total Exception Threshold Exceeded"),
            contains("Total exceptions have reached 50"),
            eq("TOTAL_EXCEPTION_THRESHOLD")
        );
    }
    
    @Test
    void shouldHandleRedisError() {
        // Given
        when(redisTemplate.opsForHash()).thenReturn(mock(org.springframework.data.redis.core.ReactiveHashOperations.class));
        when(redisTemplate.opsForHash().putAll(anyString(), anyMap())).thenReturn(Mono.error(new RuntimeException("Redis error")));
        
        // When & Then
        assertDoesNotThrow(() -> {
            statisticsService.recordException("TestException", "Error", new RuntimeException());
        });
    }
    
    @Test
    void shouldCreateExceptionStatisticsWithAllFields() {
        // When
        ExceptionStatisticsService.ExceptionStatistics stats = 
            new ExceptionStatisticsService.ExceptionStatistics(10, 5, 8, java.util.Map.of("TestException", 3L));
        
        // Then
        assertEquals(10, stats.getTotalExceptions());
        assertEquals(5, stats.getExceptionsInLastMinute());
        assertEquals(8, stats.getExceptionsInLastHour());
        assertEquals(1, stats.getExceptionsByType().size());
        assertEquals(3L, stats.getExceptionsByType().get("TestException"));
    }
    
    @Test
    void shouldHandleNullThrowable() {
        // Given
        String exceptionType = "TestException";
        String message = "Test error message";
        
        when(redisTemplate.opsForHash()).thenReturn(mock(org.springframework.data.redis.core.ReactiveHashOperations.class));
        when(redisTemplate.opsForHash().putAll(anyString(), anyMap())).thenReturn(Mono.just(true));
        
        // When & Then
        assertDoesNotThrow(() -> {
            statisticsService.recordException(exceptionType, message, null);
        });
        
        verify(mockCounter).increment();
    }
}