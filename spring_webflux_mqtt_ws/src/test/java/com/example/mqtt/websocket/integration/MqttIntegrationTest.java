package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.config.properties.MqttProperties;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.messaging.MessageChannel;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * MQTT 集成测试类
 * 测试 MQTT 配置的完整集成
 */
@SpringBootTest
@TestPropertySource(properties = {
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.mqtt.client-id=test-client",
    "app.mqtt.username=testuser",
    "app.mqtt.password=testpass",
    "app.mqtt.connection-timeout=30",
    "app.mqtt.keep-alive-interval=60",
    "app.mqtt.clean-session=true",
    "app.mqtt.automatic-reconnect=true",
    "app.mqtt.subscribe-topics[0]=test/topic1",
    "app.mqtt.subscribe-topics[1]=test/topic2"
})
class MqttIntegrationTest {

    @Autowired
    private MqttProperties mqttProperties;

    @Autowired
    private MqttPahoClientFactory mqttClientFactory;

    @Autowired
    private MessageChannel mqttInputChannel;

    @Autowired
    private MessageChannel mqttOutboundChannel;

    @Autowired
    private MessageChannel mqttErrorChannel;

    @Autowired
    private MqttGateway mqttGateway;

    @Autowired
    private MqttMessageHandler mqttMessageHandler;

    @Autowired
    private MqttExceptionHandler mqttExceptionHandler;

    @Test
    void shouldLoadMqttProperties() {
        assertThat(mqttProperties).isNotNull();
        assertThat(mqttProperties.getBrokerUrl()).isEqualTo("tcp://localhost:1883");
        assertThat(mqttProperties.getClientId()).isEqualTo("test-client");
        assertThat(mqttProperties.getUsername()).isEqualTo("testuser");
        assertThat(mqttProperties.getPassword()).isEqualTo("testpass");
        assertThat(mqttProperties.getConnectionTimeout()).isEqualTo(30);
        assertThat(mqttProperties.getKeepAliveInterval()).isEqualTo(60);
        assertThat(mqttProperties.isCleanSession()).isTrue();
        assertThat(mqttProperties.isAutomaticReconnect()).isTrue();
        assertThat(mqttProperties.getSubscribeTopics()).containsExactly("test/topic1", "test/topic2");
    }

    @Test
    void shouldCreateMqttClientFactory() {
        assertThat(mqttClientFactory).isNotNull();
    }

    @Test
    void shouldCreateMessageChannels() {
        assertThat(mqttInputChannel).isNotNull();
        assertThat(mqttOutboundChannel).isNotNull();
        assertThat(mqttErrorChannel).isNotNull();
    }

    @Test
    void shouldCreateMqttGateway() {
        assertThat(mqttGateway).isNotNull();
    }

    @Test
    void shouldCreateMqttMessageHandler() {
        assertThat(mqttMessageHandler).isNotNull();
    }

    @Test
    void shouldCreateMqttExceptionHandler() {
        assertThat(mqttExceptionHandler).isNotNull();
    }

    @Test
    void shouldHaveCorrectChannelConfiguration() {
        // 验证通道配置是否正确
        assertThat(mqttInputChannel).isNotNull();
        assertThat(mqttOutboundChannel).isNotNull();
        assertThat(mqttErrorChannel).isNotNull();
    }
}