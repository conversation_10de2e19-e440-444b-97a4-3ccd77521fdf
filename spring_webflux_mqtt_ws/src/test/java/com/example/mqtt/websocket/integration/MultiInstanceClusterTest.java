package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.socket.*;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 多实例集群部署测试
 * 测试集群环境下的负载均衡、故障转移和数据一致性
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.cluster.enabled=true",
    "app.cluster.instance-id=test-instance-1",
    "app.cluster.health-check-interval=5000",
    "app.cluster.failure-detection-timeout=10000",
    "app.websocket.endpoint=/ws",
    "logging.level.com.example.mqtt.websocket=DEBUG"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class MultiInstanceClusterTest {
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private ClusterStateManager clusterStateManager;
    
    @Autowired
    private LoadBalancer loadBalancer;
    
    private ObjectMapper objectMapper;
    private List<WebSocketSession> webSocketSessions;
    private ExecutorService executorService;
    
    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        webSocketSessions = new ArrayList<>();
        executorService = Executors.newFixedThreadPool(10);
        
        // 注册当前实例到集群
        ClusterStateManager.InstanceInfo instanceInfo = createTestInstanceInfo("test-instance-1");
        clusterStateManager.registerInstance("test-instance-1", instanceInfo).block();
        
        // 等待集群状态稳定
        Thread.sleep(2000);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        // 关闭所有 WebSocket 连接
        for (WebSocketSession session : webSocketSessions) {
            if (session.isOpen()) {
                session.close();
            }
        }
        webSocketSessions.clear();
        
        if (executorService != null) {
            executorService.shutdown();
            executorService.awaitTermination(5, TimeUnit.SECONDS);
        }
    }
    
    @Test
    @Order(1)
    void testClusterInstanceRegistration() throws Exception {
        System.out.println("Testing cluster instance registration");
        
        // 验证当前实例已注册
        Set<String> activeInstances = clusterStateManager.getActiveInstances();
        assertFalse(activeInstances.isEmpty(), "At least one instance should be active");
        assertTrue(activeInstances.contains("test-instance-1"), "Current instance should be registered");
        
        // 模拟注册额外实例
        clusterStateManager.registerInstance("test-instance-2", createTestInstanceInfo("test-instance-2")).block();
        clusterStateManager.registerInstance("test-instance-3", createTestInstanceInfo("test-instance-3")).block();
        
        Thread.sleep(1000);
        
        // 验证多实例注册
        Set<String> updatedInstances = clusterStateManager.getActiveInstances();
        assertTrue(updatedInstances.size() >= 3, "Multiple instances should be registered");
        assertTrue(updatedInstances.contains("test-instance-2"), "Instance 2 should be registered");
        assertTrue(updatedInstances.contains("test-instance-3"), "Instance 3 should be registered");
        
        System.out.println("Active instances: " + updatedInstances);
        System.out.println("Cluster instance registration test completed successfully");
    }
    
    @Test
    @Order(2)
    void testLoadBalancerDistribution() throws Exception {
        System.out.println("Testing load balancer distribution");
        
        // 确保有多个实例可用
        clusterStateManager.registerInstance("test-instance-2", createTestInstanceInfo("test-instance-2")).block();
        clusterStateManager.registerInstance("test-instance-3", createTestInstanceInfo("test-instance-3")).block();
        Thread.sleep(1000);
        
        // 测试负载均衡分发
        Map<String, Integer> instanceCounts = new HashMap<>();
        int totalRequests = 100;
        
        for (int i = 0; i < totalRequests; i++) {
            String selectedInstance = loadBalancer.selectInstance("request-" + i);
            assertNotNull(selectedInstance, "Load balancer should select an instance");
            
            instanceCounts.merge(selectedInstance, 1, Integer::sum);
        }
        
        System.out.println("Load distribution: " + instanceCounts);
        
        // 验证负载分布
        assertFalse(instanceCounts.isEmpty(), "Requests should be distributed");
        assertTrue(instanceCounts.size() > 1, "Requests should be distributed across multiple instances");
        
        // 验证分布相对均匀（允许一定偏差）
        int expectedPerInstance = totalRequests / instanceCounts.size();
        for (Map.Entry<String, Integer> entry : instanceCounts.entrySet()) {
            int count = entry.getValue();
            assertTrue(count > 0, "Each instance should handle at least one request");
            assertTrue(Math.abs(count - expectedPerInstance) <= expectedPerInstance * 0.5, 
                "Load should be relatively balanced");
        }
        
        System.out.println("Load balancer distribution test completed successfully");
    }
    
    @Test
    @Order(3)
    void testFailoverMechanism() throws Exception {
        System.out.println("Testing failover mechanism");
        
        // 注册多个实例
        clusterStateManager.registerInstance("test-instance-2");
        clusterStateManager.registerInstance("test-instance-3");
        Thread.sleep(1000);
        
        Set<String> initialInstances = clusterStateManager.getActiveInstances();
        assertTrue(initialInstances.size() >= 3, "Should have multiple instances for failover test");
        
        // 选择一个实例进行故障模拟
        String failingInstance = initialInstances.iterator().next();
        System.out.println("Simulating failure of instance: " + failingInstance);
        
        // 更新实例健康状态为不健康
        loadBalancer.updateInstanceHealth(failingInstance, false);
        
        // 等待故障检测
        Thread.sleep(3000);
        
        // 测试负载均衡器是否避开故障实例
        Set<String> selectedInstances = new HashSet<>();
        for (int i = 0; i < 20; i++) {
            String selected = loadBalancer.selectInstance("failover-test-" + i);
            selectedInstances.add(selected);
        }
        
        // 验证故障实例不再被选择
        assertFalse(selectedInstances.contains(failingInstance), 
            "Failed instance should not be selected by load balancer");
        
        // 模拟实例恢复
        System.out.println("Simulating recovery of instance: " + failingInstance);
        loadBalancer.updateInstanceHealth(failingInstance, true);
        
        Thread.sleep(2000);
        
        // 验证实例恢复后重新参与负载均衡
        selectedInstances.clear();
        for (int i = 0; i < 50; i++) {
            String selected = loadBalancer.selectInstance("recovery-test-" + i);
            selectedInstances.add(selected);
        }
        
        assertTrue(selectedInstances.contains(failingInstance), 
            "Recovered instance should be selected by load balancer");
        
        System.out.println("Failover mechanism test completed successfully");
    }
    
    @Test
    @Order(4)
    void testClusterMessageBroadcast() throws Exception {
        System.out.println("Testing cluster message broadcast");
        
        // 注册多个实例
        clusterStateManager.registerInstance("test-instance-2");
        clusterStateManager.registerInstance("test-instance-3");
        Thread.sleep(1000);
        
        // 准备广播消息
        Map<String, Object> broadcastMessage = Map.of(
            "type", "cluster_broadcast",
            "timestamp", System.currentTimeMillis(),
            "sender", "test-instance-1",
            "data", Map.of(
                "event", "configuration_update",
                "config", Map.of("maxConnections", 1000)
            )
        );
        
        // 执行集群广播
        clusterStateManager.broadcastToCluster(broadcastMessage);
        
        // 等待广播传播
        Thread.sleep(2000);
        
        // 验证广播成功（在实际实现中，这里会检查其他实例是否收到消息）
        // 由于测试环境限制，我们主要验证广播方法执行成功
        Set<String> activeInstances = clusterStateManager.getActiveInstances();
        assertTrue(activeInstances.size() >= 3, "Multiple instances should be active for broadcast");
        
        System.out.println("Cluster message broadcast test completed successfully");
    }
    
    @Test
    @Order(5)
    void testConcurrentClientConnections() throws Exception {
        System.out.println("Testing concurrent client connections across cluster");
        
        int connectionCount = 20;
        CountDownLatch connectionLatch = new CountDownLatch(connectionCount);
        AtomicInteger successfulConnections = new AtomicInteger(0);
        AtomicReference<Exception> connectionError = new AtomicReference<>();
        
        // 并发建立多个 WebSocket 连接
        for (int i = 0; i < connectionCount; i++) {
            final int clientId = i;
            
            executorService.submit(() -> {
                try {
                    WebSocketClient client = new StandardWebSocketClient();
                    URI uri = URI.create("ws://localhost:" + port + "/ws?userId=testUser" + clientId);
                    
                    WebSocketHandler handler = new WebSocketHandler() {
                        @Override
                        public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                            System.out.println("Client " + clientId + " connected: " + session.getId());
                            successfulConnections.incrementAndGet();
                            connectionLatch.countDown();
                        }
                        
                        @Override
                        public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {}
                        
                        @Override
                        public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                            connectionError.set(new Exception("Transport error for client " + clientId, exception));
                            connectionLatch.countDown();
                        }
                        
                        @Override
                        public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {}
                        
                        @Override
                        public boolean supportsPartialMessages() {
                            return false;
                        }
                    };
                    
                    WebSocketSession session = client.doHandshake(handler, null, uri).get(10, TimeUnit.SECONDS);
                    webSocketSessions.add(session);
                    
                } catch (Exception e) {
                    connectionError.set(e);
                    connectionLatch.countDown();
                }
            });
        }
        
        // 等待所有连接完成
        boolean allConnected = connectionLatch.await(30, TimeUnit.SECONDS);
        assertTrue(allConnected, "All connections should be established within timeout");
        
        if (connectionError.get() != null) {
            throw connectionError.get();
        }
        
        assertEquals(connectionCount, successfulConnections.get(), 
            "All connections should be successful");
        
        System.out.println("Successfully established " + successfulConnections.get() + " concurrent connections");
        System.out.println("Concurrent client connections test completed successfully");
    }
    
    @Test
    @Order(6)
    void testDataConsistencyAcrossInstances() throws Exception {
        System.out.println("Testing data consistency across instances");
        
        // 注册多个实例
        clusterStateManager.registerInstance("test-instance-2");
        clusterStateManager.registerInstance("test-instance-3");
        Thread.sleep(1000);
        
        // 建立多个 WebSocket 连接到不同实例（模拟）
        int sessionCount = 5;
        CountDownLatch messageLatch = new CountDownLatch(sessionCount);
        List<String> receivedMessages = Collections.synchronizedList(new ArrayList<>());
        
        for (int i = 0; i < sessionCount; i++) {
            final int sessionId = i;
            
            WebSocketClient client = new StandardWebSocketClient();
            URI uri = URI.create("ws://localhost:" + port + "/ws?userId=consistencyTestUser" + sessionId);
            
            WebSocketHandler handler = new WebSocketHandler() {
                @Override
                public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                    System.out.println("Consistency test session " + sessionId + " connected");
                }
                
                @Override
                public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                    String payload = message.getPayload().toString();
                    
                    // 只处理广播消息
                    try {
                        JsonNode messageJson = objectMapper.readTree(payload);
                        if ("broadcast_test".equals(messageJson.get("type").asText())) {
                            receivedMessages.add(payload);
                            messageLatch.countDown();
                            System.out.println("Session " + sessionId + " received broadcast message");
                        }
                    } catch (Exception e) {
                        // 忽略非 JSON 消息
                    }
                }
                
                @Override
                public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {}
                
                @Override
                public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {}
                
                @Override
                public boolean supportsPartialMessages() {
                    return false;
                }
            };
            
            WebSocketSession session = client.doHandshake(handler, null, uri).get(10, TimeUnit.SECONDS);
            webSocketSessions.add(session);
        }
        
        // 等待所有连接建立
        Thread.sleep(3000);
        
        // 发送广播消息测试数据一致性
        Map<String, Object> broadcastData = Map.of(
            "type", "broadcast_test",
            "timestamp", System.currentTimeMillis(),
            "message", "Data consistency test message",
            "sequenceNumber", 12345
        );
        
        clusterStateManager.broadcastToCluster(broadcastData);
        
        // 等待所有会话接收消息
        boolean allReceived = messageLatch.await(15, TimeUnit.SECONDS);
        assertTrue(allReceived, "All sessions should receive the broadcast message");
        
        // 验证消息一致性
        assertEquals(sessionCount, receivedMessages.size(), 
            "All sessions should receive the same message");
        
        // 验证所有接收到的消息内容一致
        String firstMessage = receivedMessages.get(0);
        for (String message : receivedMessages) {
            JsonNode firstJson = objectMapper.readTree(firstMessage);
            JsonNode currentJson = objectMapper.readTree(message);
            
            assertEquals(firstJson.get("type").asText(), currentJson.get("type").asText());
            assertEquals(firstJson.get("message").asText(), currentJson.get("message").asText());
            assertEquals(firstJson.get("sequenceNumber").asInt(), currentJson.get("sequenceNumber").asInt());
        }
        
        System.out.println("Data consistency test completed successfully");
    }
    
    @Test
    @Order(7)
    void testClusterHealthMonitoring() throws Exception {
        System.out.println("Testing cluster health monitoring");
        
        // 注册多个实例
        clusterStateManager.registerInstance("test-instance-2");
        clusterStateManager.registerInstance("test-instance-3");
        Thread.sleep(1000);
        
        Set<String> initialInstances = clusterStateManager.getActiveInstances();
        int initialCount = initialInstances.size();
        assertTrue(initialCount >= 3, "Should have multiple instances for health monitoring test");
        
        // 模拟实例健康状态变化
        String monitoredInstance = "test-instance-2";
        
        // 标记实例为不健康
        loadBalancer.updateInstanceHealth(monitoredInstance, false);
        Thread.sleep(2000);
        
        // 验证负载均衡器识别不健康实例
        Set<String> selectedInstances = new HashSet<>();
        for (int i = 0; i < 10; i++) {
            String selected = loadBalancer.selectInstance("health-test-" + i);
            selectedInstances.add(selected);
        }
        
        assertFalse(selectedInstances.contains(monitoredInstance), 
            "Unhealthy instance should not be selected");
        
        // 恢复实例健康状态
        loadBalancer.updateInstanceHealth(monitoredInstance, true);
        Thread.sleep(2000);
        
        // 验证实例恢复后重新可用
        selectedInstances.clear();
        for (int i = 0; i < 20; i++) {
            String selected = loadBalancer.selectInstance("recovery-health-test-" + i);
            selectedInstances.add(selected);
        }
        
        assertTrue(selectedInstances.contains(monitoredInstance), 
            "Recovered instance should be available for selection");
        
        System.out.println("Cluster health monitoring test completed successfully");
    }
    
    @Test
    @Order(8)
    void testClusterScalability() throws Exception {
        System.out.println("Testing cluster scalability");
        
        // 动态添加更多实例
        List<String> newInstances = Arrays.asList(
            "test-instance-4", "test-instance-5", "test-instance-6", 
            "test-instance-7", "test-instance-8"
        );
        
        for (String instanceId : newInstances) {
            clusterStateManager.registerInstance(instanceId, createTestInstanceInfo(instanceId)).block();
            Thread.sleep(500); // 模拟实例启动间隔
        }
        
        Thread.sleep(2000);
        
        // 验证所有实例都已注册
        Set<String> allInstances = clusterStateManager.getActiveInstances();
        assertTrue(allInstances.size() >= 8, "Should have at least 8 instances");
        
        for (String instanceId : newInstances) {
            assertTrue(allInstances.contains(instanceId), 
                "New instance " + instanceId + " should be registered");
        }
        
        // 测试扩展后的负载分布
        Map<String, Integer> distributionAfterScale = new HashMap<>();
        int testRequests = 200;
        
        for (int i = 0; i < testRequests; i++) {
            String selected = loadBalancer.selectInstance("scale-test-" + i);
            distributionAfterScale.merge(selected, 1, Integer::sum);
        }
        
        System.out.println("Load distribution after scaling: " + distributionAfterScale);
        
        // 验证负载在更多实例间分布
        assertTrue(distributionAfterScale.size() >= 5, 
            "Load should be distributed across multiple instances");
        
        // 验证没有实例过载
        int maxLoad = distributionAfterScale.values().stream().mapToInt(Integer::intValue).max().orElse(0);
        int expectedMaxLoad = testRequests / distributionAfterScale.size() * 2; // 允许2倍偏差
        assertTrue(maxLoad <= expectedMaxLoad, 
            "No single instance should be overloaded");
        
        System.out.println("Cluster scalability test completed successfully");
    }
    
    /**
     * 创建测试实例信息
     */
    private ClusterStateManager.InstanceInfo createTestInstanceInfo(String instanceId) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("startTime", System.currentTimeMillis());
        metadata.put("version", "1.0.0-test");
        
        return new ClusterStateManager.InstanceInfo(
            instanceId,
            "localhost",
            port + instanceId.hashCode() % 1000, // 模拟不同端口
            java.time.Instant.now(),
            java.time.Instant.now(),
            metadata
        );
    }
}