package com.example.mqtt.websocket.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.util.List;
import java.util.Map;

/**
 * ConfigurationController 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ConfigurationControllerTest {
    
    private WebTestClient webTestClient;
    private ConfigurationController controller;
    
    @BeforeEach
    void setUp() {
        controller = new ConfigurationController();
        
        // 设置测试用的配置值
        ReflectionTestUtils.setField(controller, "mqttBrokerUrl", "tcp://localhost:1883");
        ReflectionTestUtils.setField(controller, "mqttSubscribeTopics", 
            List.of("sensor/+/data", "device/+/status"));
        ReflectionTestUtils.setField(controller, "websocketEndpoint", "/ws");
        ReflectionTestUtils.setField(controller, "instanceId", "test-instance-123");
        
        webTestClient = WebTestClient.bindToController(controller).build();
    }
    
    @Test
    void shouldReturnMqttConfigSuccessfully() {
        // When & Then
        webTestClient.get()
            .uri("/api/config/mqtt")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.brokerUrl").isEqualTo("tcp://localhost:1883")
            .jsonPath("$.subscribeTopics").isArray()
            .jsonPath("$.subscribeTopics[0]").isEqualTo("sensor/+/data")
            .jsonPath("$.subscribeTopics[1]").isEqualTo("device/+/status")
            .jsonPath("$.connected").isEqualTo(true);
    }
    
    @Test
    void shouldReturnWebSocketConfigSuccessfully() {
        // When & Then
        webTestClient.get()
            .uri("/api/config/websocket")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.endpoint").isEqualTo("/ws")
            .jsonPath("$.activeConnections").isEqualTo(0);
    }
    
    @Test
    void shouldReturnClusterConfigSuccessfully() {
        // When & Then
        webTestClient.get()
            .uri("/api/config/cluster")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.instanceId").isEqualTo("test-instance-123")
            .jsonPath("$.activeInstances").isArray()
            .jsonPath("$.activeInstances[0]").isEqualTo("test-instance-123")
            .jsonPath("$.healthy").isEqualTo(true);
    }
    
    @Test
    void shouldReturnSystemInfoSuccessfully() {
        // When & Then
        webTestClient.get()
            .uri("/api/config/system")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.instanceId").isEqualTo("test-instance-123")
            .jsonPath("$.uptime").isNumber()
            .jsonPath("$.javaVersion").isNotEmpty()
            .jsonPath("$.springBootVersion").isEqualTo("2.7.18")
            .jsonPath("$.totalMemory").isNumber()
            .jsonPath("$.freeMemory").isNumber()
            .jsonPath("$.maxMemory").isNumber()
            .jsonPath("$.usedMemory").isNumber();
    }
    
    @Test
    void shouldUpdateMqttTopicsSuccessfully() {
        // Given
        ConfigurationController.UpdateTopicsRequest request = new ConfigurationController.UpdateTopicsRequest();
        request.setTopics(List.of("new/topic/1", "new/topic/2"));
        
        // When & Then
        webTestClient.put()
            .uri("/api/config/mqtt/topics")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.message").isEqualTo("Topics updated successfully")
            .jsonPath("$.topics").isArray()
            .jsonPath("$.topics[0]").isEqualTo("new/topic/1")
            .jsonPath("$.topics[1]").isEqualTo("new/topic/2")
            .jsonPath("$.timestamp").isNumber();
    }
    
    @Test
    void shouldReturnValidationErrorForEmptyTopicsList() {
        // Given
        ConfigurationController.UpdateTopicsRequest request = new ConfigurationController.UpdateTopicsRequest();
        request.setTopics(List.of()); // Empty list
        
        // When & Then
        webTestClient.put()
            .uri("/api/config/mqtt/topics")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isBadRequest();
    }
    
    @Test
    void shouldReturnValidationErrorForNullTopics() {
        // Given
        Map<String, Object> request = Map.of(); // Missing topics field
        
        // When & Then
        webTestClient.put()
            .uri("/api/config/mqtt/topics")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isBadRequest();
    }
    
    @Test
    void shouldReturnValidationErrorForBlankTopic() {
        // Given
        ConfigurationController.UpdateTopicsRequest request = new ConfigurationController.UpdateTopicsRequest();
        request.setTopics(List.of("valid/topic", "")); // Contains blank topic
        
        // When & Then
        webTestClient.put()
            .uri("/api/config/mqtt/topics")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isBadRequest();
    }
    
    @Test
    void shouldHandleInvalidJsonRequest() {
        // When & Then
        webTestClient.put()
            .uri("/api/config/mqtt/topics")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue("invalid json")
            .exchange()
            .expectStatus().isBadRequest();
    }
}