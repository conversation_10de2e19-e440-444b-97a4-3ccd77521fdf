package com.example.mqtt.websocket.controller;

import com.example.mqtt.websocket.service.MessageBridgeService;
import com.example.mqtt.websocket.service.MessageConverter;
import com.example.mqtt.websocket.service.MqttPublishService;
import com.example.mqtt.websocket.service.WebSocketSessionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * MessageController 集成测试
 * 
 * <AUTHOR>
 */
@WebFluxTest(MessageController.class)
class MessageControllerIntegrationTest {
    
    @Autowired
    private WebTestClient webTestClient;
    
    @MockBean
    private MessageBridgeService messageBridgeService;
    
    @MockBean
    private MessageConverter messageConverter;
    
    @MockBean
    private MqttPublishService mqttPublishService;
    
    @MockBean
    private WebSocketSessionManager sessionManager;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void shouldHandleCompleteMessageFlowSuccessfully() throws Exception {
        // Given
        MessageController.MqttSendRequest request = new MessageController.MqttSendRequest();
        request.setTopic("integration/test");
        request.setMessageType("integration");
        request.setPayload("integration test payload");
        request.setQos(2);
        request.setRetained(true);
        request.setHeaders(Map.of("test", "integration"));
        
        when(mqttPublishService.publishMessage(any()))
            .thenReturn(Mono.empty());
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/mqtt/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").isEqualTo(true)
            .jsonPath("$.message").isEqualTo("Message sent successfully");
    }
    
    @Test
    void shouldHandleWebSocketMessageFlowSuccessfully() throws Exception {
        // Given
        MessageController.WebSocketSendRequest request = new MessageController.WebSocketSendRequest();
        request.setMessageType("integration");
        request.setPayload("websocket integration test");
        request.setTargetUserId("integration-user");
        request.setHeaders(Map.of("integration", "test"));
        
        when(sessionManager.sendToUser(any(), any()))
            .thenReturn(Mono.empty());
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/websocket/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").isEqualTo(true)
            .jsonPath("$.message").isEqualTo("Message sent successfully");
    }
    
    @Test
    void shouldHandleMessageStreamEndpoint() {
        // When & Then
        webTestClient.get()
            .uri("/api/messages/stream?messageType=integration&source=test")
            .accept(MediaType.TEXT_EVENT_STREAM)
            .exchange()
            .expectStatus().isOk()
            .expectHeader().contentType(MediaType.TEXT_EVENT_STREAM);
    }
    
    @Test
    void shouldHandleMessageHistoryWithPagination() {
        // When & Then
        webTestClient.get()
            .uri("/api/messages/history?messageType=integration&limit=5&offset=10")
            .exchange()
            .expectStatus().isOk()
            .expectBodyList(Object.class)
            .hasSize(5);
    }
    
    @Test
    void shouldHandleStatsEndpoint() {
        // Given
        when(sessionManager.getActiveSessionCount()).thenReturn(10);
        
        // When & Then
        webTestClient.get()
            .uri("/api/messages/stats")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.totalMessages").isNumber()
            .jsonPath("$.activeConnections").isEqualTo(10);
    }
    
    @Test
    void shouldHandleHealthCheckEndpoint() {
        // Given
        when(sessionManager.getActiveSessionCount()).thenReturn(7);
        
        // When & Then
        webTestClient.get()
            .uri("/api/messages/health")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.status").isEqualTo("UP")
            .jsonPath("$.activeWebSocketSessions").isEqualTo(7);
    }
    
    @Test
    void shouldHandleServiceErrorGracefully() throws Exception {
        // Given
        MessageController.MqttSendRequest request = new MessageController.MqttSendRequest();
        request.setTopic("error/test");
        request.setMessageType("error");
        request.setPayload("error test payload");
        
        when(mqttPublishService.publishMessage(any()))
            .thenReturn(Mono.error(new RuntimeException("Service unavailable")));
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/mqtt/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().is5xxServerError()
            .expectBody()
            .jsonPath("$.success").isEqualTo(false)
            .jsonPath("$.message").value(org.hamcrest.Matchers.containsString("Failed to send message"));
    }
    
    @Test
    void shouldValidateRequestParameters() {
        // Given - Invalid request with missing required fields
        Map<String, Object> invalidRequest = Map.of(
            "topic", "",  // Empty topic
            "messageType", "",  // Empty message type
            "payload", ""  // Empty payload
        );
        
        // When & Then
        webTestClient.post()
            .uri("/api/messages/mqtt/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(invalidRequest)
            .exchange()
            .expectStatus().isBadRequest();
    }
    
    @Test
    void shouldHandleMalformedJsonRequest() {
        // When & Then
        webTestClient.post()
            .uri("/api/messages/mqtt/send")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue("{invalid json}")
            .exchange()
            .expectStatus().isBadRequest();
    }
    
    @Test
    void shouldHandleUnsupportedMediaType() {
        // When & Then
        webTestClient.post()
            .uri("/api/messages/mqtt/send")
            .contentType(MediaType.TEXT_PLAIN)
            .bodyValue("plain text")
            .exchange()
            .expectStatus().is4xxClientError();
    }
}