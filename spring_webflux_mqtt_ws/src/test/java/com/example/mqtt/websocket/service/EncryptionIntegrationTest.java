package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.config.properties.EncryptionProperties;
import com.example.mqtt.websocket.service.impl.AesEncryptionService;
import com.example.mqtt.websocket.service.impl.RedisKeyManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import javax.crypto.SecretKey;
import java.time.Duration;
import java.util.Arrays;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 加密服务集成测试，包括密钥轮换测试
 */
@SpringBootTest
@Testcontainers
class EncryptionIntegrationTest {
    
    @Container
    static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:7-alpine"))
            .withExposedPorts(6379);
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", redis::getFirstMappedPort);
    }
    
    private ReactiveRedisTemplate<String, String> redisTemplate;
    private EncryptionProperties encryptionProperties;
    private RedisKeyManager keyManager;
    private AesEncryptionService encryptionService;
    
    @BeforeEach
    void setUp() {
        // Setup Redis template
        redisTemplate = new ReactiveStringRedisTemplate(
            org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory
                .createStandaloneConnectionFactory(redis.getHost(), redis.getFirstMappedPort())
        );
        
        // Setup encryption properties
        encryptionProperties = new EncryptionProperties();
        encryptionProperties.setAlgorithm("AES");
        encryptionProperties.setKeyLength(256);
        encryptionProperties.setTransformation("AES/GCM/NoPadding");
        encryptionProperties.setKeyRotationInterval(1000L); // 1 second for testing
        
        // Setup services
        keyManager = new RedisKeyManager(redisTemplate, encryptionProperties);
        encryptionService = new AesEncryptionService(keyManager, encryptionProperties);
        
        // Clear Redis
        redisTemplate.getConnectionFactory().getReactiveConnection()
            .serverCommands().flushAll().block(Duration.ofSeconds(5));
    }
    
    @Test
    void shouldPerformCompleteEncryptionDecryptionFlow() throws Exception {
        // Given
        String keyId = "integration-test-key";
        String plaintext = "Integration test message";
        
        // Generate key
        SecretKey key = keyManager.generateKey(keyId);
        assertNotNull(key);
        
        // When - Encrypt
        byte[] encrypted = encryptionService.encrypt(plaintext.getBytes(), keyId);
        assertNotNull(encrypted);
        assertTrue(encrypted.length > plaintext.length()); // Should be larger due to IV and tag
        
        // When - Decrypt
        byte[] decrypted = encryptionService.decrypt(encrypted, keyId);
        String decryptedText = new String(decrypted);
        
        // Then
        assertEquals(plaintext, decryptedText);
    }
    
    @Test
    void shouldHandleKeyRotation() throws Exception {
        // Given
        String keyId = "rotation-test-key";
        String plaintext = "Message before rotation";
        
        // Generate initial key
        SecretKey originalKey = keyManager.generateKey(keyId);
        
        // Encrypt with original key
        byte[] encrypted = encryptionService.encrypt(plaintext.getBytes(), keyId);
        
        // Wait for key to be eligible for rotation
        Thread.sleep(1100); // Wait longer than rotation interval
        
        // When - Rotate key
        SecretKey rotatedKey = keyManager.rotateKey(keyId);
        
        // Then
        assertNotNull(rotatedKey);
        assertNotEquals(originalKey.getEncoded(), rotatedKey.getEncoded());
        
        // Old encrypted data should not be decryptable with new key
        assertThrows(EncryptionException.class, 
            () -> encryptionService.decrypt(encrypted, keyId));
        
        // But new data should work with new key
        String newPlaintext = "Message after rotation";
        byte[] newEncrypted = encryptionService.encrypt(newPlaintext.getBytes(), keyId);
        byte[] newDecrypted = encryptionService.decrypt(newEncrypted, keyId);
        assertEquals(newPlaintext, new String(newDecrypted));
    }
    
    @Test
    void shouldManageMultipleKeys() throws Exception {
        // Given
        String keyId1 = "key-1";
        String keyId2 = "key-2";
        String keyId3 = "key-3";
        
        // When - Generate multiple keys
        keyManager.generateKey(keyId1);
        keyManager.generateKey(keyId2);
        keyManager.generateKey(keyId3);
        
        // Then
        Set<String> allKeys = keyManager.getAllKeyIds();
        assertEquals(3, allKeys.size());
        assertTrue(allKeys.contains(keyId1));
        assertTrue(allKeys.contains(keyId2));
        assertTrue(allKeys.contains(keyId3));
        
        // All keys should exist
        assertTrue(keyManager.keyExists(keyId1));
        assertTrue(keyManager.keyExists(keyId2));
        assertTrue(keyManager.keyExists(keyId3));
    }
    
    @Test
    void shouldCleanupExpiredKeys() throws Exception {
        // Given
        String expiredKeyId = "expired-key";
        String recentKeyId = "recent-key";
        
        // Generate keys
        keyManager.generateKey(expiredKeyId);
        Thread.sleep(1100); // Make first key expired
        keyManager.generateKey(recentKeyId);
        
        // Verify both keys exist
        assertTrue(keyManager.keyExists(expiredKeyId));
        assertTrue(keyManager.keyExists(recentKeyId));
        
        // When - Cleanup expired keys
        keyManager.cleanupExpiredKeys();
        
        // Then - Only expired key should be removed
        assertFalse(keyManager.keyExists(expiredKeyId));
        assertTrue(keyManager.keyExists(recentKeyId));
    }
    
    @Test
    void shouldHandleKeyDeletion() throws Exception {
        // Given
        String keyId = "deletion-test-key";
        keyManager.generateKey(keyId);
        assertTrue(keyManager.keyExists(keyId));
        
        // When
        keyManager.deleteKey(keyId);
        
        // Then
        assertFalse(keyManager.keyExists(keyId));
        assertThrows(KeyNotFoundException.class, () -> keyManager.getKey(keyId));
    }
    
    @Test
    void shouldDetectKeyRotationRequirement() throws Exception {
        // Given
        String keyId = "rotation-detection-key";
        keyManager.generateKey(keyId);
        
        // Initially should not need rotation
        assertFalse(keyManager.shouldRotateKey(keyId));
        
        // Wait for key to age
        Thread.sleep(1100);
        
        // Now should need rotation
        assertTrue(keyManager.shouldRotateKey(keyId));
    }
    
    @Test
    void shouldHandleConcurrentEncryption() throws Exception {
        // Given
        String keyId = "concurrent-test-key";
        keyManager.generateKey(keyId);
        
        // When - Encrypt multiple messages concurrently
        String[] messages = {"Message 1", "Message 2", "Message 3", "Message 4", "Message 5"};
        byte[][] encrypted = new byte[messages.length][];
        
        // Encrypt all messages
        for (int i = 0; i < messages.length; i++) {
            encrypted[i] = encryptionService.encrypt(messages[i].getBytes(), keyId);
        }
        
        // Then - All should decrypt correctly
        for (int i = 0; i < messages.length; i++) {
            byte[] decrypted = encryptionService.decrypt(encrypted[i], keyId);
            assertEquals(messages[i], new String(decrypted));
        }
        
        // All encrypted versions should be different (due to random IV)
        for (int i = 0; i < encrypted.length; i++) {
            for (int j = i + 1; j < encrypted.length; j++) {
                assertFalse(Arrays.equals(encrypted[i], encrypted[j]));
            }
        }
    }
    
    @Test
    void shouldHandleCurrentKeyIdManagement() throws Exception {
        // Given
        String newKeyId = "current-key-test";
        keyManager.generateKey(newKeyId);
        
        // When
        encryptionService.setCurrentKeyId(newKeyId);
        String currentKeyId = encryptionService.getCurrentKeyId();
        
        // Then
        assertEquals(newKeyId, currentKeyId);
        
        // Should be able to encrypt/decrypt with current key
        String plaintext = "Current key test message";
        byte[] encrypted = encryptionService.encrypt(plaintext.getBytes(), currentKeyId);
        byte[] decrypted = encryptionService.decrypt(encrypted, currentKeyId);
        assertEquals(plaintext, new String(decrypted));
    }
    
    @Test
    void shouldHandleKeyMetadata() throws Exception {
        // Given
        String keyId = "metadata-test-key";
        
        // When
        keyManager.generateKey(keyId);
        
        // Then
        assertNotNull(keyManager.getKeyCreationTime(keyId));
        assertTrue(keyManager.getKeyCreationTime(keyId).isBefore(
            java.time.LocalDateTime.now().plusSeconds(1)));
    }
}