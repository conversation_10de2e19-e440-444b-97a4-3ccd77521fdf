package com.example.mqtt.websocket.performance;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能测试套件
 * 验证系统吞吐量和并发处理能力
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class PerformanceTestSuite {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceTestSuite.class);

    private static final int CONCURRENT_USERS = 100;
    private static final int MESSAGES_PER_USER = 100;
    private static final int TEST_DURATION_SECONDS = 60;

    /**
     * 基准性能测试
     */
    @Test
    public void baselinePerformanceTest() throws Exception {
        logger.info("Starting baseline performance test");
        
        long startTime = System.currentTimeMillis();
        AtomicInteger completedOperations = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_USERS);
        
        try {
            CompletableFuture<?>[] futures = new CompletableFuture[CONCURRENT_USERS];
            
            for (int i = 0; i < CONCURRENT_USERS; i++) {
                final int userId = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    performUserOperations(userId, MESSAGES_PER_USER, completedOperations, totalResponseTime);
                }, executor);
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures).get(TEST_DURATION_SECONDS + 10, TimeUnit.SECONDS);
            
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            
            // 计算性能指标
            int totalOperations = completedOperations.get();
            double throughput = (double) totalOperations / (totalTime / 1000.0);
            double averageResponseTime = totalOperations > 0 ? 
                (double) totalResponseTime.get() / totalOperations : 0;
            
            logger.info("Baseline Performance Test Results:");
            logger.info("  Total Operations: {}", totalOperations);
            logger.info("  Total Time: {} ms", totalTime);
            logger.info("  Throughput: {:.2f} ops/sec", throughput);
            logger.info("  Average Response Time: {:.2f} ms", averageResponseTime);
            logger.info("  Concurrent Users: {}", CONCURRENT_USERS);
            
            // 性能断言
            assert throughput > 100 : "Throughput should be greater than 100 ops/sec";
            assert averageResponseTime < 1000 : "Average response time should be less than 1000ms";
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
        }
    }

    /**
     * 内存压力测试
     */
    @Test
    public void memoryStressTest() throws Exception {
        logger.info("Starting memory stress test");
        
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        AtomicInteger allocatedObjects = new AtomicInteger(0);
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        try {
            // 创建内存压力
            CompletableFuture<?>[] futures = new CompletableFuture[10];
            for (int i = 0; i < 10; i++) {
                futures[i] = CompletableFuture.runAsync(() -> {
                    performMemoryIntensiveOperations(allocatedObjects);
                }, executor);
            }
            
            CompletableFuture.allOf(futures).get(30, TimeUnit.SECONDS);
            
            // 强制垃圾回收
            System.gc();
            Thread.sleep(1000);
            
            long finalMemory = runtime.totalMemory() - runtime.freeMemory();
            long memoryIncrease = finalMemory - initialMemory;
            
            logger.info("Memory Stress Test Results:");
            logger.info("  Initial Memory: {} MB", initialMemory / 1024 / 1024);
            logger.info("  Final Memory: {} MB", finalMemory / 1024 / 1024);
            logger.info("  Memory Increase: {} MB", memoryIncrease / 1024 / 1024);
            logger.info("  Objects Allocated: {}", allocatedObjects.get());
            
            // 内存泄漏检查
            assert memoryIncrease < 100 * 1024 * 1024 : "Memory increase should be less than 100MB";
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
        }
    }

    /**
     * 并发连接测试
     */
    @Test
    public void concurrentConnectionTest() throws Exception {
        logger.info("Starting concurrent connection test");
        
        AtomicInteger successfulConnections = new AtomicInteger(0);
        AtomicInteger failedConnections = new AtomicInteger(0);
        AtomicLong totalConnectionTime = new AtomicLong(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_USERS);
        
        try {
            CompletableFuture<?>[] futures = new CompletableFuture[CONCURRENT_USERS];
            
            for (int i = 0; i < CONCURRENT_USERS; i++) {
                final int connectionId = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    performConnectionTest(connectionId, successfulConnections, 
                        failedConnections, totalConnectionTime);
                }, executor);
            }
            
            CompletableFuture.allOf(futures).get(60, TimeUnit.SECONDS);
            
            int successful = successfulConnections.get();
            int failed = failedConnections.get();
            double averageConnectionTime = successful > 0 ? 
                (double) totalConnectionTime.get() / successful : 0;
            
            logger.info("Concurrent Connection Test Results:");
            logger.info("  Successful Connections: {}", successful);
            logger.info("  Failed Connections: {}", failed);
            logger.info("  Success Rate: {:.2f}%", (double) successful / (successful + failed) * 100);
            logger.info("  Average Connection Time: {:.2f} ms", averageConnectionTime);
            
            // 连接成功率断言
            double successRate = (double) successful / (successful + failed);
            assert successRate > 0.95 : "Connection success rate should be greater than 95%";
            assert averageConnectionTime < 5000 : "Average connection time should be less than 5000ms";
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
        }
    }

    /**
     * 长时间稳定性测试
     */
    @Test
    public void longRunningStabilityTest() throws Exception {
        logger.info("Starting long running stability test");
        
        AtomicInteger totalOperations = new AtomicInteger(0);
        AtomicInteger errors = new AtomicInteger(0);
        AtomicLong maxResponseTime = new AtomicLong(0);
        AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
        
        ExecutorService executor = Executors.newFixedThreadPool(20);
        long testEndTime = System.currentTimeMillis() + (TEST_DURATION_SECONDS * 1000);
        
        try {
            while (System.currentTimeMillis() < testEndTime) {
                CompletableFuture<?>[] futures = new CompletableFuture[20];
                
                for (int i = 0; i < 20; i++) {
                    futures[i] = CompletableFuture.runAsync(() -> {
                        performStabilityOperation(totalOperations, errors, 
                            maxResponseTime, minResponseTime);
                    }, executor);
                }
                
                CompletableFuture.allOf(futures).get(5, TimeUnit.SECONDS);
                Thread.sleep(1000); // 1秒间隔
            }
            
            int operations = totalOperations.get();
            int errorCount = errors.get();
            double errorRate = operations > 0 ? (double) errorCount / operations * 100 : 0;
            
            logger.info("Long Running Stability Test Results:");
            logger.info("  Test Duration: {} seconds", TEST_DURATION_SECONDS);
            logger.info("  Total Operations: {}", operations);
            logger.info("  Errors: {}", errorCount);
            logger.info("  Error Rate: {:.2f}%", errorRate);
            logger.info("  Max Response Time: {} ms", maxResponseTime.get());
            logger.info("  Min Response Time: {} ms", minResponseTime.get());
            
            // 稳定性断言
            assert errorRate < 1.0 : "Error rate should be less than 1%";
            assert maxResponseTime.get() < 10000 : "Max response time should be less than 10000ms";
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
        }
    }

    /**
     * 执行用户操作
     */
    private void performUserOperations(int userId, int messageCount, 
                                     AtomicInteger completedOperations, 
                                     AtomicLong totalResponseTime) {
        for (int i = 0; i < messageCount; i++) {
            long startTime = System.currentTimeMillis();
            
            try {
                // 模拟消息处理操作
                simulateMessageProcessing();
                
                long responseTime = System.currentTimeMillis() - startTime;
                totalResponseTime.addAndGet(responseTime);
                completedOperations.incrementAndGet();
                
            } catch (Exception e) {
                logger.warn("Error in user operation for user {}: {}", userId, e.getMessage());
            }
        }
    }

    /**
     * 执行内存密集型操作
     */
    private void performMemoryIntensiveOperations(AtomicInteger allocatedObjects) {
        for (int i = 0; i < 1000; i++) {
            try {
                // 创建大量对象
                byte[] data = new byte[1024 * 10]; // 10KB
                String text = new String(data);
                text.hashCode(); // 使用对象防止优化
                
                allocatedObjects.incrementAndGet();
                
                if (i % 100 == 0) {
                    Thread.sleep(10); // 短暂休息
                }
            } catch (Exception e) {
                logger.warn("Error in memory intensive operation: {}", e.getMessage());
            }
        }
    }

    /**
     * 执行连接测试
     */
    private void performConnectionTest(int connectionId, 
                                     AtomicInteger successfulConnections,
                                     AtomicInteger failedConnections,
                                     AtomicLong totalConnectionTime) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 模拟连接建立
            simulateConnectionEstablishment();
            
            long connectionTime = System.currentTimeMillis() - startTime;
            totalConnectionTime.addAndGet(connectionTime);
            successfulConnections.incrementAndGet();
            
        } catch (Exception e) {
            failedConnections.incrementAndGet();
            logger.warn("Connection failed for connection {}: {}", connectionId, e.getMessage());
        }
    }

    /**
     * 执行稳定性操作
     */
    private void performStabilityOperation(AtomicInteger totalOperations,
                                         AtomicInteger errors,
                                         AtomicLong maxResponseTime,
                                         AtomicLong minResponseTime) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 模拟各种操作
            simulateVariousOperations();
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            // 更新响应时间统计
            maxResponseTime.updateAndGet(current -> Math.max(current, responseTime));
            minResponseTime.updateAndGet(current -> Math.min(current, responseTime));
            
            totalOperations.incrementAndGet();
            
        } catch (Exception e) {
            errors.incrementAndGet();
            logger.warn("Error in stability operation: {}", e.getMessage());
        }
    }

    /**
     * 模拟消息处理
     */
    private void simulateMessageProcessing() throws InterruptedException {
        // 模拟 CPU 密集型操作
        for (int i = 0; i < 1000; i++) {
            Math.sqrt(i);
        }
        
        // 模拟 I/O 等待
        Thread.sleep(1 + (int) (Math.random() * 5));
    }

    /**
     * 模拟连接建立
     */
    private void simulateConnectionEstablishment() throws InterruptedException {
        // 模拟网络延迟
        Thread.sleep(10 + (int) (Math.random() * 50));
        
        // 模拟连接验证
        for (int i = 0; i < 100; i++) {
            Math.random();
        }
    }

    /**
     * 模拟各种操作
     */
    private void simulateVariousOperations() throws InterruptedException {
        int operationType = (int) (Math.random() * 3);
        
        switch (operationType) {
            case 0:
                simulateMessageProcessing();
                break;
            case 1:
                simulateConnectionEstablishment();
                break;
            case 2:
                // 模拟数据库操作
                Thread.sleep(5 + (int) (Math.random() * 15));
                break;
        }
    }
}