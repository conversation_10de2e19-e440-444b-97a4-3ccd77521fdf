package com.example.mqtt.websocket.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.support.WebExchangeBindException;
import org.springframework.web.server.ServerWebInputException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Path;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * GlobalExceptionHandler 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {
    
    private GlobalExceptionHandler exceptionHandler;
    
    @BeforeEach
    void setUp() {
        exceptionHandler = new GlobalExceptionHandler();
    }
    
    @Test
    void shouldHandleWebExchangeBindException() {
        // Given
        BeanPropertyBindingResult bindingResult = new BeanPropertyBindingResult(new Object(), "testObject");
        bindingResult.addError(new FieldError("testObject", "field1", "Field1 is required"));
        bindingResult.addError(new FieldError("testObject", "field2", "Field2 must be valid"));
        
        WebExchangeBindException exception = new WebExchangeBindException(null, bindingResult);
        
        // When
        Mono<ResponseEntity<GlobalExceptionHandler.ErrorResponse>> result = 
            exceptionHandler.handleValidationException(exception);
        
        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
                GlobalExceptionHandler.ErrorResponse errorResponse = response.getBody();
                assertNotNull(errorResponse);
                assertEquals("VALIDATION_ERROR", errorResponse.getErrorCode());
                assertEquals("Request validation failed", errorResponse.getMessage());
                assertEquals(2, errorResponse.getDetails().size());
                assertEquals("Field1 is required", errorResponse.getDetails().get("field1"));
                assertEquals("Field2 must be valid", errorResponse.getDetails().get("field2"));
            })
            .verifyComplete();
    }
    
    @Test
    void shouldHandleConstraintViolationException() {
        // Given
        ConstraintViolation<?> violation1 = mock(ConstraintViolation.class);
        ConstraintViolation<?> violation2 = mock(ConstraintViolation.class);
        
        Path path1 = mock(Path.class);
        Path path2 = mock(Path.class);
        
        when(violation1.getPropertyPath()).thenReturn(path1);
        when(violation2.getPropertyPath()).thenReturn(path2);
        when(path1.toString()).thenReturn("property1");
        when(path2.toString()).thenReturn("property2");
        when(violation1.getMessage()).thenReturn("Property1 constraint violated");
        when(violation2.getMessage()).thenReturn("Property2 constraint violated");
        
        Set<ConstraintViolation<?>> violations = Set.of(violation1, violation2);
        ConstraintViolationException exception = new ConstraintViolationException(violations);
        
        // When
        Mono<ResponseEntity<GlobalExceptionHandler.ErrorResponse>> result = 
            exceptionHandler.handleConstraintViolationException(exception);
        
        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
                GlobalExceptionHandler.ErrorResponse errorResponse = response.getBody();
                assertNotNull(errorResponse);
                assertEquals("CONSTRAINT_VIOLATION", errorResponse.getErrorCode());
                assertEquals("Request constraint violation", errorResponse.getMessage());
                assertEquals(2, errorResponse.getDetails().size());
            })
            .verifyComplete();
    }
    
    @Test
    void shouldHandleServerWebInputException() {
        // Given
        ServerWebInputException exception = new ServerWebInputException("Invalid input format");
        
        // When
        Mono<ResponseEntity<GlobalExceptionHandler.ErrorResponse>> result = 
            exceptionHandler.handleServerWebInputException(exception);
        
        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
                GlobalExceptionHandler.ErrorResponse errorResponse = response.getBody();
                assertNotNull(errorResponse);
                assertEquals("INPUT_ERROR", errorResponse.getErrorCode());
                assertEquals("Invalid input format or missing required parameters", errorResponse.getMessage());
                assertNotNull(errorResponse.getDetails().get("detail"));
            })
            .verifyComplete();
    }
    
    @Test
    void shouldHandleIllegalArgumentException() {
        // Given
        IllegalArgumentException exception = new IllegalArgumentException("Invalid argument provided");
        
        // When
        Mono<ResponseEntity<GlobalExceptionHandler.ErrorResponse>> result = 
            exceptionHandler.handleIllegalArgumentException(exception);
        
        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
                GlobalExceptionHandler.ErrorResponse errorResponse = response.getBody();
                assertNotNull(errorResponse);
                assertEquals("ILLEGAL_ARGUMENT", errorResponse.getErrorCode());
                assertEquals("Invalid argument provided", errorResponse.getMessage());
                assertEquals("Invalid argument provided", errorResponse.getDetails().get("detail"));
            })
            .verifyComplete();
    }
    
    @Test
    void shouldHandleRuntimeException() {
        // Given
        RuntimeException exception = new RuntimeException("Something went wrong");
        
        // When
        Mono<ResponseEntity<GlobalExceptionHandler.ErrorResponse>> result = 
            exceptionHandler.handleRuntimeException(exception);
        
        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
                GlobalExceptionHandler.ErrorResponse errorResponse = response.getBody();
                assertNotNull(errorResponse);
                assertEquals("RUNTIME_ERROR", errorResponse.getErrorCode());
                assertEquals("An unexpected error occurred", errorResponse.getMessage());
                assertEquals("Something went wrong", errorResponse.getDetails().get("detail"));
            })
            .verifyComplete();
    }
    
    @Test
    void shouldHandleGenericException() {
        // Given
        Exception exception = new Exception("Generic error occurred");
        
        // When
        Mono<ResponseEntity<GlobalExceptionHandler.ErrorResponse>> result = 
            exceptionHandler.handleGenericException(exception);
        
        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
                GlobalExceptionHandler.ErrorResponse errorResponse = response.getBody();
                assertNotNull(errorResponse);
                assertEquals("INTERNAL_ERROR", errorResponse.getErrorCode());
                assertEquals("An internal server error occurred", errorResponse.getMessage());
                assertEquals("Please contact system administrator", errorResponse.getDetails().get("detail"));
            })
            .verifyComplete();
    }
    
    @Test
    void shouldCreateErrorResponseWithCorrectTimestamp() {
        // Given
        RuntimeException exception = new RuntimeException("Test error");
        
        // When
        Mono<ResponseEntity<GlobalExceptionHandler.ErrorResponse>> result = 
            exceptionHandler.handleRuntimeException(exception);
        
        // Then
        StepVerifier.create(result)
            .assertNext(response -> {
                GlobalExceptionHandler.ErrorResponse errorResponse = response.getBody();
                assertNotNull(errorResponse);
                assertNotNull(errorResponse.getTimestamp());
            })
            .verifyComplete();
    }
}