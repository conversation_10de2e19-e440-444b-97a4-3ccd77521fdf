package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.service.EncryptionService;
import com.example.mqtt.websocket.service.KeyManager;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.eclipse.paho.client.mqttv3.*;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.socket.*;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import javax.crypto.SecretKey;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 加密消息传输端到端测试
 * 测试消息加密、解密、密钥管理和安全传输
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.encryption.enabled=true",
    "app.encryption.algorithm=AES",
    "app.encryption.key-length=256",
    "app.encryption.key-rotation-interval=300000", // 5 minutes for testing
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.websocket.endpoint=/ws",
    "logging.level.com.example.mqtt.websocket=DEBUG"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class EncryptedMessageEndToEndTest {
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private EncryptionService encryptionService;
    
    @Autowired
    private KeyManager keyManager;
    
    private WebSocketSession webSocketSession;
    private MqttClient mqttClient;
    private ObjectMapper objectMapper;
    private CountDownLatch messageLatch;
    private AtomicReference<String> receivedWebSocketMessage;
    private AtomicReference<String> receivedMqttMessage;
    private AtomicInteger encryptedMessageCount;
    
    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        messageLatch = new CountDownLatch(1);
        receivedWebSocketMessage = new AtomicReference<>();
        receivedMqttMessage = new AtomicReference<>();
        encryptedMessageCount = new AtomicInteger(0);
        
        // 设置 MQTT 客户端
        setupMqttClient();
        
        // 设置 WebSocket 客户端
        setupWebSocketClient();
        
        // 等待连接建立
        Thread.sleep(2000);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (webSocketSession != null && webSocketSession.isOpen()) {
            webSocketSession.close();
        }
        if (mqttClient != null && mqttClient.isConnected()) {
            mqttClient.disconnect();
            mqttClient.close();
        }
    }
    
    private void setupMqttClient() throws Exception {
        String clientId = "encryption-test-client-" + System.currentTimeMillis();
        mqttClient = new MqttClient("tcp://localhost:1883", clientId);
        
        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(true);
        options.setConnectionTimeout(10);
        options.setKeepAliveInterval(30);
        
        mqttClient.setCallback(new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                System.err.println("MQTT connection lost: " + cause.getMessage());
            }
            
            @Override
            public void messageArrived(String topic, MqttMessage message) throws Exception {
                String payload = new String(message.getPayload(), StandardCharsets.UTF_8);
                System.out.println("Received MQTT message on topic " + topic + ": " + payload);
                receivedMqttMessage.set(payload);
                messageLatch.countDown();
            }
            
            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                System.out.println("MQTT message delivery complete");
            }
        });
        
        mqttClient.connect(options);
        
        // 订阅测试主题
        mqttClient.subscribe("test/encrypted/+/data", 1);
        mqttClient.subscribe("test/secure/+/response", 1);
    }
    
    private void setupWebSocketClient() throws Exception {
        WebSocketClient client = new StandardWebSocketClient();
        URI uri = URI.create("ws://localhost:" + port + "/ws?userId=encryptionTestUser");
        
        WebSocketHandler handler = new WebSocketHandler() {
            @Override
            public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                System.out.println("WebSocket connection established for encryption test: " + session.getId());
            }
            
            @Override
            public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                String payload = message.getPayload().toString();
                System.out.println("Received WebSocket message: " + payload);
                
                // 检查是否是加密消息
                try {
                    JsonNode messageJson = objectMapper.readTree(payload);
                    if (messageJson.has("encrypted") && messageJson.get("encrypted").asBoolean()) {
                        encryptedMessageCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    // 忽略非 JSON 消息
                }
                
                receivedWebSocketMessage.set(payload);
                messageLatch.countDown();
            }
            
            @Override
            public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                System.err.println("WebSocket transport error: " + exception.getMessage());
                exception.printStackTrace();
            }
            
            @Override
            public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
                System.out.println("WebSocket connection closed: " + closeStatus);
            }
            
            @Override
            public boolean supportsPartialMessages() {
                return false;
            }
        };
        
        webSocketSession = client.doHandshake(handler, null, uri).get(10, TimeUnit.SECONDS);
        
        // 等待连接确认消息
        assertTrue(messageLatch.await(10, TimeUnit.SECONDS), "Connection confirmation not received");
        
        // 重置计数器
        messageLatch = new CountDownLatch(1);
        receivedWebSocketMessage.set(null);
    }
    
    @Test
    @Order(1)
    void testBasicEncryptionDecryption() throws Exception {
        System.out.println("Testing basic encryption and decryption");
        
        // 准备测试数据
        String originalData = "This is sensitive test data that needs encryption";
        String keyId = encryptionService.generateKeyId();
        
        // 加密数据
        byte[] encryptedData = encryptionService.encrypt(originalData.getBytes(StandardCharsets.UTF_8), keyId);
        assertNotNull(encryptedData, "Encrypted data should not be null");
        assertTrue(encryptedData.length > 0, "Encrypted data should not be empty");
        
        // 验证加密后数据与原数据不同
        assertFalse(Arrays.equals(originalData.getBytes(StandardCharsets.UTF_8), encryptedData), 
            "Encrypted data should be different from original");
        
        // 解密数据
        byte[] decryptedData = encryptionService.decrypt(encryptedData, keyId);
        assertNotNull(decryptedData, "Decrypted data should not be null");
        
        String decryptedString = new String(decryptedData, StandardCharsets.UTF_8);
        assertEquals(originalData, decryptedString, "Decrypted data should match original");
        
        System.out.println("Basic encryption/decryption test completed successfully");
    }
    
    @Test
    @Order(2)
    void testEncryptedMqttToWebSocketFlow() throws Exception {
        System.out.println("Testing encrypted MQTT to WebSocket flow");
        
        // 准备敏感数据
        Map<String, Object> sensitiveData = Map.of(
            "userId", "user-12345",
            "accountBalance", 50000.75,
            "creditCardNumber", "4532-1234-5678-9012",
            "ssn", "***********",
            "timestamp", System.currentTimeMillis()
        );
        
        String sensitivePayload = objectMapper.writeValueAsString(sensitiveData);
        
        // 生成加密密钥
        String keyId = encryptionService.generateKeyId();
        
        // 加密敏感数据
        byte[] encryptedPayload = encryptionService.encrypt(sensitivePayload.getBytes(StandardCharsets.UTF_8), keyId);
        
        // 创建加密消息包装
        Map<String, Object> encryptedMessage = Map.of(
            "id", "encrypted-msg-" + System.currentTimeMillis(),
            "timestamp", System.currentTimeMillis(),
            "type", "encrypted_sensitive_data",
            "encrypted", true,
            "keyId", keyId,
            "algorithm", "AES-256-GCM",
            "payload", Base64.getEncoder().encodeToString(encryptedPayload)
        );
        
        String messageJson = objectMapper.writeValueAsString(encryptedMessage);
        
        // 通过 MQTT 发送加密消息
        MqttMessage mqttMessage = new MqttMessage(messageJson.getBytes(StandardCharsets.UTF_8));
        mqttMessage.setQos(1);
        mqttClient.publish("test/sensor/encrypted/data", mqttMessage);
        
        // 等待消息通过 WebSocket 接收（应该已解密）
        boolean messageReceived = messageLatch.await(15, TimeUnit.SECONDS);
        assertTrue(messageReceived, "Encrypted message should be received and decrypted");
        
        // 验证接收到的消息
        String receivedMessage = receivedWebSocketMessage.get();
        assertNotNull(receivedMessage, "Decrypted message should not be null");
        
        JsonNode receivedJson = objectMapper.readTree(receivedMessage);
        assertEquals("encrypted_sensitive_data", receivedJson.get("type").asText());
        
        // 验证敏感数据已正确解密
        if (receivedJson.has("decryptedData")) {
            JsonNode decryptedData = receivedJson.get("decryptedData");
            assertEquals("user-12345", decryptedData.get("userId").asText());
            assertEquals(50000.75, decryptedData.get("accountBalance").asDouble(), 0.01);
        }
        
        System.out.println("Encrypted MQTT to WebSocket flow test completed successfully");
    }
    
    @Test
    @Order(3)
    void testEncryptedWebSocketToMqttFlow() throws Exception {
        System.out.println("Testing encrypted WebSocket to MQTT flow");
        
        // 重置消息接收器
        messageLatch = new CountDownLatch(1);
        receivedMqttMessage.set(null);
        
        // 准备敏感命令数据
        Map<String, Object> sensitiveCommand = Map.of(
            "command", "transfer_funds",
            "fromAccount", "ACC-*********",
            "toAccount", "ACC-*********",
            "amount", 10000.00,
            "authToken", "secret-auth-token-12345",
            "timestamp", System.currentTimeMillis()
        );
        
        String commandPayload = objectMapper.writeValueAsString(sensitiveCommand);
        
        // 生成加密密钥
        String keyId = encryptionService.generateKeyId();
        
        // 加密命令数据
        byte[] encryptedCommand = encryptionService.encrypt(commandPayload.getBytes(StandardCharsets.UTF_8), keyId);
        
        // 创建加密 WebSocket 消息
        Map<String, Object> encryptedWebSocketMessage = Map.of(
            "type", "encrypted_command",
            "encrypted", true,
            "keyId", keyId,
            "payload", Base64.getEncoder().encodeToString(encryptedCommand),
            "headers", Map.of(
                "mqtt.targetTopic", "test/secure/command/response",
                "requiresEncryption", true
            )
        );
        
        String messageJson = objectMapper.writeValueAsString(encryptedWebSocketMessage);
        
        // 通过 WebSocket 发送加密消息
        webSocketSession.sendMessage(new TextMessage(messageJson));
        
        // 等待消息通过 MQTT 接收
        boolean messageReceived = messageLatch.await(15, TimeUnit.SECONDS);
        assertTrue(messageReceived, "Encrypted message should be received via MQTT");
        
        // 验证接收到的消息
        String receivedMessage = receivedMqttMessage.get();
        assertNotNull(receivedMessage, "MQTT message should not be null");
        
        JsonNode receivedJson = objectMapper.readTree(receivedMessage);
        
        // 验证消息仍然是加密的（在传输过程中保持加密）
        if (receivedJson.has("encrypted")) {
            assertTrue(receivedJson.get("encrypted").asBoolean(), "Message should remain encrypted in transit");
            assertTrue(receivedJson.has("payload"), "Encrypted payload should be present");
        }
        
        System.out.println("Encrypted WebSocket to MQTT flow test completed successfully");
    }
    
    @Test
    @Order(4)
    void testKeyRotationDuringTransmission() throws Exception {
        System.out.println("Testing key rotation during message transmission");
        
        // 生成初始密钥
        String initialKeyId = encryptionService.generateKeyId();
        SecretKey initialKey = keyManager.getKey(initialKeyId);
        assertNotNull(initialKey, "Initial key should be generated");
        
        // 使用初始密钥加密消息
        String testData = "Data encrypted with initial key";
        byte[] encryptedWithInitialKey = encryptionService.encrypt(testData.getBytes(StandardCharsets.UTF_8), initialKeyId);
        
        // 执行密钥轮换
        keyManager.rotateKey(initialKeyId);
        Thread.sleep(1000); // 等待密钥轮换完成
        
        // 验证旧密钥仍可解密之前的数据
        byte[] decryptedWithOldKey = encryptionService.decrypt(encryptedWithInitialKey, initialKeyId);
        assertEquals(testData, new String(decryptedWithOldKey, StandardCharsets.UTF_8), 
            "Old key should still decrypt previous data");
        
        // 生成新密钥并测试
        String newKeyId = encryptionService.generateKeyId();
        String newTestData = "Data encrypted with new key after rotation";
        byte[] encryptedWithNewKey = encryptionService.encrypt(newTestData.getBytes(StandardCharsets.UTF_8), newKeyId);
        
        byte[] decryptedWithNewKey = encryptionService.decrypt(encryptedWithNewKey, newKeyId);
        assertEquals(newTestData, new String(decryptedWithNewKey, StandardCharsets.UTF_8), 
            "New key should encrypt and decrypt correctly");
        
        // 测试密钥轮换期间的消息传输
        messageLatch = new CountDownLatch(1);
        receivedWebSocketMessage.set(null);
        
        Map<String, Object> rotationTestMessage = Map.of(
            "type", "key_rotation_test",
            "encrypted", true,
            "keyId", newKeyId,
            "payload", Base64.getEncoder().encodeToString(encryptedWithNewKey),
            "rotationTest", true
        );
        
        String messageJson = objectMapper.writeValueAsString(rotationTestMessage);
        
        // 通过 MQTT 发送使用新密钥的消息
        MqttMessage mqttMessage = new MqttMessage(messageJson.getBytes(StandardCharsets.UTF_8));
        mqttMessage.setQos(1);
        mqttClient.publish("test/sensor/rotation/data", mqttMessage);
        
        // 等待消息接收和解密
        boolean messageReceived = messageLatch.await(15, TimeUnit.SECONDS);
        assertTrue(messageReceived, "Message with rotated key should be received");
        
        System.out.println("Key rotation during transmission test completed successfully");
    }
    
    @Test
    @Order(5)
    void testMultipleEncryptionAlgorithms() throws Exception {
        System.out.println("Testing multiple encryption algorithms");
        
        // 测试不同的加密配置
        List<Map<String, Object>> encryptionConfigs = Arrays.asList(
            Map.of("algorithm", "AES", "keyLength", 256, "mode", "GCM"),
            Map.of("algorithm", "AES", "keyLength", 192, "mode", "CBC"),
            Map.of("algorithm", "AES", "keyLength", 128, "mode", "CTR")
        );
        
        for (Map<String, Object> config : encryptionConfigs) {
            String algorithm = (String) config.get("algorithm");
            int keyLength = (Integer) config.get("keyLength");
            String mode = (String) config.get("mode");
            
            System.out.println("Testing " + algorithm + "-" + keyLength + "-" + mode);
            
            // 生成特定算法的密钥
            String keyId = encryptionService.generateKeyId();
            
            // 准备测试数据
            String testData = String.format("Test data for %s-%d-%s encryption", algorithm, keyLength, mode);
            
            // 加密数据
            byte[] encryptedData = encryptionService.encrypt(testData.getBytes(StandardCharsets.UTF_8), keyId);
            assertNotNull(encryptedData, "Encrypted data should not be null for " + algorithm);
            
            // 解密数据
            byte[] decryptedData = encryptionService.decrypt(encryptedData, keyId);
            String decryptedString = new String(decryptedData, StandardCharsets.UTF_8);
            
            assertEquals(testData, decryptedString, 
                "Decryption should work correctly for " + algorithm + "-" + keyLength + "-" + mode);
        }
        
        System.out.println("Multiple encryption algorithms test completed successfully");
    }
    
    @Test
    @Order(6)
    void testEncryptionPerformanceUnderLoad() throws Exception {
        System.out.println("Testing encryption performance under load");
        
        int messageCount = 100;
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch performanceLatch = new CountDownLatch(messageCount);
        AtomicInteger successfulEncryptions = new AtomicInteger(0);
        AtomicInteger successfulDecryptions = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        // 并发执行加密解密操作
        for (int i = 0; i < messageCount; i++) {
            final int messageIndex = i;
            
            executor.submit(() -> {
                try {
                    // 生成测试数据
                    String testData = String.format("Performance test message %d with timestamp %d", 
                        messageIndex, System.currentTimeMillis());
                    
                    // 生成密钥
                    String keyId = encryptionService.generateKeyId();
                    
                    // 加密
                    byte[] encryptedData = encryptionService.encrypt(testData.getBytes(StandardCharsets.UTF_8), keyId);
                    successfulEncryptions.incrementAndGet();
                    
                    // 解密
                    byte[] decryptedData = encryptionService.decrypt(encryptedData, keyId);
                    String decryptedString = new String(decryptedData, StandardCharsets.UTF_8);
                    
                    if (testData.equals(decryptedString)) {
                        successfulDecryptions.incrementAndGet();
                    }
                    
                } catch (Exception e) {
                    System.err.println("Encryption performance test error: " + e.getMessage());
                } finally {
                    performanceLatch.countDown();
                }
            });
        }
        
        // 等待所有操作完成
        boolean allCompleted = performanceLatch.await(60, TimeUnit.SECONDS);
        assertTrue(allCompleted, "All encryption operations should complete within timeout");
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        executor.shutdown();
        
        // 验证性能结果
        assertEquals(messageCount, successfulEncryptions.get(), "All encryptions should succeed");
        assertEquals(messageCount, successfulDecryptions.get(), "All decryptions should succeed");
        
        double operationsPerSecond = (messageCount * 2.0) / (totalTime / 1000.0); // 加密+解密
        System.out.println(String.format("Encryption performance: %.2f operations/second", operationsPerSecond));
        System.out.println(String.format("Total time: %d ms for %d operations", totalTime, messageCount * 2));
        
        // 性能基准：至少每秒100次操作
        assertTrue(operationsPerSecond >= 100, "Encryption performance should be at least 100 ops/sec");
        
        System.out.println("Encryption performance under load test completed successfully");
    }
    
    @Test
    @Order(7)
    void testEncryptionErrorHandling() throws Exception {
        System.out.println("Testing encryption error handling");
        
        // 测试无效密钥ID
        try {
            encryptionService.decrypt("invalid data".getBytes(), "non-existent-key-id");
            fail("Should throw exception for invalid key ID");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("key") || e.getMessage().contains("decrypt"), 
                "Exception should indicate key or decryption error");
        }
        
        // 测试损坏的加密数据
        String validKeyId = encryptionService.generateKeyId();
        byte[] corruptedData = "corrupted encrypted data".getBytes(StandardCharsets.UTF_8);
        
        try {
            encryptionService.decrypt(corruptedData, validKeyId);
            fail("Should throw exception for corrupted data");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("decrypt") || e.getMessage().contains("invalid"), 
                "Exception should indicate decryption error");
        }
        
        // 测试空数据加密
        try {
            byte[] emptyData = new byte[0];
            byte[] encryptedEmpty = encryptionService.encrypt(emptyData, validKeyId);
            byte[] decryptedEmpty = encryptionService.decrypt(encryptedEmpty, validKeyId);
            assertEquals(0, decryptedEmpty.length, "Empty data should encrypt and decrypt correctly");
        } catch (Exception e) {
            // 某些实现可能不支持空数据加密，这是可接受的
            System.out.println("Empty data encryption not supported: " + e.getMessage());
        }
        
        System.out.println("Encryption error handling test completed successfully");
    }
    
    @Test
    @Order(8)
    void testEndToEndEncryptedCommunication() throws Exception {
        System.out.println("Testing end-to-end encrypted communication");
        
        // 模拟完整的加密通信流程
        int communicationRounds = 5;
        
        for (int round = 1; round <= communicationRounds; round++) {
            System.out.println("Encrypted communication round " + round);
            
            // 重置消息接收器
            messageLatch = new CountDownLatch(2); // 期望接收两个消息
            receivedWebSocketMessage.set(null);
            receivedMqttMessage.set(null);
            
            // 1. 客户端通过 WebSocket 发送加密请求
            Map<String, Object> clientRequest = Map.of(
                "requestId", "req-" + round + "-" + System.currentTimeMillis(),
                "action", "get_account_balance",
                "userId", "user-" + round,
                "authToken", "secret-token-" + round
            );
            
            String requestPayload = objectMapper.writeValueAsString(clientRequest);
            String requestKeyId = encryptionService.generateKeyId();
            byte[] encryptedRequest = encryptionService.encrypt(requestPayload.getBytes(StandardCharsets.UTF_8), requestKeyId);
            
            Map<String, Object> encryptedWebSocketMessage = Map.of(
                "type", "encrypted_request",
                "encrypted", true,
                "keyId", requestKeyId,
                "payload", Base64.getEncoder().encodeToString(encryptedRequest),
                "headers", Map.of("mqtt.targetTopic", "test/secure/request/response")
            );
            
            webSocketSession.sendMessage(new TextMessage(objectMapper.writeValueAsString(encryptedWebSocketMessage)));
            
            // 2. 服务器通过 MQTT 发送加密响应
            Thread.sleep(1000); // 给请求处理时间
            
            Map<String, Object> serverResponse = Map.of(
                "requestId", "req-" + round + "-" + System.currentTimeMillis(),
                "status", "success",
                "data", Map.of(
                    "accountBalance", 75000.50 + round * 1000,
                    "currency", "USD",
                    "lastUpdated", System.currentTimeMillis()
                )
            );
            
            String responsePayload = objectMapper.writeValueAsString(serverResponse);
            String responseKeyId = encryptionService.generateKeyId();
            byte[] encryptedResponse = encryptionService.encrypt(responsePayload.getBytes(StandardCharsets.UTF_8), responseKeyId);
            
            Map<String, Object> encryptedMqttMessage = Map.of(
                "type", "encrypted_response",
                "encrypted", true,
                "keyId", responseKeyId,
                "payload", Base64.getEncoder().encodeToString(encryptedResponse)
            );
            
            MqttMessage mqttMessage = new MqttMessage(objectMapper.writeValueAsString(encryptedMqttMessage).getBytes(StandardCharsets.UTF_8));
            mqttMessage.setQos(1);
            mqttClient.publish("test/sensor/encrypted/data", mqttMessage);
            
            // 等待两个消息都被处理
            boolean bothReceived = messageLatch.await(20, TimeUnit.SECONDS);
            assertTrue(bothReceived, "Both encrypted messages should be processed in round " + round);
            
            Thread.sleep(1000); // 轮次间隔
        }
        
        // 验证加密消息计数
        assertTrue(encryptedMessageCount.get() >= communicationRounds, 
            "Should have processed multiple encrypted messages");
        
        System.out.println("End-to-end encrypted communication test completed successfully");
        System.out.println("Total encrypted messages processed: " + encryptedMessageCount.get());
    }
}