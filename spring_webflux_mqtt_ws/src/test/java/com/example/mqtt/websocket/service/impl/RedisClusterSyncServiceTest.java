package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.properties.RedisProperties;
import com.example.mqtt.websocket.service.ClusterStateManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RedisClusterSyncServiceTest {

    @Mock
    private ClusterStateManager clusterStateManager;

    private RedisProperties redisProperties;
    private RedisClusterSyncService clusterSyncService;

    @BeforeEach
    void setUp() {
        redisProperties = new RedisProperties(
                "localhost", 6379, null, 0,
                Duration.ofSeconds(5), Duration.ofSeconds(3),
                java.util.List.of(),
                new RedisProperties.Pool(),
                new RedisProperties.Cluster(
                        "test:instance:", Duration.ofSeconds(1),
                        Duration.ofSeconds(5), "test:messages",
                        Duration.ofSeconds(2)
                )
        );
        
        clusterSyncService = new RedisClusterSyncService(
                clusterStateManager, redisProperties, 8080);
    }

    @Test
    void shouldStartSuccessfully() {
        // Given
        when(clusterStateManager.registerInstance(anyString(), any()))
                .thenReturn(Mono.empty());
        when(clusterStateManager.listenClusterMessages())
                .thenReturn(Flux.empty());

        // When & Then
        StepVerifier.create(clusterSyncService.start())
                .verifyComplete();

        verify(clusterStateManager).registerInstance(anyString(), any());
    }

    @Test
    void shouldStopSuccessfully() {
        // Given
        when(clusterStateManager.registerInstance(anyString(), any()))
                .thenReturn(Mono.empty());
        when(clusterStateManager.unregisterInstance(anyString()))
                .thenReturn(Mono.empty());
        when(clusterStateManager.listenClusterMessages())
                .thenReturn(Flux.empty());

        // When & Then
        StepVerifier.create(
                clusterSyncService.start()
                        .then(clusterSyncService.stop())
        )
        .verifyComplete();

        verify(clusterStateManager).unregisterInstance(anyString());
    }

    @Test
    void shouldSyncClusterStateSuccessfully() {
        // Given
        when(clusterStateManager.getActiveInstances())
                .thenReturn(Mono.just(Set.of("instance-1", "instance-2")));
        when(clusterStateManager.cleanupExpiredInstances())
                .thenReturn(Mono.just(1L));

        // When & Then
        StepVerifier.create(clusterSyncService.syncClusterState())
                .verifyComplete();

        verify(clusterStateManager).getActiveInstances();
        verify(clusterStateManager).cleanupExpiredInstances();
    }

    @Test
    void shouldHandleInstanceJoinedEvent() {
        // Given
        String instanceId = "new-instance";
        ClusterStateManager.InstanceInfo instanceInfo = new ClusterStateManager.InstanceInfo(
                instanceId, "localhost", 8081, Instant.now(), Instant.now(), new HashMap<>()
        );
        
        when(clusterStateManager.getInstanceInfo(instanceId))
                .thenReturn(Mono.just(instanceInfo));
        when(clusterStateManager.broadcastToCluster(any()))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(clusterSyncService.handleInstanceJoined(instanceId))
                .verifyComplete();

        verify(clusterStateManager).getInstanceInfo(instanceId);
        verify(clusterStateManager).broadcastToCluster(any());
    }

    @Test
    void shouldHandleInstanceLeftEvent() {
        // Given
        String instanceId = "leaving-instance";
        when(clusterStateManager.broadcastToCluster(any()))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(clusterSyncService.handleInstanceLeft(instanceId))
                .verifyComplete();

        verify(clusterStateManager).broadcastToCluster(any());
    }

    @Test
    void shouldReturnCurrentInstanceId() {
        // When
        String instanceId = clusterSyncService.getCurrentInstanceId();

        // Then
        assertThat(instanceId).isNotNull();
        assertThat(instanceId).contains("8080");
    }

    @Test
    void shouldNotStartTwice() {
        // Given
        when(clusterStateManager.registerInstance(anyString(), any()))
                .thenReturn(Mono.empty());
        when(clusterStateManager.listenClusterMessages())
                .thenReturn(Flux.empty());

        // When & Then
        StepVerifier.create(
                clusterSyncService.start()
                        .then(clusterSyncService.start()) // 第二次启动应该被忽略
        )
        .verifyComplete();

        // 验证只注册了一次
        verify(clusterStateManager, times(1)).registerInstance(anyString(), any());
    }

    @Test
    void shouldNotStopTwice() {
        // Given
        when(clusterStateManager.registerInstance(anyString(), any()))
                .thenReturn(Mono.empty());
        when(clusterStateManager.unregisterInstance(anyString()))
                .thenReturn(Mono.empty());
        when(clusterStateManager.listenClusterMessages())
                .thenReturn(Flux.empty());

        // When & Then
        StepVerifier.create(
                clusterSyncService.start()
                        .then(clusterSyncService.stop())
                        .then(clusterSyncService.stop()) // 第二次停止应该被忽略
        )
        .verifyComplete();

        // 验证只注销了一次
        verify(clusterStateManager, times(1)).unregisterInstance(anyString());
    }
}