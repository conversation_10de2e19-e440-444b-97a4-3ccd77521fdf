package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * 加权轮询负载均衡器测试
 */
@ExtendWith(MockitoExtension.class)
class WeightedRoundRobinLoadBalancerTest {

    @Mock
    private ClusterStateManager clusterStateManager;

    private WeightedRoundRobinLoadBalancer loadBalancer;

    @BeforeEach
    void setUp() {
        loadBalancer = new WeightedRoundRobinLoadBalancer(clusterStateManager);
    }

    @Test
    void shouldSelectInstanceBasedOnWeight() {
        // Given
        Set<String> activeInstances = Set.of("instance1", "instance2");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        // 设置权重：instance1=3, instance2=1
        StepVerifier.create(loadBalancer.setInstanceWeight("instance1", 3))
            .verifyComplete();
        StepVerifier.create(loadBalancer.setInstanceWeight("instance2", 1))
            .verifyComplete();

        // When & Then - 执行多次选择，验证权重分布
        Map<String, Integer> selectionCount = new HashMap<>();
        for (int i = 0; i < 8; i++) {
            StepVerifier.create(loadBalancer.selectInstance())
                .assertNext(instance -> {
                    selectionCount.merge(instance, 1, Integer::sum);
                    assertThat(activeInstances).contains(instance);
                })
                .verifyComplete();
        }

        // instance1应该被选择更多次（权重为3）
        assertThat(selectionCount.get("instance1")).isGreaterThan(selectionCount.get("instance2"));
    }

    @Test
    void shouldHandleSingleInstance() {
        // Given
        Set<String> activeInstances = Set.of("instance1");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        // When & Then
        StepVerifier.create(loadBalancer.selectInstance())
            .expectNext("instance1")
            .verifyComplete();
    }

    @Test
    void shouldUpdateInstanceHealthAndResetWeight() {
        // Given
        Set<String> activeInstances = Set.of("instance1", "instance2");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        StepVerifier.create(loadBalancer.setInstanceWeight("instance1", 5))
            .verifyComplete();

        // When - 标记实例为不健康
        StepVerifier.create(loadBalancer.updateInstanceHealth("instance1", false))
            .verifyComplete();

        // Then - 只有健康实例应该被返回
        StepVerifier.create(loadBalancer.getHealthyInstances())
            .expectNextMatches(instances -> 
                instances.size() == 1 && instances.contains("instance2"))
            .verifyComplete();
    }

    @Test
    void shouldProvideCorrectStats() {
        // Given
        Set<String> activeInstances = Set.of("instance1", "instance2");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        StepVerifier.create(loadBalancer.setInstanceWeight("instance1", 2))
            .verifyComplete();

        // 执行一些请求
        StepVerifier.create(loadBalancer.selectInstance())
            .expectNextMatches(activeInstances::contains)
            .verifyComplete();

        // When & Then
        StepVerifier.create(loadBalancer.getStats())
            .assertNext(stats -> {
                assertThat(stats.totalRequests()).isEqualTo(1);
                assertThat(stats.currentStrategy()).isEqualTo(LoadBalancer.Strategy.WEIGHTED_ROUND_ROBIN);
                assertThat(stats.instanceWeights()).containsEntry("instance1", 2);
                assertThat(stats.instanceWeights()).containsEntry("instance2", 1); // 默认权重
            })
            .verifyComplete();
    }

    @Test
    void shouldRejectInvalidWeight() {
        // Given
        String instanceId = "instance1";

        // When & Then
        StepVerifier.create(loadBalancer.setInstanceWeight(instanceId, 0))
            .expectError(IllegalArgumentException.class)
            .verify();

        StepVerifier.create(loadBalancer.setInstanceWeight(instanceId, -1))
            .expectError(IllegalArgumentException.class)
            .verify();
    }

    @Test
    void shouldReturnDefaultWeight() {
        // Given
        String instanceId = "unknown-instance";

        // When & Then
        StepVerifier.create(loadBalancer.getInstanceWeight(instanceId))
            .expectNext(1) // 默认权重
            .verifyComplete();
    }

    @Test
    void shouldHandleEmptyInstanceList() {
        // Given
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(Set.of()));

        // When & Then
        StepVerifier.create(loadBalancer.selectInstance())
            .verifyComplete();
    }

    @Test
    void shouldResetStatsAndWeights() {
        // Given
        Set<String> activeInstances = Set.of("instance1");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        StepVerifier.create(loadBalancer.setInstanceWeight("instance1", 5))
            .verifyComplete();

        StepVerifier.create(loadBalancer.selectInstance())
            .expectNext("instance1")
            .verifyComplete();

        // When
        loadBalancer.resetStats();

        // Then
        StepVerifier.create(loadBalancer.getStats())
            .assertNext(stats -> {
                assertThat(stats.totalRequests()).isEqualTo(0);
                assertThat(stats.requestCounts()).isEmpty();
                // 权重应该保持不变
                assertThat(stats.instanceWeights()).containsEntry("instance1", 5);
            })
            .verifyComplete();
    }

    @Test
    void shouldDistributeRequestsAccordingToWeights() {
        // Given
        Set<String> activeInstances = Set.of("instance1", "instance2", "instance3");
        when(clusterStateManager.getActiveInstances()).thenReturn(Mono.just(activeInstances));

        // 设置权重：instance1=4, instance2=2, instance3=1
        StepVerifier.create(loadBalancer.setInstanceWeight("instance1", 4))
            .verifyComplete();
        StepVerifier.create(loadBalancer.setInstanceWeight("instance2", 2))
            .verifyComplete();
        StepVerifier.create(loadBalancer.setInstanceWeight("instance3", 1))
            .verifyComplete();

        // When - 执行多轮选择
        Map<String, Integer> selectionCount = new HashMap<>();
        int totalSelections = 21; // 3轮完整的权重周期 (4+2+1)*3

        for (int i = 0; i < totalSelections; i++) {
            StepVerifier.create(loadBalancer.selectInstance())
                .assertNext(instance -> {
                    selectionCount.merge(instance, 1, Integer::sum);
                    assertThat(activeInstances).contains(instance);
                })
                .verifyComplete();
        }

        // Then - 验证分布比例大致符合权重比例
        int count1 = selectionCount.getOrDefault("instance1", 0);
        int count2 = selectionCount.getOrDefault("instance2", 0);
        int count3 = selectionCount.getOrDefault("instance3", 0);

        // 权重比例应该大致为 4:2:1
        assertThat(count1).isGreaterThan(count2);
        assertThat(count2).isGreaterThan(count3);
        
        // 在完整的权重周期中，比例应该接近精确值
        double ratio1to2 = (double) count1 / count2;
        double ratio2to3 = (double) count2 / count3;
        
        assertThat(ratio1to2).isBetween(1.5, 2.5); // 应该接近2.0
        assertThat(ratio2to3).isBetween(1.5, 2.5); // 应该接近2.0
    }
}