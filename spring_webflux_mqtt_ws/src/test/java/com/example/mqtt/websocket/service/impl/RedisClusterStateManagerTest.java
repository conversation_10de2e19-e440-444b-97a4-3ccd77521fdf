package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.properties.RedisProperties;
import com.example.mqtt.websocket.service.ClusterStateManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.data.redis.listener.ReactiveRedisMessageListenerContainer;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RedisClusterStateManagerTest {

    @Mock
    private ReactiveRedisTemplate<String, Object> redisTemplate;

    @Mock
    private ReactiveValueOperations<String, Object> valueOperations;

    @Mock
    private ReactiveRedisMessageListenerContainer messageListenerContainer;

    private RedisProperties redisProperties;
    private ObjectMapper objectMapper;
    private RedisClusterStateManager clusterStateManager;

    @BeforeEach
    void setUp() {
        redisProperties = new RedisProperties(
                "localhost", 6379, null, 0,
                Duration.ofSeconds(5), Duration.ofSeconds(3),
                java.util.List.of(),
                new RedisProperties.Pool(),
                new RedisProperties.Cluster(
                        "test:instance:", Duration.ofSeconds(30),
                        Duration.ofMinutes(2), "test:messages",
                        Duration.ofSeconds(10)
                )
        );
        
        objectMapper = new ObjectMapper();
        clusterStateManager = new RedisClusterStateManager(
                redisTemplate, messageListenerContainer, redisProperties, objectMapper);

        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void shouldRegisterInstanceSuccessfully() {
        // Given
        String instanceId = "test-instance-1";
        ClusterStateManager.InstanceInfo instanceInfo = createTestInstanceInfo(instanceId);
        
        when(valueOperations.set(anyString(), any(), any(Duration.class)))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(clusterStateManager.registerInstance(instanceId, instanceInfo))
                .verifyComplete();

        verify(valueOperations).set(eq("test:instance:test-instance-1"), eq(instanceInfo), 
                eq(redisProperties.cluster().instanceExpiration()));
    }

    @Test
    void shouldUnregisterInstanceSuccessfully() {
        // Given
        String instanceId = "test-instance-1";
        
        when(redisTemplate.delete(anyString())).thenReturn(Mono.just(1L));

        // When & Then
        StepVerifier.create(clusterStateManager.unregisterInstance(instanceId))
                .verifyComplete();

        verify(redisTemplate).delete("test:instance:test-instance-1");
    }

    @Test
    void shouldUpdateHeartbeatSuccessfully() {
        // Given
        String instanceId = "test-instance-1";
        ClusterStateManager.InstanceInfo originalInfo = createTestInstanceInfo(instanceId);
        
        when(valueOperations.get(anyString())).thenReturn(Mono.just(originalInfo));
        when(valueOperations.set(anyString(), any(), any(Duration.class)))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(clusterStateManager.updateHeartbeat(instanceId))
                .verifyComplete();

        verify(valueOperations).get("test:instance:test-instance-1");
        verify(valueOperations).set(eq("test:instance:test-instance-1"), any(), 
                eq(redisProperties.cluster().instanceExpiration()));
    }

    @Test
    void shouldGetActiveInstancesSuccessfully() {
        // Given
        Set<String> expectedKeys = Set.of(
                "test:instance:instance-1",
                "test:instance:instance-2",
                "test:instance:instance-3"
        );
        
        when(redisTemplate.keys("test:instance:*"))
                .thenReturn(Flux.fromIterable(expectedKeys));

        // When & Then
        StepVerifier.create(clusterStateManager.getActiveInstances())
                .expectNext(Set.of("instance-1", "instance-2", "instance-3"))
                .verifyComplete();
    }

    @Test
    void shouldGetInstanceInfoSuccessfully() {
        // Given
        String instanceId = "test-instance-1";
        ClusterStateManager.InstanceInfo expectedInfo = createTestInstanceInfo(instanceId);
        
        when(valueOperations.get(anyString())).thenReturn(Mono.just(expectedInfo));

        // When & Then
        StepVerifier.create(clusterStateManager.getInstanceInfo(instanceId))
                .expectNext(expectedInfo)
                .verifyComplete();

        verify(valueOperations).get("test:instance:test-instance-1");
    }

    @Test
    void shouldGetAllInstancesInfoSuccessfully() {
        // Given
        Set<String> keys = Set.of(
                "test:instance:instance-1",
                "test:instance:instance-2"
        );
        
        ClusterStateManager.InstanceInfo info1 = createTestInstanceInfo("instance-1");
        ClusterStateManager.InstanceInfo info2 = createTestInstanceInfo("instance-2");
        
        when(redisTemplate.keys("test:instance:*"))
                .thenReturn(Flux.fromIterable(keys));
        when(valueOperations.get("test:instance:instance-1"))
                .thenReturn(Mono.just(info1));
        when(valueOperations.get("test:instance:instance-2"))
                .thenReturn(Mono.just(info2));

        // When & Then
        StepVerifier.create(clusterStateManager.getAllInstancesInfo())
                .expectNextMatches(map -> 
                        map.size() == 2 && 
                        map.containsKey("instance-1") && 
                        map.containsKey("instance-2"))
                .verifyComplete();
    }

    @Test
    void shouldBroadcastToClusterSuccessfully() {
        // Given
        String testMessage = "test-message";
        
        when(redisTemplate.convertAndSend(anyString(), anyString()))
                .thenReturn(Mono.just(1L));

        // When & Then
        StepVerifier.create(clusterStateManager.broadcastToCluster(testMessage))
                .verifyComplete();

        verify(redisTemplate).convertAndSend(eq("test:messages"), anyString());
    }

    @Test
    void shouldCheckInstanceHealthCorrectly() {
        // Given
        String instanceId = "test-instance-1";
        ClusterStateManager.InstanceInfo healthyInfo = new ClusterStateManager.InstanceInfo(
                instanceId, "localhost", 8080, Instant.now().minus(Duration.ofMinutes(1)),
                Instant.now().minus(Duration.ofSeconds(30)), new HashMap<>()
        );
        
        when(valueOperations.get(anyString())).thenReturn(Mono.just(healthyInfo));

        // When & Then
        StepVerifier.create(clusterStateManager.isInstanceHealthy(instanceId))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void shouldDetectUnhealthyInstance() {
        // Given
        String instanceId = "test-instance-1";
        ClusterStateManager.InstanceInfo unhealthyInfo = new ClusterStateManager.InstanceInfo(
                instanceId, "localhost", 8080, Instant.now().minus(Duration.ofHours(1)),
                Instant.now().minus(Duration.ofHours(1)), new HashMap<>()
        );
        
        when(valueOperations.get(anyString())).thenReturn(Mono.just(unhealthyInfo));

        // When & Then
        StepVerifier.create(clusterStateManager.isInstanceHealthy(instanceId))
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    void shouldCleanupExpiredInstancesSuccessfully() {
        // Given
        Set<String> keys = Set.of(
                "test:instance:healthy-instance",
                "test:instance:expired-instance"
        );
        
        ClusterStateManager.InstanceInfo healthyInfo = new ClusterStateManager.InstanceInfo(
                "healthy-instance", "localhost", 8080, Instant.now(),
                Instant.now(), new HashMap<>()
        );
        
        ClusterStateManager.InstanceInfo expiredInfo = new ClusterStateManager.InstanceInfo(
                "expired-instance", "localhost", 8081, Instant.now().minus(Duration.ofHours(1)),
                Instant.now().minus(Duration.ofHours(1)), new HashMap<>()
        );
        
        when(redisTemplate.keys("test:instance:*"))
                .thenReturn(Flux.fromIterable(keys));
        when(valueOperations.get("test:instance:healthy-instance"))
                .thenReturn(Mono.just(healthyInfo));
        when(valueOperations.get("test:instance:expired-instance"))
                .thenReturn(Mono.just(expiredInfo));
        when(redisTemplate.delete("test:instance:expired-instance"))
                .thenReturn(Mono.just(1L));

        // When & Then
        StepVerifier.create(clusterStateManager.cleanupExpiredInstances())
                .expectNext(1L)
                .verifyComplete();

        verify(redisTemplate).delete("test:instance:expired-instance");
        verify(redisTemplate, never()).delete("test:instance:healthy-instance");
    }

    private ClusterStateManager.InstanceInfo createTestInstanceInfo(String instanceId) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("version", "1.0.0");
        metadata.put("environment", "test");
        
        return new ClusterStateManager.InstanceInfo(
                instanceId,
                "localhost",
                8080,
                Instant.now().minus(Duration.ofMinutes(5)),
                Instant.now().minus(Duration.ofSeconds(10)),
                metadata
        );
    }
}