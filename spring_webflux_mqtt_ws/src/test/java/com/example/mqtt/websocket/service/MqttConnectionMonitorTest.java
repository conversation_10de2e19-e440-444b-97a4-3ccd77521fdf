package com.example.mqtt.websocket.service;

import com.example.mqtt.websocket.config.properties.MqttProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

/**
 * MQTT 连接监控服务测试类
 */
@ExtendWith(MockitoExtension.class)
class MqttConnectionMonitorTest {

    @Mock
    private MqttProperties mqttProperties;

    @Mock
    private MqttPahoClientFactory mqttClientFactory;

    @InjectMocks
    private MqttConnectionMonitor mqttConnectionMonitor;

    @BeforeEach
    void setUp() {
        when(mqttProperties.isAutomaticReconnect()).thenReturn(true);
    }

    @Test
    void shouldInitializeSuccessfully() {
        // When & Then
        assertDoesNotThrow(() -> mqttConnectionMonitor.initialize());
    }

    @Test
    void shouldCheckConnectionStatus() {
        // When & Then
        assertDoesNotThrow(() -> mqttConnectionMonitor.checkConnectionStatus());
    }

    @Test
    void shouldHandleConnectionLost() {
        // Given
        Throwable cause = new RuntimeException("Connection lost");

        // When
        mqttConnectionMonitor.onConnectionLost(cause);

        // Then
        assertThat(mqttConnectionMonitor.isConnected()).isFalse();
    }

    @Test
    void shouldHandleConnectionEstablished() {
        // When
        mqttConnectionMonitor.onConnectionEstablished();

        // Then
        assertThat(mqttConnectionMonitor.isConnected()).isTrue();
        assertThat(mqttConnectionMonitor.isReconnecting()).isFalse();
    }

    @Test
    void shouldReturnCorrectConnectionStatus() {
        // Initially should be false
        assertThat(mqttConnectionMonitor.isConnected()).isFalse();

        // After connection established
        mqttConnectionMonitor.onConnectionEstablished();
        assertThat(mqttConnectionMonitor.isConnected()).isTrue();

        // After connection lost
        mqttConnectionMonitor.onConnectionLost(new RuntimeException("Test"));
        assertThat(mqttConnectionMonitor.isConnected()).isFalse();
    }

    @Test
    void shouldHandleReconnectionState() {
        // Given
        when(mqttProperties.isAutomaticReconnect()).thenReturn(true);

        // When connection is lost
        mqttConnectionMonitor.onConnectionLost(new RuntimeException("Test"));

        // Then should be in reconnecting state
        assertThat(mqttConnectionMonitor.isReconnecting()).isTrue();

        // When connection is established
        mqttConnectionMonitor.onConnectionEstablished();

        // Then should not be reconnecting
        assertThat(mqttConnectionMonitor.isReconnecting()).isFalse();
    }

    @Test
    void shouldCleanupSuccessfully() {
        // When & Then
        assertDoesNotThrow(() -> mqttConnectionMonitor.cleanup());
    }
}