package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.properties.EncryptionProperties;
import com.example.mqtt.websocket.service.EncryptionException;
import com.example.mqtt.websocket.service.KeyManager;
import com.example.mqtt.websocket.service.KeyNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AesEncryptionServiceTest {
    
    @Mock
    private KeyManager keyManager;
    
    private EncryptionProperties encryptionProperties;
    private AesEncryptionService encryptionService;
    private SecretKey testKey;
    
    @BeforeEach
    void setUp() throws NoSuchAlgorithmException {
        encryptionProperties = new EncryptionProperties();
        encryptionProperties.setAlgorithm("AES");
        encryptionProperties.setKeyLength(256);
        encryptionProperties.setTransformation("AES/GCM/NoPadding");
        
        // Generate a test key
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(256);
        testKey = keyGenerator.generateKey();
        
        encryptionService = new AesEncryptionService(keyManager, encryptionProperties);
    }
    
    @Test
    void shouldEncryptAndDecryptData() throws Exception {
        // Given
        String keyId = "test-key";
        byte[] plaintext = "Hello, World!".getBytes();
        
        when(keyManager.getKey(keyId)).thenReturn(testKey);
        
        // When
        byte[] encrypted = encryptionService.encrypt(plaintext, keyId);
        byte[] decrypted = encryptionService.decrypt(encrypted, keyId);
        
        // Then
        assertNotNull(encrypted);
        assertNotNull(decrypted);
        assertArrayEquals(plaintext, decrypted);
        assertFalse(Arrays.equals(plaintext, encrypted)); // Ensure data is actually encrypted
    }
    
    @Test
    void shouldEncryptAndDecryptStringData() throws Exception {
        // Given
        String keyId = "test-key";
        String plaintext = "Hello, World!";
        
        when(keyManager.getKey(keyId)).thenReturn(testKey);
        
        // When
        byte[] encrypted = encryptionService.encryptString(plaintext, keyId);
        String decrypted = encryptionService.decryptToString(encrypted, keyId);
        
        // Then
        assertNotNull(encrypted);
        assertEquals(plaintext, decrypted);
    }
    
    @Test
    void shouldThrowExceptionForNullPlaintext() {
        // Given
        String keyId = "test-key";
        
        // When & Then
        assertThrows(EncryptionException.class, 
            () -> encryptionService.encrypt(null, keyId));
    }
    
    @Test
    void shouldThrowExceptionForEmptyPlaintext() {
        // Given
        String keyId = "test-key";
        byte[] emptyPlaintext = new byte[0];
        
        // When & Then
        assertThrows(EncryptionException.class, 
            () -> encryptionService.encrypt(emptyPlaintext, keyId));
    }
    
    @Test
    void shouldThrowExceptionForNullKeyId() {
        // Given
        byte[] plaintext = "Hello, World!".getBytes();
        
        // When & Then
        assertThrows(EncryptionException.class, 
            () -> encryptionService.encrypt(plaintext, null));
    }
    
    @Test
    void shouldThrowExceptionForEmptyKeyId() {
        // Given
        byte[] plaintext = "Hello, World!".getBytes();
        
        // When & Then
        assertThrows(EncryptionException.class, 
            () -> encryptionService.encrypt(plaintext, ""));
    }
    
    @Test
    void shouldThrowExceptionWhenKeyNotFound() throws KeyNotFoundException {
        // Given
        String keyId = "non-existent-key";
        byte[] plaintext = "Hello, World!".getBytes();
        
        when(keyManager.getKey(keyId)).thenThrow(new KeyNotFoundException(keyId));
        
        // When & Then
        assertThrows(EncryptionException.class, 
            () -> encryptionService.encrypt(plaintext, keyId));
    }
    
    @Test
    void shouldThrowExceptionForInvalidCiphertext() throws KeyNotFoundException {
        // Given
        String keyId = "test-key";
        byte[] invalidCiphertext = "invalid".getBytes(); // Too short
        
        when(keyManager.getKey(keyId)).thenReturn(testKey);
        
        // When & Then
        assertThrows(EncryptionException.class, 
            () -> encryptionService.decrypt(invalidCiphertext, keyId));
    }
    
    @Test
    void shouldThrowExceptionForNullCiphertext() {
        // Given
        String keyId = "test-key";
        
        // When & Then
        assertThrows(EncryptionException.class, 
            () -> encryptionService.decrypt(null, keyId));
    }
    
    @Test
    void shouldGenerateUniqueKeyIds() {
        // When
        String keyId1 = encryptionService.generateKeyId();
        String keyId2 = encryptionService.generateKeyId();
        
        // Then
        assertNotNull(keyId1);
        assertNotNull(keyId2);
        assertNotEquals(keyId1, keyId2);
        assertTrue(keyId1.startsWith("key-"));
        assertTrue(keyId2.startsWith("key-"));
    }
    
    @Test
    void shouldCheckKeyExists() {
        // Given
        String keyId = "test-key";
        when(keyManager.keyExists(keyId)).thenReturn(true);
        
        // When
        boolean result = encryptionService.keyExists(keyId);
        
        // Then
        assertTrue(result);
        verify(keyManager).keyExists(keyId);
    }
    
    @Test
    void shouldReturnFalseForNullKeyId() {
        // When
        boolean result = encryptionService.keyExists(null);
        
        // Then
        assertFalse(result);
        verify(keyManager, never()).keyExists(anyString());
    }
    
    @Test
    void shouldReturnFalseForEmptyKeyId() {
        // When
        boolean result = encryptionService.keyExists("");
        
        // Then
        assertFalse(result);
        verify(keyManager, never()).keyExists(anyString());
    }
    
    @Test
    void shouldGetCurrentKeyId() {
        // When
        String currentKeyId = encryptionService.getCurrentKeyId();
        
        // Then
        assertNotNull(currentKeyId);
        assertTrue(currentKeyId.startsWith("default-"));
    }
    
    @Test
    void shouldSetCurrentKeyId() {
        // Given
        String newKeyId = "new-key";
        when(keyManager.keyExists(newKeyId)).thenReturn(true);
        
        // When
        encryptionService.setCurrentKeyId(newKeyId);
        String currentKeyId = encryptionService.getCurrentKeyId();
        
        // Then
        assertEquals(newKeyId, currentKeyId);
    }
    
    @Test
    void shouldNotSetNonExistentKeyAsCurrent() {
        // Given
        String nonExistentKeyId = "non-existent-key";
        String originalKeyId = encryptionService.getCurrentKeyId();
        when(keyManager.keyExists(nonExistentKeyId)).thenReturn(false);
        
        // When
        encryptionService.setCurrentKeyId(nonExistentKeyId);
        String currentKeyId = encryptionService.getCurrentKeyId();
        
        // Then
        assertEquals(originalKeyId, currentKeyId); // Should remain unchanged
    }
    
    @Test
    void shouldHandleKeyRotation() {
        // Given
        String oldKeyId = encryptionService.getCurrentKeyId();
        when(keyManager.shouldRotateKey(oldKeyId)).thenReturn(true);
        when(keyManager.keyExists(anyString())).thenReturn(true);
        
        // When
        String currentKeyId = encryptionService.getCurrentKeyId();
        
        // Then
        assertNotEquals(oldKeyId, currentKeyId);
        assertTrue(currentKeyId.startsWith("key-"));
    }
    
    @Test
    void shouldEncryptLargeData() throws Exception {
        // Given
        String keyId = "test-key";
        byte[] largePlaintext = new byte[10000]; // 10KB of data
        Arrays.fill(largePlaintext, (byte) 'A');
        
        when(keyManager.getKey(keyId)).thenReturn(testKey);
        
        // When
        byte[] encrypted = encryptionService.encrypt(largePlaintext, keyId);
        byte[] decrypted = encryptionService.decrypt(encrypted, keyId);
        
        // Then
        assertArrayEquals(largePlaintext, decrypted);
    }
    
    @Test
    void shouldProduceDifferentCiphertextForSamePlaintext() throws Exception {
        // Given
        String keyId = "test-key";
        byte[] plaintext = "Hello, World!".getBytes();
        
        when(keyManager.getKey(keyId)).thenReturn(testKey);
        
        // When
        byte[] encrypted1 = encryptionService.encrypt(plaintext, keyId);
        byte[] encrypted2 = encryptionService.encrypt(plaintext, keyId);
        
        // Then
        assertFalse(Arrays.equals(encrypted1, encrypted2)); // Different due to random IV
        
        // But both should decrypt to the same plaintext
        byte[] decrypted1 = encryptionService.decrypt(encrypted1, keyId);
        byte[] decrypted2 = encryptionService.decrypt(encrypted2, keyId);
        assertArrayEquals(plaintext, decrypted1);
        assertArrayEquals(plaintext, decrypted2);
    }
}