package com.example.mqtt.websocket.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AlertService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class AlertServiceTest {
    
    @Mock
    private MeterRegistry meterRegistry;
    
    @Mock
    private AlertService.AlertProperties alertProperties;
    
    @Mock
    private Counter mockCounter;
    
    @InjectMocks
    private AlertService alertService;
    
    @BeforeEach
    void setUp() {
        when(meterRegistry.counter(anyString())).thenReturn(mockCounter);
        when(meterRegistry.counter(anyString(), anyString(), anyString())).thenReturn(mockCounter);
        
        // Mock alert properties
        AlertService.AlertProperties.Email email = new AlertService.AlertProperties.Email();
        AlertService.AlertProperties.Sms sms = new AlertService.AlertProperties.Sms();
        AlertService.AlertProperties.Slack slack = new AlertService.AlertProperties.Slack();
        
        when(alertProperties.getEmail()).thenReturn(email);
        when(alertProperties.getSms()).thenReturn(sms);
        when(alertProperties.getSlack()).thenReturn(slack);
        
        alertService.initMetrics();
    }
    
    @Test
    void shouldSendCriticalAlert() {
        // Given
        String title = "Critical Error";
        String message = "System is down";
        
        // When
        alertService.sendAlert(AlertService.AlertLevel.CRITICAL, title, message);
        
        // Then
        verify(mockCounter, times(2)).increment(); // One for total, one for level
    }
    
    @Test
    void shouldSendHighAlert() {
        // Given
        String title = "High Priority Issue";
        String message = "Service degraded";
        
        // When
        alertService.sendAlert(AlertService.AlertLevel.HIGH, title, message);
        
        // Then
        verify(mockCounter, times(2)).increment();
    }
    
    @Test
    void shouldSendMediumAlert() {
        // Given
        String title = "Medium Priority Issue";
        String message = "Performance issue detected";
        
        // When
        alertService.sendAlert(AlertService.AlertLevel.MEDIUM, title, message);
        
        // Then
        verify(mockCounter, times(2)).increment();
    }
    
    @Test
    void shouldSendLowAlert() {
        // Given
        String title = "Low Priority Issue";
        String message = "Minor issue detected";
        
        // When
        alertService.sendAlert(AlertService.AlertLevel.LOW, title, message);
        
        // Then
        verify(mockCounter, times(2)).increment();
    }
    
    @Test
    void shouldSendInfoAlert() {
        // Given
        String title = "Information";
        String message = "System status update";
        
        // When
        alertService.sendAlert(AlertService.AlertLevel.INFO, title, message);
        
        // Then
        verify(mockCounter, times(2)).increment();
    }
    
    @Test
    void shouldSendAlertWithTag() {
        // Given
        String title = "Tagged Alert";
        String message = "Alert with tag";
        String tag = "TEST_TAG";
        
        // When
        alertService.sendAlert(AlertService.AlertLevel.MEDIUM, title, message, tag);
        
        // Then
        verify(mockCounter, times(2)).increment();
    }
    
    @Test
    void shouldHandleAlertSendingException() {
        // Given
        when(mockCounter.increment()).thenThrow(new RuntimeException("Metrics error"));
        
        // When & Then
        assertDoesNotThrow(() -> {
            alertService.sendAlert(AlertService.AlertLevel.CRITICAL, "Test", "Test message");
        });
    }
    
    @Test
    void shouldCreateAlertWithAllFields() {
        // When
        AlertService.Alert alert = new AlertService.Alert(
            AlertService.AlertLevel.HIGH, 
            "Test Title", 
            "Test Message", 
            "TEST_TAG"
        );
        
        // Then
        assertEquals(AlertService.AlertLevel.HIGH, alert.getLevel());
        assertEquals("Test Title", alert.getTitle());
        assertEquals("Test Message", alert.getMessage());
        assertEquals("TEST_TAG", alert.getTag());
        assertNotNull(alert.getTimestamp());
        assertNotNull(alert.toString());
    }
    
    @Test
    void shouldCreateAlertWithoutTag() {
        // When
        AlertService.Alert alert = new AlertService.Alert(
            AlertService.AlertLevel.INFO, 
            "Test Title", 
            "Test Message", 
            null
        );
        
        // Then
        assertEquals(AlertService.AlertLevel.INFO, alert.getLevel());
        assertEquals("Test Title", alert.getTitle());
        assertEquals("Test Message", alert.getMessage());
        assertNull(alert.getTag());
    }
    
    @Test
    void shouldConfigureEmailProperties() {
        // Given
        AlertService.AlertProperties.Email email = new AlertService.AlertProperties.Email();
        
        // When
        email.setEnabled(true);
        email.setSmtpHost("smtp.test.com");
        email.setSmtpPort(587);
        email.setUsername("<EMAIL>");
        email.setPassword("password");
        email.setRecipients(new String[]{"<EMAIL>"});
        
        // Then
        assertTrue(email.isEnabled());
        assertEquals("smtp.test.com", email.getSmtpHost());
        assertEquals(587, email.getSmtpPort());
        assertEquals("<EMAIL>", email.getUsername());
        assertEquals("password", email.getPassword());
        assertArrayEquals(new String[]{"<EMAIL>"}, email.getRecipients());
    }
    
    @Test
    void shouldConfigureSmsProperties() {
        // Given
        AlertService.AlertProperties.Sms sms = new AlertService.AlertProperties.Sms();
        
        // When
        sms.setEnabled(true);
        sms.setApiKey("test-key");
        sms.setApiSecret("test-secret");
        sms.setPhoneNumbers(new String[]{"+1234567890"});
        
        // Then
        assertTrue(sms.isEnabled());
        assertEquals("test-key", sms.getApiKey());
        assertEquals("test-secret", sms.getApiSecret());
        assertArrayEquals(new String[]{"+1234567890"}, sms.getPhoneNumbers());
    }
    
    @Test
    void shouldConfigureSlackProperties() {
        // Given
        AlertService.AlertProperties.Slack slack = new AlertService.AlertProperties.Slack();
        
        // When
        slack.setEnabled(true);
        slack.setWebhookUrl("https://hooks.slack.com/test");
        slack.setChannel("#alerts");
        
        // Then
        assertTrue(slack.isEnabled());
        assertEquals("https://hooks.slack.com/test", slack.getWebhookUrl());
        assertEquals("#alerts", slack.getChannel());
    }
    
    @Test
    void shouldHaveDefaultPropertiesDisabled() {
        // Given
        AlertService.AlertProperties properties = new AlertService.AlertProperties();
        
        // Then
        assertFalse(properties.getEmail().isEnabled());
        assertFalse(properties.getSms().isEnabled());
        assertFalse(properties.getSlack().isEnabled());
    }
}