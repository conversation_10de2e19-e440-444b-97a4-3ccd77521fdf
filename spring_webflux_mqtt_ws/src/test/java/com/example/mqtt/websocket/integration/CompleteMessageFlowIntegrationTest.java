package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.EncryptionService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.eclipse.paho.client.mqttv3.*;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.socket.*;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 完整消息流转集成测试
 * 测试 MQTT -> WebSocket 和 WebSocket -> MQTT 的完整流程
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.websocket.endpoint=/ws",
    "app.websocket.allowed-origins=*",
    "app.encryption.enabled=true",
    "logging.level.com.example.mqtt.websocket=DEBUG"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class CompleteMessageFlowIntegrationTest {
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private EncryptionService encryptionService;
    
    @Autowired
    private ClusterStateManager clusterStateManager;
    
    private WebSocketSession webSocketSession;
    private MqttClient mqttClient;
    private ObjectMapper objectMapper;
    private CountDownLatch messageLatch;
    private AtomicReference<String> receivedWebSocketMessage;
    private AtomicReference<String> receivedMqttMessage;
    private AtomicInteger messageCounter;
    
    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        messageLatch = new CountDownLatch(1);
        receivedWebSocketMessage = new AtomicReference<>();
        receivedMqttMessage = new AtomicReference<>();
        messageCounter = new AtomicInteger(0);
        
        // 设置 MQTT 客户端
        setupMqttClient();
        
        // 设置 WebSocket 客户端
        setupWebSocketClient();
        
        // 等待连接建立
        Thread.sleep(2000);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (webSocketSession != null && webSocketSession.isOpen()) {
            webSocketSession.close();
        }
        if (mqttClient != null && mqttClient.isConnected()) {
            mqttClient.disconnect();
            mqttClient.close();
        }
    }
    
    private void setupMqttClient() throws Exception {
        String clientId = "test-client-" + System.currentTimeMillis();
        mqttClient = new MqttClient("tcp://localhost:1883", clientId);
        
        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(true);
        options.setConnectionTimeout(10);
        options.setKeepAliveInterval(30);
        
        mqttClient.setCallback(new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                System.err.println("MQTT connection lost: " + cause.getMessage());
            }
            
            @Override
            public void messageArrived(String topic, MqttMessage message) throws Exception {
                String payload = new String(message.getPayload(), StandardCharsets.UTF_8);
                System.out.println("Received MQTT message on topic " + topic + ": " + payload);
                receivedMqttMessage.set(payload);
                messageLatch.countDown();
            }
            
            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                System.out.println("MQTT message delivery complete");
            }
        });
        
        mqttClient.connect(options);
        
        // 订阅测试主题
        mqttClient.subscribe("test/response/+/data", 1);
        mqttClient.subscribe("test/notification/+", 1);
    }
    
    private void setupWebSocketClient() throws Exception {
        WebSocketClient client = new StandardWebSocketClient();
        URI uri = URI.create("ws://localhost:" + port + "/ws?userId=testUser123");
        
        WebSocketHandler handler = new WebSocketHandler() {
            @Override
            public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                System.out.println("WebSocket connection established: " + session.getId());
            }
            
            @Override
            public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                String payload = message.getPayload().toString();
                System.out.println("Received WebSocket message: " + payload);
                receivedWebSocketMessage.set(payload);
                messageCounter.incrementAndGet();
                messageLatch.countDown();
            }
            
            @Override
            public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                System.err.println("WebSocket transport error: " + exception.getMessage());
                exception.printStackTrace();
            }
            
            @Override
            public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
                System.out.println("WebSocket connection closed: " + closeStatus);
            }
            
            @Override
            public boolean supportsPartialMessages() {
                return false;
            }
        };
        
        webSocketSession = client.doHandshake(handler, null, uri).get(10, TimeUnit.SECONDS);
        
        // 等待连接确认消息
        assertTrue(messageLatch.await(10, TimeUnit.SECONDS), "Connection confirmation not received");
        
        // 重置计数器
        messageLatch = new CountDownLatch(1);
        receivedWebSocketMessage.set(null);
    }
    
    @Test
    @Order(1)
    void testMqttToWebSocketMessageFlow() throws Exception {
        System.out.println("Testing MQTT -> WebSocket message flow");
        
        // 准备测试消息
        String testPayload = objectMapper.writeValueAsString(Map.of(
            "id", "msg-" + System.currentTimeMillis(),
            "timestamp", System.currentTimeMillis(),
            "type", "sensor_data",
            "source", "mqtt",
            "data", Map.of(
                "temperature", 25.5,
                "humidity", 60.0,
                "deviceId", "sensor-001"
            )
        ));
        
        // 通过 MQTT 发送消息到系统
        MqttMessage mqttMessage = new MqttMessage(testPayload.getBytes(StandardCharsets.UTF_8));
        mqttMessage.setQos(1);
        mqttClient.publish("test/sensor/001/data", mqttMessage);
        
        // 等待消息通过 WebSocket 接收
        boolean messageReceived = messageLatch.await(15, TimeUnit.SECONDS);
        assertTrue(messageReceived, "Message should be received via WebSocket");
        
        // 验证接收到的消息
        String receivedMessage = receivedWebSocketMessage.get();
        assertNotNull(receivedMessage, "WebSocket message should not be null");
        
        JsonNode receivedJson = objectMapper.readTree(receivedMessage);
        assertEquals("sensor_data", receivedJson.get("type").asText());
        assertTrue(receivedJson.has("data"));
        
        System.out.println("MQTT -> WebSocket flow test completed successfully");
    }
    
    @Test
    @Order(2)
    void testWebSocketToMqttMessageFlow() throws Exception {
        System.out.println("Testing WebSocket -> MQTT message flow");
        
        // 重置消息接收器
        messageLatch = new CountDownLatch(1);
        receivedMqttMessage.set(null);
        
        // 准备 WebSocket 消息
        String webSocketMessage = objectMapper.writeValueAsString(Map.of(
            "type", "device_command",
            "content", Map.of(
                "action", "turn_on",
                "deviceId", "device-123",
                "timestamp", System.currentTimeMillis()
            ),
            "headers", Map.of(
                "mqtt.targetTopic", "test/response/123/data"
            )
        ));
        
        // 通过 WebSocket 发送消息
        webSocketSession.sendMessage(new TextMessage(webSocketMessage));
        
        // 等待消息通过 MQTT 接收
        boolean messageReceived = messageLatch.await(15, TimeUnit.SECONDS);
        assertTrue(messageReceived, "Message should be received via MQTT");
        
        // 验证接收到的消息
        String receivedMessage = receivedMqttMessage.get();
        assertNotNull(receivedMessage, "MQTT message should not be null");
        
        JsonNode receivedJson = objectMapper.readTree(receivedMessage);
        assertTrue(receivedJson.has("content"));
        
        System.out.println("WebSocket -> MQTT flow test completed successfully");
    }
    
    @Test
    @Order(3)
    void testEncryptedMessageFlow() throws Exception {
        System.out.println("Testing encrypted message flow");
        
        // 重置消息接收器
        messageLatch = new CountDownLatch(1);
        receivedWebSocketMessage.set(null);
        
        // 准备加密消息
        String originalPayload = objectMapper.writeValueAsString(Map.of(
            "id", "encrypted-msg-" + System.currentTimeMillis(),
            "type", "sensitive_data",
            "data", Map.of(
                "userId", "user-456",
                "balance", 1000.50,
                "accountNumber", "ACC-*********"
            )
        ));
        
        // 加密消息
        String keyId = encryptionService.generateKeyId();
        byte[] encryptedData = encryptionService.encrypt(originalPayload.getBytes(StandardCharsets.UTF_8), keyId);
        
        String encryptedMessage = objectMapper.writeValueAsString(Map.of(
            "id", "encrypted-wrapper-" + System.currentTimeMillis(),
            "timestamp", System.currentTimeMillis(),
            "type", "encrypted_message",
            "encrypted", true,
            "keyId", keyId,
            "payload", java.util.Base64.getEncoder().encodeToString(encryptedData)
        ));
        
        // 通过 MQTT 发送加密消息
        MqttMessage mqttMessage = new MqttMessage(encryptedMessage.getBytes(StandardCharsets.UTF_8));
        mqttMessage.setQos(1);
        mqttClient.publish("test/sensor/encrypted/data", mqttMessage);
        
        // 等待解密后的消息通过 WebSocket 接收
        boolean messageReceived = messageLatch.await(15, TimeUnit.SECONDS);
        assertTrue(messageReceived, "Encrypted message should be decrypted and received");
        
        // 验证消息已解密
        String receivedMessage = receivedWebSocketMessage.get();
        assertNotNull(receivedMessage, "Decrypted message should not be null");
        
        JsonNode receivedJson = objectMapper.readTree(receivedMessage);
        assertEquals("sensitive_data", receivedJson.get("type").asText());
        assertTrue(receivedJson.has("data"));
        
        System.out.println("Encrypted message flow test completed successfully");
    }
    
    @Test
    @Order(4)
    void testBidirectionalMessageExchange() throws Exception {
        System.out.println("Testing bidirectional message exchange");
        
        // 测试多轮消息交换
        for (int round = 1; round <= 3; round++) {
            System.out.println("Round " + round + " of bidirectional exchange");
            
            // 重置接收器
            messageLatch = new CountDownLatch(2); // 期望接收两个消息
            receivedWebSocketMessage.set(null);
            receivedMqttMessage.set(null);
            
            // 1. WebSocket -> MQTT
            String wsToMqttMessage = objectMapper.writeValueAsString(Map.of(
                "type", "ping",
                "round", round,
                "timestamp", System.currentTimeMillis(),
                "headers", Map.of("mqtt.targetTopic", "test/notification/ping")
            ));
            
            webSocketSession.sendMessage(new TextMessage(wsToMqttMessage));
            
            // 2. MQTT -> WebSocket (模拟响应)
            String mqttToWsMessage = objectMapper.writeValueAsString(Map.of(
                "type", "pong",
                "round", round,
                "timestamp", System.currentTimeMillis(),
                "responseToRound", round
            ));
            
            Thread.sleep(1000); // 给第一个消息处理时间
            
            MqttMessage responseMessage = new MqttMessage(mqttToWsMessage.getBytes(StandardCharsets.UTF_8));
            responseMessage.setQos(1);
            mqttClient.publish("test/sensor/pong/data", responseMessage);
            
            // 等待两个消息都被处理
            boolean bothReceived = messageLatch.await(10, TimeUnit.SECONDS);
            assertTrue(bothReceived, "Both messages should be received in round " + round);
            
            Thread.sleep(500); // 轮次间隔
        }
        
        System.out.println("Bidirectional message exchange test completed successfully");
    }
    
    @Test
    @Order(5)
    void testMessageValidationAndErrorHandling() throws Exception {
        System.out.println("Testing message validation and error handling");
        
        // 重置接收器
        messageLatch = new CountDownLatch(1);
        receivedWebSocketMessage.set(null);
        
        // 测试无效 JSON 消息
        String invalidJson = "{ invalid json format }";
        webSocketSession.sendMessage(new TextMessage(invalidJson));
        
        // 等待错误响应
        boolean errorReceived = messageLatch.await(5, TimeUnit.SECONDS);
        
        if (errorReceived) {
            String errorResponse = receivedWebSocketMessage.get();
            assertNotNull(errorResponse);
            
            JsonNode errorJson = objectMapper.readTree(errorResponse);
            assertEquals("error", errorJson.get("type").asText());
            assertTrue(errorJson.get("message").asText().contains("Invalid"));
        }
        
        // 验证连接仍然活跃
        assertTrue(webSocketSession.isOpen(), "WebSocket should remain open after error");
        
        // 测试缺少必需字段的消息
        messageLatch = new CountDownLatch(1);
        receivedWebSocketMessage.set(null);
        
        String incompleteMessage = objectMapper.writeValueAsString(Map.of(
            "content", "Missing type field"
        ));
        
        webSocketSession.sendMessage(new TextMessage(incompleteMessage));
        
        // 等待验证错误响应
        boolean validationErrorReceived = messageLatch.await(5, TimeUnit.SECONDS);
        
        if (validationErrorReceived) {
            String validationErrorResponse = receivedWebSocketMessage.get();
            assertNotNull(validationErrorResponse);
            
            JsonNode validationErrorJson = objectMapper.readTree(validationErrorResponse);
            assertEquals("error", validationErrorJson.get("type").asText());
        }
        
        System.out.println("Message validation and error handling test completed successfully");
    }
    
    @Test
    @Order(6)
    void testHighThroughputMessageProcessing() throws Exception {
        System.out.println("Testing high throughput message processing");
        
        int messageCount = 50;
        CountDownLatch throughputLatch = new CountDownLatch(messageCount);
        AtomicInteger processedMessages = new AtomicInteger(0);
        
        // 设置消息计数器
        WebSocketHandler throughputHandler = new WebSocketHandler() {
            @Override
            public void afterConnectionEstablished(WebSocketSession session) throws Exception {}
            
            @Override
            public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                processedMessages.incrementAndGet();
                throughputLatch.countDown();
            }
            
            @Override
            public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {}
            
            @Override
            public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {}
            
            @Override
            public boolean supportsPartialMessages() {
                return false;
            }
        };
        
        // 发送大量消息
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        for (int i = 0; i < messageCount; i++) {
            final int messageIndex = i;
            executor.submit(() -> {
                try {
                    String message = objectMapper.writeValueAsString(Map.of(
                        "type", "throughput_test",
                        "index", messageIndex,
                        "timestamp", System.currentTimeMillis(),
                        "data", "Test message " + messageIndex
                    ));
                    
                    MqttMessage mqttMessage = new MqttMessage(message.getBytes(StandardCharsets.UTF_8));
                    mqttMessage.setQos(0); // 使用 QoS 0 提高吞吐量
                    mqttClient.publish("test/sensor/throughput/data", mqttMessage);
                    
                } catch (Exception e) {
                    e.printStackTrace();
                    throughputLatch.countDown();
                }
            });
        }
        
        // 等待所有消息处理完成
        boolean allProcessed = throughputLatch.await(30, TimeUnit.SECONDS);
        assertTrue(allProcessed, "All messages should be processed within timeout");
        
        executor.shutdown();
        
        System.out.println("Processed " + processedMessages.get() + " out of " + messageCount + " messages");
        assertTrue(processedMessages.get() >= messageCount * 0.9, "At least 90% of messages should be processed");
        
        System.out.println("High throughput message processing test completed successfully");
    }
}