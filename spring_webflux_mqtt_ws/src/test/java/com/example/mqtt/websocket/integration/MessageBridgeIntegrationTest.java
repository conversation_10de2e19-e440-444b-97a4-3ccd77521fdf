package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.MqttMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import com.example.mqtt.websocket.service.MessageBridgeService;
import com.example.mqtt.websocket.service.MessageConverter;
import com.example.mqtt.websocket.service.WebSocketSessionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.socket.*;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.URI;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 消息桥接集成测试
 * 测试 MQTT 和 WebSocket 之间的端到端消息流转
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.websocket.endpoint=/ws",
    "app.websocket.allowed-origins=*"
})
class MessageBridgeIntegrationTest {
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private MessageBridgeService messageBridgeService;
    
    @Autowired
    private MessageConverter messageConverter;
    
    @Autowired
    private WebSocketSessionManager sessionManager;
    
    @Autowired
    private MessageChannel mqttInboundChannel;
    
    @Autowired
    private MessageChannel webSocketInboundChannel;
    
    private ObjectMapper objectMapper;
    private WebSocketSession testWebSocketSession;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }
    
    @Test
    void testMqttToWebSocketMessageFlow() throws Exception {
        // 准备测试数据
        String testTopic = "sensor/temperature/data";
        String testPayload = "{\"temperature\":25.5,\"humidity\":60.0,\"timestamp\":**********}";
        
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType("sensor_data")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8(testPayload))
                .build();
        
        MqttMessage mqttMessage = MqttMessage.newBuilder()
                .setTopic(testTopic)
                .setQos(1)
                .setRetained(false)
                .setMessage(baseMessage)
                .build();
        
        // 测试 MQTT 消息转发到 WebSocket
        StepVerifier.create(messageBridgeService.forwardMqttToWebSocket(mqttMessage))
                .verifyComplete();
    }
    
    @Test
    void testWebSocketToMqttMessageFlow() throws Exception {
        // 准备测试数据
        String testSessionId = "test-session-123";
        String testUserId = "user-456";
        String testPayload = "{\"command\":\"turn_on\",\"deviceId\":\"device-789\"}";
        
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType("device_command")
                .setSource("websocket")
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8(testPayload))
                .putHeaders("mqtt.targetTopic", "device/command/device-789")
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setSessionId(testSessionId)
                .setUserId(testUserId)
                .setType(WebSocketMessage.MessageType.UNICAST)
                .setMessage(baseMessage)
                .build();
        
        String targetTopic = "device/command/device-789";
        
        // 测试 WebSocket 消息转发到 MQTT
        StepVerifier.create(messageBridgeService.forwardWebSocketToMqtt(wsMessage, targetTopic))
                .verifyComplete();
    }
    
    @Test
    void testBidirectionalMessageFlow() throws Exception {
        // 测试双向消息流转
        String mqttTopic = "system/broadcast";
        String originalPayload = "{\"type\":\"system_notification\",\"message\":\"System maintenance scheduled\"}";
        
        // 1. 模拟 MQTT 消息接收
        byte[] mqttPayload = messageConverter.convertToBytes(
            BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType("system_notification")
                .setSource("system")
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8(originalPayload))
                .build()
        );
        
        StepVerifier.create(messageBridgeService.handleMqttMessage(mqttTopic, mqttPayload, 1, false))
                .verifyComplete();
        
        // 2. 模拟 WebSocket 消息接收
        String wsMessageData = "{\"type\":\"device_command\",\"content\":\"{\\\"action\\\":\\\"restart\\\"}\",\"headers\":{\"mqtt.targetTopic\":\"device/restart\"}}";
        String sessionId = "test-session-bidirectional";
        
        StepVerifier.create(messageBridgeService.handleWebSocketMessage(sessionId, wsMessageData))
                .verifyComplete();
    }
    
    @Test
    void testEncryptedMessageFlow() throws Exception {
        // 测试加密消息流转
        String sensitivePayload = "{\"creditCard\":\"1234-**************\",\"cvv\":\"123\"}";
        
        BaseMessage sensitiveMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType("sensitive_data")
                .setSource("websocket")
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8(sensitivePayload))
                .putHeaders("encrypt", "true")
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setSessionId("test-session-encrypted")
                .setType(WebSocketMessage.MessageType.UNICAST)
                .setMessage(sensitiveMessage)
                .build();
        
        String targetTopic = "secure/data";
        
        // 测试加密消息处理
        StepVerifier.create(messageBridgeService.forwardWebSocketToMqtt(wsMessage, targetTopic))
                .verifyComplete();
    }
    
    @Test
    void testMessageRoutingRules() throws Exception {
        // 测试消息路由规则
        
        // 1. 测试广播消息路由
        WebSocketMessage broadcastMessage = createTestWebSocketMessage(
            WebSocketMessage.MessageType.BROADCAST, 
            "broadcast", 
            "System announcement"
        );
        
        StepVerifier.create(messageBridgeService.forwardMqttToWebSocket(
            createTestMqttMessage("system/broadcast", broadcastMessage.getMessage())))
                .verifyComplete();
        
        // 2. 测试单播消息路由
        WebSocketMessage unicastMessage = createTestWebSocketMessage(
            WebSocketMessage.MessageType.UNICAST, 
            "private_message", 
            "Personal notification"
        );
        unicastMessage = unicastMessage.toBuilder().setUserId("target-user-123").build();
        
        StepVerifier.create(messageBridgeService.forwardMqttToWebSocket(
            createTestMqttMessage("user/target-user-123/notification", unicastMessage.getMessage())))
                .verifyComplete();
        
        // 3. 测试系统消息路由
        WebSocketMessage systemMessage = createTestWebSocketMessage(
            WebSocketMessage.MessageType.SYSTEM, 
            "system_status", 
            "System health check"
        );
        
        StepVerifier.create(messageBridgeService.forwardMqttToWebSocket(
            createTestMqttMessage("system/status", systemMessage.getMessage())))
                .verifyComplete();
    }
    
    @Test
    void testMessageConversionAndValidation() throws Exception {
        // 测试消息转换和验证
        
        // 1. 测试有效消息格式
        String validMessage = "{\"type\":\"test_message\",\"content\":\"Hello World\",\"timestamp\":**********}";
        assertTrue(messageConverter.isValidMessageFormat(validMessage));
        
        WebSocketMessage parsedMessage = messageConverter.parseWebSocketMessage(validMessage);
        assertNotNull(parsedMessage);
        assertEquals("test_message", parsedMessage.getMessage().getType());
        
        // 2. 测试无效消息格式
        String invalidMessage = "invalid json format";
        assertFalse(messageConverter.isValidMessageFormat(invalidMessage));
        
        // 3. 测试消息类型提取
        assertEquals("test_message", messageConverter.extractMessageType(validMessage));
        assertEquals("unknown", messageConverter.extractMessageType(invalidMessage));
    }
    
    @Test
    void testErrorHandling() throws Exception {
        // 测试错误处理
        
        // 1. 测试空消息处理
        StepVerifier.create(messageBridgeService.handleMqttMessage("test/topic", new byte[0], 0, false))
                .verifyComplete();
        
        // 2. 测试无效 JSON 处理
        StepVerifier.create(messageBridgeService.handleWebSocketMessage("test-session", "invalid json"))
                .verifyComplete();
        
        // 3. 测试缺失目标主题的处理
        String messageWithoutTopic = "{\"type\":\"unknown_type\",\"content\":\"test content\"}";
        StepVerifier.create(messageBridgeService.handleWebSocketMessage("test-session", messageWithoutTopic))
                .verifyComplete();
    }
    
    @Test
    void testConcurrentMessageProcessing() throws Exception {
        // 测试并发消息处理
        int messageCount = 10;
        CountDownLatch latch = new CountDownLatch(messageCount);
        
        for (int i = 0; i < messageCount; i++) {
            final int messageIndex = i;
            
            Mono.fromRunnable(() -> {
                try {
                    String topic = "concurrent/test/" + messageIndex;
                    String payload = "{\"index\":" + messageIndex + ",\"data\":\"test data\"}";
                    byte[] payloadBytes = messageConverter.convertToBytes(
                        BaseMessage.newBuilder()
                            .setId(UUID.randomUUID().toString())
                            .setTimestamp(System.currentTimeMillis())
                            .setType("concurrent_test")
                            .setSource("test")
                            .setPayload(com.google.protobuf.ByteString.copyFromUtf8(payload))
                            .build()
                    );
                    
                    messageBridgeService.handleMqttMessage(topic, payloadBytes, 1, false)
                        .doFinally(signal -> latch.countDown())
                        .subscribe();
                        
                } catch (Exception e) {
                    latch.countDown();
                }
            }).subscribe();
        }
        
        // 等待所有消息处理完成
        assertTrue(latch.await(10, TimeUnit.SECONDS), "Not all concurrent messages were processed");
    }
    
    /**
     * 创建测试用的 WebSocket 消息
     */
    private WebSocketMessage createTestWebSocketMessage(WebSocketMessage.MessageType type, String messageType, String content) {
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(System.currentTimeMillis())
                .setType(messageType)
                .setSource("test")
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8(content))
                .build();
        
        return WebSocketMessage.newBuilder()
                .setType(type)
                .setMessage(baseMessage)
                .build();
    }
    
    /**
     * 创建测试用的 MQTT 消息
     */
    private MqttMessage createTestMqttMessage(String topic, BaseMessage baseMessage) {
        return MqttMessage.newBuilder()
                .setTopic(topic)
                .setQos(1)
                .setRetained(false)
                .setMessage(baseMessage)
                .build();
    }
}