package com.example.mqtt.websocket.handler;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import com.example.mqtt.websocket.service.WebSocketSessionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.web.reactive.socket.HandshakeInfo;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.URI;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * ReactiveWebSocketHandler 单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ReactiveWebSocketHandlerTest {
    
    @Mock
    private WebSocketSessionManager sessionManager;
    
    @Mock
    private WebSocketSession mockSession;
    
    @Mock
    private HandshakeInfo handshakeInfo;
    
    private ReactiveWebSocketHandler webSocketHandler;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        webSocketHandler = new ReactiveWebSocketHandler(sessionManager, objectMapper);
    }
    
    @Test
    void shouldHandleWebSocketConnection() {
        // Given
        String sessionId = "test-session-id";
        URI uri = URI.create("ws://localhost:8080/ws");
        
        when(mockSession.getId()).thenReturn(sessionId);
        when(mockSession.getHandshakeInfo()).thenReturn(handshakeInfo);
        when(handshakeInfo.getUri()).thenReturn(uri);
        when(mockSession.receive()).thenReturn(Flux.empty());
        when(mockSession.send(any())).thenReturn(Mono.empty());
        when(sessionManager.getBroadcastStream()).thenReturn(Flux.empty());
        
        // When & Then
        StepVerifier.create(webSocketHandler.handle(mockSession))
                .verifyComplete();
        
        verify(sessionManager).registerSession(eq(mockSession), any());
        verify(sessionManager).unregisterSession(sessionId);
    }
    
    @Test
    void shouldHandleBinaryMessage() {
        // Given
        String sessionId = "test-session-id";
        URI uri = URI.create("ws://localhost:8080/ws");
        
        // Create a test WebSocket message
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId("test-msg-id")
                .setTimestamp(System.currentTimeMillis())
                .setType("test")
                .setSource("client")
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setType(WebSocketMessage.MessageType.BROADCAST)
                .setMessage(baseMessage)
                .build();
        
        byte[] messageBytes = wsMessage.toByteArray();
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(messageBytes);
        
        org.springframework.web.reactive.socket.WebSocketMessage incomingMessage = 
                mock(org.springframework.web.reactive.socket.WebSocketMessage.class);
        
        when(mockSession.getId()).thenReturn(sessionId);
        when(mockSession.getHandshakeInfo()).thenReturn(handshakeInfo);
        when(handshakeInfo.getUri()).thenReturn(uri);
        when(mockSession.receive()).thenReturn(Flux.just(incomingMessage));
        when(mockSession.send(any())).thenReturn(Mono.empty());
        when(sessionManager.getBroadcastStream()).thenReturn(Flux.empty());
        when(sessionManager.broadcastMessage(any())).thenReturn(Mono.empty());
        
        when(incomingMessage.getType()).thenReturn(org.springframework.web.reactive.socket.WebSocketMessage.Type.BINARY);
        when(incomingMessage.getPayload()).thenReturn(dataBuffer);
        
        // When & Then
        StepVerifier.create(webSocketHandler.handle(mockSession))
                .verifyComplete();
        
        verify(sessionManager).registerSession(eq(mockSession), any());
        verify(sessionManager).broadcastMessage(any(WebSocketMessage.class));
        verify(sessionManager).unregisterSession(sessionId);
    }
    
    @Test
    void shouldHandleTextMessage() {
        // Given
        String sessionId = "test-session-id";
        URI uri = URI.create("ws://localhost:8080/ws");
        String textPayload = "Hello WebSocket";
        
        org.springframework.web.reactive.socket.WebSocketMessage incomingMessage = 
                mock(org.springframework.web.reactive.socket.WebSocketMessage.class);
        
        when(mockSession.getId()).thenReturn(sessionId);
        when(mockSession.getHandshakeInfo()).thenReturn(handshakeInfo);
        when(handshakeInfo.getUri()).thenReturn(uri);
        when(mockSession.receive()).thenReturn(Flux.just(incomingMessage));
        when(mockSession.send(any())).thenReturn(Mono.empty());
        when(sessionManager.getBroadcastStream()).thenReturn(Flux.empty());
        when(sessionManager.broadcastMessage(any())).thenReturn(Mono.empty());
        
        when(incomingMessage.getType()).thenReturn(org.springframework.web.reactive.socket.WebSocketMessage.Type.TEXT);
        when(incomingMessage.getPayloadAsText()).thenReturn(textPayload);
        
        // When & Then
        StepVerifier.create(webSocketHandler.handle(mockSession))
                .verifyComplete();
        
        verify(sessionManager).registerSession(eq(mockSession), any());
        verify(sessionManager).broadcastMessage(any(WebSocketMessage.class));
        verify(sessionManager).unregisterSession(sessionId);
    }
    
    @Test
    void shouldHandleSystemPingMessage() {
        // Given
        String sessionId = "test-session-id";
        URI uri = URI.create("ws://localhost:8080/ws");
        
        BaseMessage pingMessage = BaseMessage.newBuilder()
                .setId("ping-msg-id")
                .setTimestamp(System.currentTimeMillis())
                .setType("ping")
                .setSource("client")
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setSessionId(sessionId)
                .setType(WebSocketMessage.MessageType.SYSTEM)
                .setMessage(pingMessage)
                .build();
        
        byte[] messageBytes = wsMessage.toByteArray();
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(messageBytes);
        
        org.springframework.web.reactive.socket.WebSocketMessage incomingMessage = 
                mock(org.springframework.web.reactive.socket.WebSocketMessage.class);
        
        when(mockSession.getId()).thenReturn(sessionId);
        when(mockSession.getHandshakeInfo()).thenReturn(handshakeInfo);
        when(handshakeInfo.getUri()).thenReturn(uri);
        when(mockSession.receive()).thenReturn(Flux.just(incomingMessage));
        when(mockSession.send(any())).thenReturn(Mono.empty());
        when(sessionManager.getBroadcastStream()).thenReturn(Flux.empty());
        when(sessionManager.sendToSession(eq(sessionId), any())).thenReturn(Mono.empty());
        
        when(incomingMessage.getType()).thenReturn(org.springframework.web.reactive.socket.WebSocketMessage.Type.BINARY);
        when(incomingMessage.getPayload()).thenReturn(dataBuffer);
        
        // When & Then
        StepVerifier.create(webSocketHandler.handle(mockSession))
                .verifyComplete();
        
        verify(sessionManager).registerSession(eq(mockSession), any());
        verify(sessionManager).sendToSession(eq(sessionId), any(WebSocketMessage.class));
        verify(sessionManager).unregisterSession(sessionId);
    }
    
    @Test
    void shouldHandleUnicastMessage() {
        // Given
        String sessionId = "test-session-id";
        String targetUserId = "target-user-123";
        URI uri = URI.create("ws://localhost:8080/ws");
        
        BaseMessage baseMessage = BaseMessage.newBuilder()
                .setId("unicast-msg-id")
                .setTimestamp(System.currentTimeMillis())
                .setType("private")
                .setSource("client")
                .build();
        
        WebSocketMessage wsMessage = WebSocketMessage.newBuilder()
                .setUserId(targetUserId)
                .setType(WebSocketMessage.MessageType.UNICAST)
                .setMessage(baseMessage)
                .build();
        
        byte[] messageBytes = wsMessage.toByteArray();
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(messageBytes);
        
        org.springframework.web.reactive.socket.WebSocketMessage incomingMessage = 
                mock(org.springframework.web.reactive.socket.WebSocketMessage.class);
        
        when(mockSession.getId()).thenReturn(sessionId);
        when(mockSession.getHandshakeInfo()).thenReturn(handshakeInfo);
        when(handshakeInfo.getUri()).thenReturn(uri);
        when(mockSession.receive()).thenReturn(Flux.just(incomingMessage));
        when(mockSession.send(any())).thenReturn(Mono.empty());
        when(sessionManager.getBroadcastStream()).thenReturn(Flux.empty());
        when(sessionManager.sendToUser(eq(targetUserId), any())).thenReturn(Mono.empty());
        
        when(incomingMessage.getType()).thenReturn(org.springframework.web.reactive.socket.WebSocketMessage.Type.BINARY);
        when(incomingMessage.getPayload()).thenReturn(dataBuffer);
        
        // When & Then
        StepVerifier.create(webSocketHandler.handle(mockSession))
                .verifyComplete();
        
        verify(sessionManager).registerSession(eq(mockSession), any());
        verify(sessionManager).sendToUser(eq(targetUserId), any(WebSocketMessage.class));
        verify(sessionManager).unregisterSession(sessionId);
    }
    
    @Test
    void shouldHandleInvalidMessage() {
        // Given
        String sessionId = "test-session-id";
        URI uri = URI.create("ws://localhost:8080/ws");
        
        // Create invalid binary data
        byte[] invalidBytes = "invalid protobuf data".getBytes();
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(invalidBytes);
        
        org.springframework.web.reactive.socket.WebSocketMessage incomingMessage = 
                mock(org.springframework.web.reactive.socket.WebSocketMessage.class);
        
        when(mockSession.getId()).thenReturn(sessionId);
        when(mockSession.getHandshakeInfo()).thenReturn(handshakeInfo);
        when(handshakeInfo.getUri()).thenReturn(uri);
        when(mockSession.receive()).thenReturn(Flux.just(incomingMessage));
        when(mockSession.send(any())).thenReturn(Mono.empty());
        when(sessionManager.getBroadcastStream()).thenReturn(Flux.empty());
        when(sessionManager.sendToSession(eq(sessionId), any())).thenReturn(Mono.empty());
        
        when(incomingMessage.getType()).thenReturn(org.springframework.web.reactive.socket.WebSocketMessage.Type.BINARY);
        when(incomingMessage.getPayload()).thenReturn(dataBuffer);
        
        // When & Then
        StepVerifier.create(webSocketHandler.handle(mockSession))
                .verifyComplete();
        
        verify(sessionManager).registerSession(eq(mockSession), any());
        verify(sessionManager).sendToSession(eq(sessionId), any(WebSocketMessage.class)); // Error message
        verify(sessionManager).unregisterSession(sessionId);
    }
}