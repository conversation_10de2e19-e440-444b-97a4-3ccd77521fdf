package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.model.proto.MqttMessage;
import com.example.mqtt.websocket.model.proto.WebSocketMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 消息转换器测试类
 */
@ExtendWith(MockitoExtension.class)
class MessageConverterTest {

    private MessageConverter messageConverter;
    private ObjectMapper objectMapper;
    
    private BaseMessage testBaseMessage;
    private String testJson;
    private byte[] testProtobufData;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        messageConverter = new MessageConverter(objectMapper);
        
        // 创建测试用的 BaseMessage
        testBaseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("test")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8("test payload"))
                .setEncrypted(false)
                .putHeaders("key1", "value1")
                .putHeaders("key2", "value2")
                .build();
        
        testProtobufData = testBaseMessage.toByteArray();
        
        testJson = """
                {
                    "id": "test-123",
                    "timestamp": 1640995200000,
                    "type": "json",
                    "source": "test",
                    "payload": "test data",
                    "encrypted": false,
                    "headers": {
                        "key1": "value1",
                        "key2": "value2"
                    }
                }
                """;
    }

    @Test
    void shouldTransformProtobufToBaseMessage() {
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(testProtobufData, "application/x-protobuf");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(testBaseMessage.getId());
        assertThat(result.getType()).isEqualTo(testBaseMessage.getType());
        assertThat(result.getSource()).isEqualTo(testBaseMessage.getSource());
        assertThat(result.getPayload()).isEqualTo(testBaseMessage.getPayload());
    }

    @Test
    void shouldTransformJsonToBaseMessage() {
        // Given
        byte[] jsonData = testJson.getBytes();
        
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(jsonData, "application/json");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo("test-123");
        assertThat(result.getType()).isEqualTo("json");
        assertThat(result.getSource()).isEqualTo("test");
        assertThat(result.getEncrypted()).isFalse();
        assertThat(result.getHeadersMap()).containsEntry("key1", "value1");
        assertThat(result.getHeadersMap()).containsEntry("key2", "value2");
    }

    @Test
    void shouldAutoDetectProtobufFormat() {
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(testProtobufData, null);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(testBaseMessage.getId());
    }

    @Test
    void shouldAutoDetectJsonFormat() {
        // Given
        byte[] jsonData = testJson.getBytes();
        
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(jsonData, null);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getType()).isEqualTo("json");
    }

    @Test
    void shouldHandleTextMessage() {
        // Given
        String textMessage = "This is a plain text message";
        byte[] textData = textMessage.getBytes();
        
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(textData, "text/plain");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getType()).isEqualTo("text");
        assertThat(result.getSource()).isEqualTo("mqtt");
        assertThat(result.getPayload().toStringUtf8()).isEqualTo(textMessage);
    }

    @Test
    void shouldConvertBaseMessageToJson() {
        // When
        String jsonResult = messageConverter.convertToJson(testBaseMessage);
        
        // Then
        assertThat(jsonResult).isNotNull();
        assertThat(jsonResult).contains(testBaseMessage.getId());
        assertThat(jsonResult).contains(testBaseMessage.getType());
        assertThat(jsonResult).contains(testBaseMessage.getSource());
    }

    @Test
    void shouldConvertBaseMessageToProtobuf() {
        // When
        byte[] protobufResult = messageConverter.convertToProtobuf(testBaseMessage);
        
        // Then
        assertThat(protobufResult).isNotNull();
        assertThat(protobufResult).isEqualTo(testBaseMessage.toByteArray());
    }

    @Test
    void shouldConvertJsonToBaseMessage() {
        // When
        BaseMessage result = messageConverter.convertFromJson(testJson);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo("test-123");
        assertThat(result.getType()).isEqualTo("json");
    }

    @Test
    void shouldConvertProtobufToBaseMessage() {
        // When
        BaseMessage result = messageConverter.convertFromProtobuf(testProtobufData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(testBaseMessage.getId());
        assertThat(result.getType()).isEqualTo(testBaseMessage.getType());
    }

    @Test
    void shouldConvertToMqttMessage() {
        // Given
        String topic = "test/topic";
        int qos = 1;
        boolean retained = true;
        
        // When
        MqttMessage result = messageConverter.convertToMqttMessage(testBaseMessage, topic, qos, retained);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTopic()).isEqualTo(topic);
        assertThat(result.getQos()).isEqualTo(qos);
        assertThat(result.getRetained()).isEqualTo(retained);
        assertThat(result.getMessage()).isEqualTo(testBaseMessage);
    }

    @Test
    void shouldConvertToWebSocketMessage() {
        // Given
        String sessionId = "session-123";
        String userId = "user-456";
        WebSocketMessage.MessageType messageType = WebSocketMessage.MessageType.BROADCAST;
        
        // When
        WebSocketMessage result = messageConverter.convertToWebSocketMessage(
                testBaseMessage, sessionId, userId, messageType);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSessionId()).isEqualTo(sessionId);
        assertThat(result.getUserId()).isEqualTo(userId);
        assertThat(result.getType()).isEqualTo(messageType);
        assertThat(result.getMessage()).isEqualTo(testBaseMessage);
    }

    @Test
    void shouldHandleInvalidJson() {
        // Given
        String invalidJson = "{ invalid json }";
        
        // When & Then
        assertThatThrownBy(() -> messageConverter.convertFromJson(invalidJson))
                .isInstanceOf(MessageConverter.MessageConversionException.class)
                .hasMessageContaining("JSON parsing failed");
    }

    @Test
    void shouldHandleInvalidProtobuf() {
        // Given
        byte[] invalidProtobuf = "invalid protobuf data".getBytes();
        
        // When & Then
        assertThatThrownBy(() -> messageConverter.convertFromProtobuf(invalidProtobuf))
                .isInstanceOf(MessageConverter.MessageConversionException.class)
                .hasMessageContaining("Protobuf parsing failed");
    }

    @Test
    void shouldCreateErrorMessageForInvalidData() {
        // Given
        byte[] invalidData = "completely invalid data".getBytes();
        
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(invalidData, "unknown/type");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getType()).isEqualTo("text"); // 应该回退到文本处理
        assertThat(result.getSource()).isEqualTo("mqtt");
    }

    @Test
    void shouldHandleJsonWithMissingFields() {
        // Given
        String minimalJson = """
                {
                    "data": "test data"
                }
                """;
        byte[] jsonData = minimalJson.getBytes();
        
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(jsonData, "application/json");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isNotEmpty(); // 应该生成UUID
        assertThat(result.getType()).isEqualTo("json");
        assertThat(result.getSource()).isEqualTo("mqtt");
        assertThat(result.getTimestamp()).isGreaterThan(0);
    }

    @Test
    void shouldHandleJsonWithNestedPayload() {
        // Given
        String nestedJson = """
                {
                    "id": "nested-123",
                    "type": "nested",
                    "payload": {
                        "nested": "data",
                        "number": 42
                    }
                }
                """;
        byte[] jsonData = nestedJson.getBytes();
        
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(jsonData, "application/json");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo("nested-123");
        assertThat(result.getType()).isEqualTo("nested");
        assertThat(result.getPayload().toStringUtf8()).contains("nested");
        assertThat(result.getPayload().toStringUtf8()).contains("42");
    }

    @Test
    void shouldHandleMqttMessageWrappedProtobuf() throws Exception {
        // Given
        MqttMessage mqttMessage = MqttMessage.newBuilder()
                .setTopic("test/topic")
                .setQos(1)
                .setRetained(false)
                .setMessage(testBaseMessage)
                .build();
        
        byte[] mqttData = mqttMessage.toByteArray();
        
        // When
        BaseMessage result = messageConverter.transformToBaseMessage(mqttData, "application/x-protobuf");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(testBaseMessage.getId());
        assertThat(result.getType()).isEqualTo(testBaseMessage.getType());
    }

    @Test
    void shouldPreserveEncryptionFlag() {
        // Given
        BaseMessage encryptedMessage = testBaseMessage.toBuilder()
                .setEncrypted(true)
                .setSignature("test-signature")
                .build();
        
        // When
        String json = messageConverter.convertToJson(encryptedMessage);
        BaseMessage result = messageConverter.convertFromJson(json);
        
        // Then
        assertThat(result.getEncrypted()).isTrue();
        assertThat(result.getSignature()).isEqualTo("test-signature");
    }
}