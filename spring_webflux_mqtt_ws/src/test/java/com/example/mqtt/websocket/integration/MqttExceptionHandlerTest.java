package com.example.mqtt.websocket.integration;

import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.support.MessageBuilder;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

/**
 * MQTT 异常处理器测试类
 */
@ExtendWith(MockitoExtension.class)
class MqttExceptionHandlerTest {

    @InjectMocks
    private MqttExceptionHandler mqttExceptionHandler;

    @BeforeEach
    void setUp() {
        // 测试设置
    }

    @Test
    void shouldHandleMqttBrokerUnavailableException() {
        // Given
        MqttException mqttException = new MqttException(MqttException.REASON_CODE_BROKER_UNAVAILABLE);
        Message<MqttException> message = MessageBuilder.withPayload(mqttException).build();

        // When & Then
        assertDoesNotThrow(() -> mqttExceptionHandler.handleMqttError(message));
    }

    @Test
    void shouldHandleMqttClientTimeoutException() {
        // Given
        MqttException mqttException = new MqttException(MqttException.REASON_CODE_CLIENT_TIMEOUT);
        Message<MqttException> message = MessageBuilder.withPayload(mqttException).build();

        // When & Then
        assertDoesNotThrow(() -> mqttExceptionHandler.handleMqttError(message));
    }

    @Test
    void shouldHandleMqttConnectionLostException() {
        // Given
        MqttException mqttException = new MqttException(MqttException.REASON_CODE_CONNECTION_LOST);
        Message<MqttException> message = MessageBuilder.withPayload(mqttException).build();

        // When & Then
        assertDoesNotThrow(() -> mqttExceptionHandler.handleMqttError(message));
    }

    @Test
    void shouldHandleMqttMaxInflightException() {
        // Given
        MqttException mqttException = new MqttException(MqttException.REASON_CODE_MAX_INFLIGHT);
        Message<MqttException> message = MessageBuilder.withPayload(mqttException).build();

        // When & Then
        assertDoesNotThrow(() -> mqttExceptionHandler.handleMqttError(message));
    }

    @Test
    void shouldHandleMessagingException() {
        // Given
        MessagingException messagingException = new MessagingException("Test messaging exception");
        Message<MessagingException> message = MessageBuilder.withPayload(messagingException).build();

        // When & Then
        assertDoesNotThrow(() -> mqttExceptionHandler.handleMqttError(message));
    }

    @Test
    void shouldHandleGenericException() {
        // Given
        RuntimeException genericException = new RuntimeException("Generic test exception");
        Message<RuntimeException> message = MessageBuilder.withPayload(genericException).build();

        // When & Then
        assertDoesNotThrow(() -> mqttExceptionHandler.handleMqttError(message));
    }

    @Test
    void shouldHandleUnknownMqttException() {
        // Given
        MqttException mqttException = new MqttException(9999); // Unknown reason code
        Message<MqttException> message = MessageBuilder.withPayload(mqttException).build();

        // When & Then
        assertDoesNotThrow(() -> mqttExceptionHandler.handleMqttError(message));
    }
}