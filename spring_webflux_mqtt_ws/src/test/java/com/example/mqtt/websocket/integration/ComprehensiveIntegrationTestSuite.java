package com.example.mqtt.websocket.integration;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

/**
 * 综合集成测试套件
 * 整合所有集成测试和端到端测试，提供完整的系统验证
 * 
 * 测试覆盖范围：
 * 1. 完整消息流转集成测试（MQTT -> WebSocket）
 * 2. 多实例集群部署测试
 * 3. 加密消息传输端到端测试
 * 4. 负载测试和并发测试
 * 5. 故障恢复和数据一致性测试
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.websocket.endpoint=/ws",
    "app.websocket.allowed-origins=*",
    "app.encryption.enabled=true",
    "app.cluster.enabled=true",
    "app.cluster.health-check-interval=5000",
    "app.cluster.failure-detection-timeout=10000",
    "logging.level.com.example.mqtt.websocket=INFO",
    "logging.level.org.springframework.integration=WARN",
    "logging.level.org.eclipse.paho.client.mqttv3=WARN"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("Spring Integration MQTT WebSocket - Comprehensive Integration Test Suite")
class ComprehensiveIntegrationTestSuite {
    
    /**
     * 消息流转集成测试
     * 测试 MQTT 和 WebSocket 之间的双向消息流转
     */
    @Nested
    @DisplayName("Message Flow Integration Tests")
    @TestMethodOrder(MethodOrderer.OrderAnnotation.class)
    class MessageFlowTests extends CompleteMessageFlowIntegrationTest {
        // 继承所有消息流转测试
    }
    
    /**
     * 集群部署测试
     * 测试多实例集群环境下的负载均衡和故障转移
     */
    @Nested
    @DisplayName("Multi-Instance Cluster Tests")
    @TestMethodOrder(MethodOrderer.OrderAnnotation.class)
    class ClusterTests extends MultiInstanceClusterTest {
        // 继承所有集群测试
    }
    
    /**
     * 加密传输测试
     * 测试端到端加密消息传输和密钥管理
     */
    @Nested
    @DisplayName("Encrypted Message Transmission Tests")
    @TestMethodOrder(MethodOrderer.OrderAnnotation.class)
    class EncryptionTests extends EncryptedMessageEndToEndTest {
        // 继承所有加密测试
    }
    
    /**
     * 负载和并发测试
     * 测试系统在高负载和高并发情况下的性能
     */
    @Nested
    @DisplayName("Load Testing and Concurrency Tests")
    @TestMethodOrder(MethodOrderer.OrderAnnotation.class)
    class LoadTests extends LoadTestingAndConcurrencyTest {
        // 继承所有负载测试
    }
    
    /**
     * 故障恢复和数据一致性测试
     * 测试系统的容错能力和数据一致性保证
     */
    @Nested
    @DisplayName("Failure Recovery and Data Consistency Tests")
    @TestMethodOrder(MethodOrderer.OrderAnnotation.class)
    class FailureRecoveryTests extends FailureRecoveryAndDataConsistencyTest {
        // 继承所有故障恢复测试
    }
}