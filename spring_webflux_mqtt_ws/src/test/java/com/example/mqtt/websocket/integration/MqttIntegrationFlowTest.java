package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.config.IntegrationConfig;
import com.example.mqtt.websocket.model.proto.BaseMessage;
import com.example.mqtt.websocket.service.EncryptionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.PollableChannel;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * MQTT 集成流程测试类
 * 测试完整的消息处理流程
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {IntegrationConfig.class, MqttMessageHandler.class, MessageConverter.class, MessageFilter.class})
class MqttIntegrationFlowTest {

    @Autowired
    private MessageChannel mqttInputChannel;
    
    @Autowired
    private PollableChannel webSocketChannel;
    
    @Autowired
    private PollableChannel httpApiChannel;
    
    @Autowired
    private PollableChannel systemChannel;
    
    @Autowired
    private PollableChannel defaultProcessingChannel;
    
    @Autowired
    private PollableChannel deadLetterChannel;

    @MockBean
    private EncryptionService encryptionService;

    @MockBean
    private ObjectMapper objectMapper;

    private BaseMessage testBaseMessage;
    private String testJson;

    @BeforeEach
    void setUp() {
        // 清空所有通道
        clearChannel(webSocketChannel);
        clearChannel(httpApiChannel);
        clearChannel(systemChannel);
        clearChannel(defaultProcessingChannel);
        clearChannel(deadLetterChannel);
        
        testBaseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("test")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8("test payload"))
                .setEncrypted(false)
                .build();
        
        testJson = """
                {
                    "id": "test-123",
                    "type": "json",
                    "data": "test data"
                }
                """;
    }

    @Test
    void shouldRouteWebSocketMessage() throws InterruptedException {
        // Given
        String webSocketTopic = "ws/realtime/data";
        byte[] messageData = testBaseMessage.toByteArray();
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(messageData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, webSocketTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then
        Message<?> receivedMessage = webSocketChannel.receive(5000);
        assertThat(receivedMessage).isNotNull();
        assertThat(receivedMessage.getHeaders().get(MqttHeaders.RECEIVED_TOPIC)).isEqualTo(webSocketTopic);
    }

    @Test
    void shouldRouteApiMessage() throws InterruptedException {
        // Given
        String apiTopic = "api/users/update";
        byte[] messageData = testBaseMessage.toByteArray();
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(messageData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, apiTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then
        Message<?> receivedMessage = httpApiChannel.receive(5000);
        assertThat(receivedMessage).isNotNull();
        assertThat(receivedMessage.getHeaders().get(MqttHeaders.RECEIVED_TOPIC)).isEqualTo(apiTopic);
    }

    @Test
    void shouldRouteSystemMessage() throws InterruptedException {
        // Given
        String systemTopic = "system/config/update";
        byte[] messageData = testBaseMessage.toByteArray();
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(messageData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, systemTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then
        Message<?> receivedMessage = systemChannel.receive(5000);
        assertThat(receivedMessage).isNotNull();
        assertThat(receivedMessage.getHeaders().get(MqttHeaders.RECEIVED_TOPIC)).isEqualTo(systemTopic);
    }

    @Test
    void shouldRouteDefaultMessage() throws InterruptedException {
        // Given
        String defaultTopic = "sensor/temperature";
        byte[] messageData = testBaseMessage.toByteArray();
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(messageData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, defaultTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then
        Message<?> receivedMessage = defaultProcessingChannel.receive(5000);
        assertThat(receivedMessage).isNotNull();
        assertThat(receivedMessage.getHeaders().get(MqttHeaders.RECEIVED_TOPIC)).isEqualTo(defaultTopic);
    }

    @Test
    void shouldFilterBlacklistedTopic() throws InterruptedException {
        // Given
        String blacklistedTopic = "$SYS/broker/version";
        byte[] messageData = testBaseMessage.toByteArray();
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(messageData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, blacklistedTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then - 消息应该被过滤，不会到达任何处理通道
        assertThat(webSocketChannel.receive(1000)).isNull();
        assertThat(httpApiChannel.receive(1000)).isNull();
        assertThat(systemChannel.receive(1000)).isNull();
        assertThat(defaultProcessingChannel.receive(1000)).isNull();
    }

    @Test
    void shouldHandleJsonMessage() throws Exception {
        // Given
        String jsonTopic = "data/json";
        byte[] jsonData = testJson.getBytes();
        
        when(objectMapper.readTree(any(byte[].class))).thenCallRealMethod();
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(jsonData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, jsonTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then
        Message<?> receivedMessage = defaultProcessingChannel.receive(5000);
        assertThat(receivedMessage).isNotNull();
        assertThat(receivedMessage.getPayload()).isInstanceOf(BaseMessage.class);
        
        BaseMessage baseMessage = (BaseMessage) receivedMessage.getPayload();
        assertThat(baseMessage.getType()).isEqualTo("text"); // JSON 被转换为文本消息
    }

    @Test
    void shouldHandleEncryptedMessage() throws Exception {
        // Given
        BaseMessage encryptedMessage = testBaseMessage.toBuilder()
                .setEncrypted(true)
                .putHeaders("keyId", "test-key")
                .build();
        
        byte[] encryptedData = encryptedMessage.toByteArray();
        byte[] decryptedData = "decrypted content".getBytes();
        
        when(encryptionService.decrypt(any(byte[].class), anyString())).thenReturn(decryptedData);
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(encryptedData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, "secure/data")
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then
        Message<?> receivedMessage = defaultProcessingChannel.receive(5000);
        assertThat(receivedMessage).isNotNull();
        assertThat(receivedMessage.getPayload()).isInstanceOf(BaseMessage.class);
        
        BaseMessage baseMessage = (BaseMessage) receivedMessage.getPayload();
        assertThat(baseMessage.getEncrypted()).isFalse(); // 应该已经解密
    }

    @Test
    void shouldHandleDecryptionError() throws Exception {
        // Given
        BaseMessage encryptedMessage = testBaseMessage.toBuilder()
                .setEncrypted(true)
                .putHeaders("keyId", "invalid-key")
                .build();
        
        byte[] encryptedData = encryptedMessage.toByteArray();
        
        when(encryptionService.decrypt(any(byte[].class), anyString()))
                .thenThrow(new RuntimeException("Decryption failed"));
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(encryptedData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, "secure/data")
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then - 消息应该被发送到死信队列
        Message<?> deadLetterMessage = deadLetterChannel.receive(5000);
        assertThat(deadLetterMessage).isNotNull();
        assertThat(deadLetterMessage.getHeaders().get("topic")).isEqualTo("secure/data");
        assertThat(deadLetterMessage.getHeaders().get("error")).isNotNull();
    }

    @Test
    void shouldFilterOversizedMessage() throws InterruptedException {
        // Given
        String normalTopic = "data/large";
        byte[] largeMessage = new byte[11 * 1024 * 1024]; // 11MB - 超过限制
        
        Message<byte[]> inputMessage = MessageBuilder
                .withPayload(largeMessage)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, normalTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        // When
        mqttInputChannel.send(inputMessage);
        
        // Then - 消息应该被过滤
        assertThat(defaultProcessingChannel.receive(1000)).isNull();
    }

    @Test
    void shouldSkipDuplicateMessage() throws InterruptedException {
        // Given
        String normalTopic = "data/normal";
        byte[] messageData = testBaseMessage.toByteArray();
        
        Message<byte[]> firstMessage = MessageBuilder
                .withPayload(messageData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, normalTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, false)
                .build();
        
        Message<byte[]> duplicateMessage = MessageBuilder
                .withPayload(messageData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, normalTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, true)
                .build();
        
        // When
        mqttInputChannel.send(firstMessage);
        mqttInputChannel.send(duplicateMessage);
        
        // Then - 只有第一条消息应该被处理
        Message<?> firstReceived = defaultProcessingChannel.receive(5000);
        assertThat(firstReceived).isNotNull();
        
        Message<?> duplicateReceived = defaultProcessingChannel.receive(1000);
        assertThat(duplicateReceived).isNull(); // 重复消息被跳过
    }

    @Test
    void shouldProcessCriticalDuplicateMessage() throws InterruptedException {
        // Given
        String criticalTopic = "critical/alert";
        byte[] messageData = testBaseMessage.toByteArray();
        
        Message<byte[]> duplicateMessage = MessageBuilder
                .withPayload(messageData)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, criticalTopic)
                .setHeader(MqttHeaders.RECEIVED_QOS, 1)
                .setHeader(MqttHeaders.RECEIVED_RETAINED, false)
                .setHeader(MqttHeaders.DUPLICATE, true)
                .build();
        
        // When
        mqttInputChannel.send(duplicateMessage);
        
        // Then - 关键主题的重复消息应该被处理
        Message<?> receivedMessage = defaultProcessingChannel.receive(5000);
        assertThat(receivedMessage).isNotNull();
    }

    /**
     * 清空通道中的所有消息
     */
    private void clearChannel(PollableChannel channel) {
        while (channel.receive(0) != null) {
            // 清空通道
        }
    }
}