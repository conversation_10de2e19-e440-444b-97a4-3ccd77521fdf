package com.example.mqtt.websocket.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.reactive.socket.WebSocketMessage;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * WebSocketConnectionManager 单元测试
 */
class WebSocketConnectionManagerTest {

    private WebSocketConnectionManager connectionManager;

    @Mock
    private WebSocketSession mockSession1;

    @Mock
    private WebSocketSession mockSession2;

    @Mock
    private WebSocketMessage mockMessage;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        connectionManager = new WebSocketConnectionManager();
        
        // 设置测试用的较小限制值
        ReflectionTestUtils.setField(connectionManager, "maxConnections", 5);
        ReflectionTestUtils.setField(connectionManager, "sessionTimeoutSeconds", 10);
        ReflectionTestUtils.setField(connectionManager, "cleanupIntervalSeconds", 1);
    }

    @Test
    void testAddConnection() {
        // 设置 mock
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(true);
        
        // 测试添加连接
        boolean result = connectionManager.addConnection(mockSession1);
        
        assertTrue(result, "Should successfully add connection");
        assertEquals(1, connectionManager.getConnectionCount(), "Connection count should be 1");
        assertEquals(mockSession1, connectionManager.getSession("session-1"), 
            "Should be able to retrieve the session");
    }

    @Test
    void testAddConnectionWhenLimitReached() {
        // 添加连接直到达到限制
        for (int i = 0; i < 5; i++) {
            WebSocketSession session = mock(WebSocketSession.class);
            when(session.getId()).thenReturn("session-" + i);
            when(session.isOpen()).thenReturn(true);
            assertTrue(connectionManager.addConnection(session), 
                "Should add connection " + i);
        }
        
        // 尝试添加超出限制的连接
        when(mockSession1.getId()).thenReturn("session-overflow");
        when(mockSession1.isOpen()).thenReturn(true);
        
        boolean result = connectionManager.addConnection(mockSession1);
        
        assertFalse(result, "Should reject connection when limit is reached");
        assertEquals(5, connectionManager.getConnectionCount(), 
            "Connection count should remain at limit");
    }

    @Test
    void testRemoveConnection() {
        // 添加连接
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(true);
        when(mockSession1.close()).thenReturn(Mono.empty());
        
        connectionManager.addConnection(mockSession1);
        assertEquals(1, connectionManager.getConnectionCount(), "Should have 1 connection");
        
        // 移除连接
        connectionManager.removeConnection("session-1");
        
        assertEquals(0, connectionManager.getConnectionCount(), "Should have 0 connections");
        assertNull(connectionManager.getSession("session-1"), "Session should be removed");
        verify(mockSession1, times(1)).close();
    }

    @Test
    void testRemoveNonExistentConnection() {
        // 尝试移除不存在的连接
        assertDoesNotThrow(() -> connectionManager.removeConnection("non-existent"), 
            "Should not throw when removing non-existent connection");
        assertEquals(0, connectionManager.getConnectionCount(), 
            "Connection count should remain 0");
    }

    @Test
    void testUpdateActivity() {
        // 添加连接
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(true);
        
        connectionManager.addConnection(mockSession1);
        
        // 更新活动时间
        assertDoesNotThrow(() -> connectionManager.updateActivity("session-1"), 
            "Should not throw when updating activity");
        
        // 尝试更新不存在会话的活动时间
        assertDoesNotThrow(() -> connectionManager.updateActivity("non-existent"), 
            "Should not throw when updating activity for non-existent session");
    }

    @Test
    void testCanAcceptConnection() {
        assertTrue(connectionManager.canAcceptConnection(), 
            "Should accept connection when under limit");
        
        // 添加连接直到达到限制
        for (int i = 0; i < 5; i++) {
            WebSocketSession session = mock(WebSocketSession.class);
            when(session.getId()).thenReturn("session-" + i);
            when(session.isOpen()).thenReturn(true);
            connectionManager.addConnection(session);
        }
        
        assertFalse(connectionManager.canAcceptConnection(), 
            "Should not accept connection when at limit");
    }

    @Test
    void testGetMaxConnections() {
        assertEquals(5, connectionManager.getMaxConnections(), 
            "Max connections should match configured value");
    }

    @Test
    void testSendToSession() {
        // 设置 mock
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(true);
        when(mockSession1.textMessage(anyString())).thenReturn(mockMessage);
        when(mockSession1.send(any(Mono.class))).thenReturn(Mono.empty());
        
        // 添加连接
        connectionManager.addConnection(mockSession1);
        
        // 发送消息
        Mono<Void> result = connectionManager.sendToSession("session-1", "test message");
        
        StepVerifier.create(result)
            .verifyComplete();
        
        verify(mockSession1, times(1)).textMessage("test message");
        verify(mockSession1, times(1)).send(any(Mono.class));
    }

    @Test
    void testSendToNonExistentSession() {
        Mono<Void> result = connectionManager.sendToSession("non-existent", "test message");
        
        StepVerifier.create(result)
            .verifyComplete();
        
        // 不应该有任何交互
        verifyNoInteractions(mockSession1);
    }

    @Test
    void testSendToClosedSession() {
        // 设置 mock - 会话已关闭
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(false);
        
        // 添加连接
        connectionManager.addConnection(mockSession1);
        
        // 尝试发送消息到已关闭的会话
        Mono<Void> result = connectionManager.sendToSession("session-1", "test message");
        
        StepVerifier.create(result)
            .verifyComplete();
        
        // 不应该尝试发送消息
        verify(mockSession1, never()).send(any(Mono.class));
    }

    @Test
    void testBroadcastToAll() {
        // 设置多个 mock 会话
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(true);
        when(mockSession1.textMessage(anyString())).thenReturn(mockMessage);
        when(mockSession1.send(any(Mono.class))).thenReturn(Mono.empty());
        
        when(mockSession2.getId()).thenReturn("session-2");
        when(mockSession2.isOpen()).thenReturn(true);
        when(mockSession2.textMessage(anyString())).thenReturn(mockMessage);
        when(mockSession2.send(any(Mono.class))).thenReturn(Mono.empty());
        
        // 添加连接
        connectionManager.addConnection(mockSession1);
        connectionManager.addConnection(mockSession2);
        
        // 广播消息
        Mono<Void> result = connectionManager.broadcastToAll("broadcast message");
        
        StepVerifier.create(result)
            .verifyComplete();
        
        // 验证消息发送到所有会话
        verify(mockSession1, times(1)).textMessage("broadcast message");
        verify(mockSession2, times(1)).textMessage("broadcast message");
        verify(mockSession1, times(1)).send(any(Mono.class));
        verify(mockSession2, times(1)).send(any(Mono.class));
    }

    @Test
    void testBroadcastToAllWithClosedSession() {
        // 设置一个开放会话和一个关闭会话
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(true);
        when(mockSession1.textMessage(anyString())).thenReturn(mockMessage);
        when(mockSession1.send(any(Mono.class))).thenReturn(Mono.empty());
        
        when(mockSession2.getId()).thenReturn("session-2");
        when(mockSession2.isOpen()).thenReturn(false);
        
        // 添加连接
        connectionManager.addConnection(mockSession1);
        connectionManager.addConnection(mockSession2);
        
        // 广播消息
        Mono<Void> result = connectionManager.broadcastToAll("broadcast message");
        
        StepVerifier.create(result)
            .verifyComplete();
        
        // 只有开放的会话应该收到消息
        verify(mockSession1, times(1)).send(any(Mono.class));
        verify(mockSession2, never()).send(any(Mono.class));
    }

    @Test
    void testGetConnectionStats() {
        // 添加一些连接
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(true);
        connectionManager.addConnection(mockSession1);
        
        when(mockSession2.getId()).thenReturn("session-2");
        when(mockSession2.isOpen()).thenReturn(true);
        connectionManager.addConnection(mockSession2);
        
        // 获取统计信息
        WebSocketConnectionManager.ConnectionStats stats = connectionManager.getConnectionStats();
        
        assertNotNull(stats, "Stats should not be null");
        assertEquals(2, stats.getActiveConnections(), "Should have 2 active connections");
        assertEquals(5, stats.getMaxConnections(), "Max connections should be 5");
        assertEquals(2, stats.getSessionCount(), "Session count should be 2");
        assertEquals(2, stats.getActivityTrackingCount(), "Activity tracking count should be 2");
        assertEquals(0.4, stats.getConnectionUtilization(), 0.001, 
            "Connection utilization should be 0.4 (2/5)");
    }

    @Test
    void testConnectionStatsCalculations() {
        WebSocketConnectionManager.ConnectionStats stats = 
            new WebSocketConnectionManager.ConnectionStats(3, 10, 3, 3);
        
        assertEquals(3, stats.getActiveConnections());
        assertEquals(10, stats.getMaxConnections());
        assertEquals(3, stats.getSessionCount());
        assertEquals(3, stats.getActivityTrackingCount());
        assertEquals(0.3, stats.getConnectionUtilization(), 0.001);
    }

    @Test
    void testConnectionStatsWithZeroMax() {
        WebSocketConnectionManager.ConnectionStats stats = 
            new WebSocketConnectionManager.ConnectionStats(0, 0, 0, 0);
        
        assertEquals(0.0, stats.getConnectionUtilization(), 0.001, 
            "Utilization should be 0 when max connections is 0");
    }

    @Test
    void testShutdown() {
        // 添加连接
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(true);
        when(mockSession1.close()).thenReturn(Mono.empty());
        
        when(mockSession2.getId()).thenReturn("session-2");
        when(mockSession2.isOpen()).thenReturn(true);
        when(mockSession2.close()).thenReturn(Mono.empty());
        
        connectionManager.addConnection(mockSession1);
        connectionManager.addConnection(mockSession2);
        
        assertEquals(2, connectionManager.getConnectionCount(), "Should have 2 connections");
        
        // 关闭连接管理器
        assertDoesNotThrow(() -> connectionManager.shutdown(), 
            "Shutdown should not throw exception");
        
        // 验证所有连接都被关闭
        verify(mockSession1, times(1)).close();
        verify(mockSession2, times(1)).close();
        
        assertEquals(0, connectionManager.getConnectionCount(), 
            "Should have 0 connections after shutdown");
    }

    @Test
    void testShutdownWithClosedSessions() {
        // 添加已关闭的连接
        when(mockSession1.getId()).thenReturn("session-1");
        when(mockSession1.isOpen()).thenReturn(false);
        
        connectionManager.addConnection(mockSession1);
        
        // 关闭连接管理器
        assertDoesNotThrow(() -> connectionManager.shutdown(), 
            "Shutdown should not throw exception with closed sessions");
        
        // 已关闭的会话不应该再次调用 close
        verify(mockSession1, never()).close();
    }
}