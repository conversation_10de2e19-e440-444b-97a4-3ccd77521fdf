package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.FailoverManager;
import com.example.mqtt.websocket.service.LoadBalancer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * 负载均衡和故障转移集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "app.loadbalancer.strategy=round_robin",
    "app.loadbalancer.health-check-enabled=true",
    "app.loadbalancer.health-check-interval=1",
    "app.failover.auto-failover-enabled=true",
    "app.failover.auto-recovery-enabled=true",
    "app.failover.failure-threshold=2",
    "app.failover.recovery-threshold=1",
    "app.failover.health-check-interval=1"
})
class LoadBalancerFailoverIntegrationTest {

    @Resource
    private LoadBalancer loadBalancer;

    @Resource
    private FailoverManager failoverManager;

    @MockBean
    private ClusterStateManager clusterStateManager;

    private final Set<String> testInstances = Set.of("instance1", "instance2", "instance3");

    @BeforeEach
    void setUp() {
        // 模拟集群状态
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.just(testInstances));
        when(clusterStateManager.isInstanceHealthy("instance1"))
            .thenReturn(Mono.just(true));
        when(clusterStateManager.isInstanceHealthy("instance2"))
            .thenReturn(Mono.just(true));
        when(clusterStateManager.isInstanceHealthy("instance3"))
            .thenReturn(Mono.just(true));
    }

    @Test
    void shouldPerformLoadBalancingAcrossHealthyInstances() {
        // When - 执行多次负载均衡
        for (int i = 0; i < 6; i++) {
            StepVerifier.create(loadBalancer.selectInstance())
                .expectNextMatches(testInstances::contains)
                .verifyComplete();
        }

        // Then - 验证统计信息
        StepVerifier.create(loadBalancer.getStats())
            .assertNext(stats -> {
                assertThat(stats.totalRequests()).isEqualTo(6);
                assertThat(stats.requestCounts()).hasSize(3);
                // 轮询策略应该均匀分布
                stats.requestCounts().values().forEach(count -> 
                    assertThat(count).isEqualTo(2L));
            })
            .verifyComplete();
    }

    @Test
    void shouldExcludeUnhealthyInstancesFromLoadBalancing() {
        // Given - 标记一个实例为不健康
        StepVerifier.create(loadBalancer.updateInstanceHealth("instance2", false))
            .verifyComplete();

        // When & Then - 只有健康实例应该被选择
        for (int i = 0; i < 4; i++) {
            StepVerifier.create(loadBalancer.selectInstance())
                .expectNextMatches(instance -> 
                    testInstances.contains(instance) && !instance.equals("instance2"))
                .verifyComplete();
        }

        // 验证健康实例列表
        StepVerifier.create(loadBalancer.getHealthyInstances())
            .expectNextMatches(instances -> 
                instances.size() == 2 && 
                instances.contains("instance1") && 
                instances.contains("instance3") &&
                !instances.contains("instance2"))
            .verifyComplete();
    }

    @Test
    void shouldTriggerFailoverAndRecovery() throws InterruptedException {
        // Given - 设置故障转移事件监听
        CountDownLatch failoverLatch = new CountDownLatch(1);
        CountDownLatch recoveryLatch = new CountDownLatch(1);

        failoverManager.listenFailoverEvents()
            .subscribe(event -> {
                if (event.type() == FailoverManager.FailoverEventType.FAILOVER_TRIGGERED) {
                    failoverLatch.countDown();
                } else if (event.type() == FailoverManager.FailoverEventType.INSTANCE_RECOVERED) {
                    recoveryLatch.countDown();
                }
            });

        // When - 触发故障转移
        String failedInstance = "instance1";
        StepVerifier.create(failoverManager.triggerFailover(failedInstance))
            .assertNext(result -> {
                assertThat(result.success()).isTrue();
                assertThat(result.failedInstanceId()).isEqualTo(failedInstance);
            })
            .verifyComplete();

        // Then - 验证故障转移事件
        assertThat(failoverLatch.await(2, TimeUnit.SECONDS)).isTrue();

        // 验证实例被标记为失败
        StepVerifier.create(failoverManager.getFailedInstances())
            .expectNextMatches(instances -> instances.contains(failedInstance))
            .verifyComplete();

        // 尝试恢复实例
        StepVerifier.create(failoverManager.attemptInstanceRecovery(failedInstance))
            .assertNext(result -> {
                assertThat(result.recovered()).isTrue();
                assertThat(result.instanceId()).isEqualTo(failedInstance);
            })
            .verifyComplete();

        // 验证恢复事件
        assertThat(recoveryLatch.await(2, TimeUnit.SECONDS)).isTrue();

        // 验证实例不再在失败列表中
        StepVerifier.create(failoverManager.getFailedInstances())
            .expectNextMatches(instances -> !instances.contains(failedInstance))
            .verifyComplete();
    }

    @Test
    void shouldHandleHealthCheckFailures() {
        // Given - 模拟实例健康检查失败
        when(clusterStateManager.isInstanceHealthy("instance3"))
            .thenReturn(Mono.just(false));

        // When & Then - 健康检查应该检测到失败
        StepVerifier.create(failoverManager.checkInstanceHealth("instance3"))
            .assertNext(result -> {
                assertThat(result.healthy()).isFalse();
                assertThat(result.instanceId()).isEqualTo("instance3");
                assertThat(result.errorMessage()).contains("heartbeat check failed");
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleClusterStateChanges() {
        // Given - 模拟集群状态变化（移除一个实例）
        Set<String> updatedInstances = Set.of("instance1", "instance2");
        when(clusterStateManager.getActiveInstances())
            .thenReturn(Mono.just(updatedInstances));

        // When & Then - 负载均衡应该适应新的实例列表
        for (int i = 0; i < 4; i++) {
            StepVerifier.create(loadBalancer.selectInstance())
                .expectNextMatches(updatedInstances::contains)
                .verifyComplete();
        }

        StepVerifier.create(loadBalancer.getHealthyInstances())
            .expectNextMatches(instances -> 
                instances.size() == 2 && 
                instances.containsAll(updatedInstances))
            .verifyComplete();
    }

    @Test
    void shouldConfigureFailoverSettings() {
        // When & Then - 获取当前配置
        StepVerifier.create(failoverManager.getFailoverConfig())
            .assertNext(config -> {
                assertThat(config.autoFailoverEnabled()).isTrue();
                assertThat(config.autoRecoveryEnabled()).isTrue();
                assertThat(config.failureThreshold()).isEqualTo(2);
            })
            .verifyComplete();

        // 更新配置
        FailoverManager.FailoverConfig newConfig = new FailoverManager.FailoverConfig(
            2, 2, 1, 1, 1, false, false
        );

        StepVerifier.create(failoverManager.updateFailoverConfig(newConfig))
            .verifyComplete();

        // 验证配置更新
        StepVerifier.create(failoverManager.getFailoverConfig())
            .assertNext(config -> {
                assertThat(config.autoFailoverEnabled()).isFalse();
                assertThat(config.autoRecoveryEnabled()).isFalse();
                assertThat(config.failureThreshold()).isEqualTo(1);
            })
            .verifyComplete();
    }

    @Test
    void shouldHandleNoHealthyInstances() {
        // Given - 标记所有实例为不健康
        StepVerifier.create(loadBalancer.updateInstanceHealth("instance1", false))
            .verifyComplete();
        StepVerifier.create(loadBalancer.updateInstanceHealth("instance2", false))
            .verifyComplete();
        StepVerifier.create(loadBalancer.updateInstanceHealth("instance3", false))
            .verifyComplete();

        // When & Then - 负载均衡应该返回空结果
        StepVerifier.create(loadBalancer.selectInstance())
            .verifyComplete();

        StepVerifier.create(loadBalancer.getHealthyInstances())
            .expectNext(java.util.List.of())
            .verifyComplete();
    }

    @Test
    void shouldStartAndStopFailureDetection() {
        // When & Then - 启动故障检测
        StepVerifier.create(failoverManager.startFailureDetection())
            .expectComplete()
            .verify(Duration.ofSeconds(3));

        // 停止故障检测
        StepVerifier.create(failoverManager.stopFailureDetection())
            .verifyComplete();
    }

    @Test
    void shouldHandleInstanceWeights() {
        // Given - 设置实例权重
        StepVerifier.create(loadBalancer.setInstanceWeight("instance1", 3))
            .verifyComplete();
        StepVerifier.create(loadBalancer.setInstanceWeight("instance2", 2))
            .verifyComplete();

        // When & Then - 验证权重设置
        StepVerifier.create(loadBalancer.getInstanceWeight("instance1"))
            .expectNext(3)
            .verifyComplete();

        StepVerifier.create(loadBalancer.getInstanceWeight("instance2"))
            .expectNext(2)
            .verifyComplete();

        StepVerifier.create(loadBalancer.getInstanceWeight("instance3"))
            .expectNext(1) // 默认权重
            .verifyComplete();

        // 验证统计信息包含权重信息
        StepVerifier.create(loadBalancer.getStats())
            .assertNext(stats -> {
                assertThat(stats.instanceWeights()).containsEntry("instance1", 3);
                assertThat(stats.instanceWeights()).containsEntry("instance2", 2);
            })
            .verifyComplete();
    }
}