package com.example.mqtt.websocket.service;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.actuate.health.Health;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MemoryMonitoringService 单元测试
 */
class MemoryMonitoringServiceTest {

    private MemoryMonitoringService memoryMonitoringService;
    private MeterRegistry meterRegistry;

    @BeforeEach
    void setUp() {
        meterRegistry = new SimpleMeterRegistry();
        memoryMonitoringService = new MemoryMonitoringService(meterRegistry);
        memoryMonitoringService.initializeMetrics();
    }

    @Test
    void testGetHeapUsageRatio() {
        double heapUsageRatio = memoryMonitoringService.getHeapUsageRatio();
        
        assertTrue(heapUsageRatio >= 0.0, "Heap usage ratio should be non-negative");
        assertTrue(heapUsageRatio <= 1.0, "Heap usage ratio should not exceed 1.0");
    }

    @Test
    void testGetNonHeapUsageRatio() {
        double nonHeapUsageRatio = memoryMonitoringService.getNonHeapUsageRatio();
        
        assertTrue(nonHeapUsageRatio >= 0.0, "Non-heap usage ratio should be non-negative");
        assertTrue(nonHeapUsageRatio <= 1.0, "Non-heap usage ratio should not exceed 1.0");
    }

    @Test
    void testGetAvailableHeapMemory() {
        long availableMemory = memoryMonitoringService.getAvailableHeapMemory();
        
        assertTrue(availableMemory >= 0, "Available heap memory should be non-negative");
    }

    @Test
    void testGetAverageGcTime() {
        double averageGcTime = memoryMonitoringService.getAverageGcTime();
        
        assertTrue(averageGcTime >= 0.0, "Average GC time should be non-negative");
    }

    @Test
    void testGetMemoryStats() {
        MemoryMonitoringService.MemoryStats stats = memoryMonitoringService.getMemoryStats();
        
        assertNotNull(stats, "Memory stats should not be null");
        assertTrue(stats.getHeapUsed() >= 0, "Heap used should be non-negative");
        assertTrue(stats.getHeapMax() > 0, "Heap max should be positive");
        assertTrue(stats.getNonHeapUsed() >= 0, "Non-heap used should be non-negative");
        assertTrue(stats.getTotalGcTime() >= 0, "Total GC time should be non-negative");
        assertTrue(stats.getTotalGcCount() >= 0, "Total GC count should be non-negative");
        assertTrue(stats.getAverageGcTime() >= 0.0, "Average GC time should be non-negative");
    }

    @Test
    void testMemoryStatsCalculations() {
        MemoryMonitoringService.MemoryStats stats = memoryMonitoringService.getMemoryStats();
        
        // 测试比例计算
        double heapRatio = stats.getHeapUsageRatio();
        double expectedHeapRatio = stats.getHeapMax() > 0 ? 
            (double) stats.getHeapUsed() / stats.getHeapMax() : 0.0;
        assertEquals(expectedHeapRatio, heapRatio, 0.001, "Heap usage ratio calculation should be correct");
        
        double nonHeapRatio = stats.getNonHeapUsageRatio();
        double expectedNonHeapRatio = stats.getNonHeapMax() > 0 ? 
            (double) stats.getNonHeapUsed() / stats.getNonHeapMax() : 0.0;
        assertEquals(expectedNonHeapRatio, nonHeapRatio, 0.001, "Non-heap usage ratio calculation should be correct");
    }

    @Test
    void testTriggerGc() {
        // 记录 GC 前的状态
        long beforeGcTime = System.currentTimeMillis();
        
        // 触发 GC
        assertDoesNotThrow(() -> memoryMonitoringService.triggerGc(), 
            "Triggering GC should not throw exception");
        
        long afterGcTime = System.currentTimeMillis();
        
        // 验证操作在合理时间内完成
        assertTrue(afterGcTime - beforeGcTime < 5000, 
            "GC trigger should complete within 5 seconds");
    }

    @Test
    void testHealthCheck() {
        Health health = memoryMonitoringService.health();
        
        assertNotNull(health, "Health check result should not be null");
        assertNotNull(health.getStatus(), "Health status should not be null");
        assertNotNull(health.getDetails(), "Health details should not be null");
        
        // 验证健康检查包含必要的详细信息
        assertTrue(health.getDetails().containsKey("heapUsageRatio"), 
            "Health details should contain heap usage ratio");
        assertTrue(health.getDetails().containsKey("heapUsed"), 
            "Health details should contain heap used");
        assertTrue(health.getDetails().containsKey("heapMax"), 
            "Health details should contain heap max");
        assertTrue(health.getDetails().containsKey("totalGcTime"), 
            "Health details should contain total GC time");
        assertTrue(health.getDetails().containsKey("totalGcCount"), 
            "Health details should contain total GC count");
    }

    @Test
    void testHealthStatusBasedOnMemoryUsage() {
        Health health = memoryMonitoringService.health();
        double heapUsageRatio = memoryMonitoringService.getHeapUsageRatio();
        
        if (heapUsageRatio > 0.9) {
            assertEquals("DOWN", health.getStatus().getCode(), 
                "Health should be DOWN when heap usage is critical");
            assertEquals("CRITICAL", health.getDetails().get("status"), 
                "Status should be CRITICAL when heap usage is critical");
        } else if (heapUsageRatio > 0.8) {
            assertEquals("UP", health.getStatus().getCode(), 
                "Health should be UP when heap usage is high but not critical");
            assertEquals("WARNING", health.getDetails().get("status"), 
                "Status should be WARNING when heap usage is high");
        } else {
            assertEquals("UP", health.getStatus().getCode(), 
                "Health should be UP when heap usage is normal");
            assertEquals("HEALTHY", health.getDetails().get("status"), 
                "Status should be HEALTHY when heap usage is normal");
        }
    }

    @Test
    void testMonitorMemoryUsage() {
        // 这个测试验证监控方法不会抛出异常
        assertDoesNotThrow(() -> memoryMonitoringService.monitorMemoryUsage(), 
            "Memory monitoring should not throw exception");
    }

    @Test
    void testMetricsRegistration() {
        // 验证指标是否正确注册
        assertNotNull(meterRegistry.find("jvm.memory.heap.used.ratio").gauge(), 
            "Heap usage ratio metric should be registered");
        assertNotNull(meterRegistry.find("jvm.memory.non_heap.used.ratio").gauge(), 
            "Non-heap usage ratio metric should be registered");
        assertNotNull(meterRegistry.find("jvm.memory.heap.available").gauge(), 
            "Available heap memory metric should be registered");
        assertNotNull(meterRegistry.find("jvm.gc.total.time").gauge(), 
            "Total GC time metric should be registered");
        assertNotNull(meterRegistry.find("jvm.gc.total.count").gauge(), 
            "Total GC count metric should be registered");
        assertNotNull(meterRegistry.find("jvm.gc.average.time").gauge(), 
            "Average GC time metric should be registered");
    }

    @Test
    void testMemoryStatsConsistency() {
        // 获取多次统计，验证数据一致性
        MemoryMonitoringService.MemoryStats stats1 = memoryMonitoringService.getMemoryStats();
        MemoryMonitoringService.MemoryStats stats2 = memoryMonitoringService.getMemoryStats();
        
        // 堆内存最大值应该保持一致
        assertEquals(stats1.getHeapMax(), stats2.getHeapMax(), 
            "Heap max should be consistent between calls");
        
        // GC 计数应该单调递增或保持不变
        assertTrue(stats2.getTotalGcCount() >= stats1.getTotalGcCount(), 
            "GC count should be monotonically increasing");
        
        // GC 时间应该单调递增或保持不变
        assertTrue(stats2.getTotalGcTime() >= stats1.getTotalGcTime(), 
            "GC time should be monotonically increasing");
    }

    @Test
    void testMemoryStatsEdgeCases() {
        MemoryMonitoringService.MemoryStats stats = memoryMonitoringService.getMemoryStats();
        
        // 测试边界情况
        if (stats.getTotalGcCount() == 0) {
            assertEquals(0.0, stats.getAverageGcTime(), 0.001, 
                "Average GC time should be 0 when no GC has occurred");
        } else {
            assertTrue(stats.getAverageGcTime() >= 0.0, 
                "Average GC time should be non-negative when GC has occurred");
        }
        
        // 验证内存使用不会超过最大值
        assertTrue(stats.getHeapUsed() <= stats.getHeapMax(), 
            "Heap used should not exceed heap max");
        
        if (stats.getNonHeapMax() > 0) {
            assertTrue(stats.getNonHeapUsed() <= stats.getNonHeapMax(), 
                "Non-heap used should not exceed non-heap max");
        }
    }
}