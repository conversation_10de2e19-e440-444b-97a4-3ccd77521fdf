package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.RedisConfig;
import com.example.mqtt.websocket.config.properties.RedisProperties;
import com.example.mqtt.websocket.service.ClusterStateManager;
import com.example.mqtt.websocket.service.ClusterSyncService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@SpringBootTest(classes = {
        RedisConfig.class,
        RedisClusterStateManager.class,
        RedisClusterSyncService.class
})
@Testcontainers
class RedisClusterIntegrationTest {

    @Container
    static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:7-alpine"))
            .withExposedPorts(6379)
            .withCommand("redis-server", "--appendonly", "yes");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("app.redis.host", redis::getHost);
        registry.add("app.redis.port", redis::getFirstMappedPort);
        registry.add("app.redis.connection-timeout", () -> "PT5S");
        registry.add("app.redis.read-timeout", () -> "PT3S");
        registry.add("app.redis.cluster.instance-key-prefix", () -> "test:instance:");
        registry.add("app.redis.cluster.heartbeat-interval", () -> "PT1S");
        registry.add("app.redis.cluster.instance-expiration", () -> "PT10S");
        registry.add("app.redis.cluster.message-channel", () -> "test:messages");
        registry.add("app.redis.cluster.state-sync-interval", () -> "PT2S");
    }

    @Autowired
    private ClusterStateManager clusterStateManager;

    @Autowired
    private ReactiveRedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisProperties redisProperties;

    @BeforeEach
    void setUp() {
        // 清理 Redis 数据
        redisTemplate.getConnectionFactory()
                .getReactiveConnection()
                .serverCommands()
                .flushAll()
                .block();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        redisTemplate.getConnectionFactory()
                .getReactiveConnection()
                .serverCommands()
                .flushAll()
                .block();
    }

    @Test
    void shouldRegisterAndDiscoverInstances() {
        // Given
        String instanceId1 = "test-instance-1";
        String instanceId2 = "test-instance-2";
        
        ClusterStateManager.InstanceInfo info1 = createTestInstanceInfo(instanceId1, 8080);
        ClusterStateManager.InstanceInfo info2 = createTestInstanceInfo(instanceId2, 8081);

        // When & Then
        StepVerifier.create(
                clusterStateManager.registerInstance(instanceId1, info1)
                        .then(clusterStateManager.registerInstance(instanceId2, info2))
                        .then(clusterStateManager.getActiveInstances())
        )
        .expectNextMatches(instances -> 
                instances.size() == 2 && 
                instances.contains(instanceId1) && 
                instances.contains(instanceId2))
        .verifyComplete();
    }

    @Test
    void shouldUpdateHeartbeatAndMaintainInstance() {
        // Given
        String instanceId = "test-instance-1";
        ClusterStateManager.InstanceInfo info = createTestInstanceInfo(instanceId, 8080);

        // When & Then
        StepVerifier.create(
                clusterStateManager.registerInstance(instanceId, info)
                        .then(Mono.delay(Duration.ofMillis(100)))
                        .then(clusterStateManager.updateHeartbeat(instanceId))
                        .then(clusterStateManager.getInstanceInfo(instanceId))
        )
        .expectNextMatches(updatedInfo -> 
                updatedInfo.instanceId().equals(instanceId) &&
                updatedInfo.lastHeartbeat().isAfter(info.lastHeartbeat()))
        .verifyComplete();
    }

    @Test
    void shouldBroadcastAndReceiveClusterMessages() {
        // Given
        String testMessage = "Hello Cluster!";

        // When & Then - 测试消息广播
        StepVerifier.create(clusterStateManager.broadcastToCluster(testMessage))
                .verifyComplete();

        // 验证消息监听（这里简化测试，实际应用中需要更复杂的验证）
        StepVerifier.create(
                clusterStateManager.listenClusterMessages()
                        .take(Duration.ofSeconds(1))
        )
        .expectNextCount(0) // 因为是自己发送的消息，会被过滤掉
        .verifyComplete();
    }

    @Test
    void shouldDetectHealthyAndUnhealthyInstances() {
        // Given
        String healthyInstanceId = "healthy-instance";
        String unhealthyInstanceId = "unhealthy-instance";
        
        ClusterStateManager.InstanceInfo healthyInfo = createTestInstanceInfo(healthyInstanceId, 8080);
        ClusterStateManager.InstanceInfo unhealthyInfo = new ClusterStateManager.InstanceInfo(
                unhealthyInstanceId, "localhost", 8081,
                Instant.now().minus(Duration.ofHours(1)),
                Instant.now().minus(Duration.ofHours(1)),
                new HashMap<>()
        );

        // When & Then
        StepVerifier.create(
                clusterStateManager.registerInstance(healthyInstanceId, healthyInfo)
                        .then(clusterStateManager.registerInstance(unhealthyInstanceId, unhealthyInfo))
                        .then(clusterStateManager.isInstanceHealthy(healthyInstanceId))
        )
        .expectNext(true)
        .verifyComplete();

        StepVerifier.create(clusterStateManager.isInstanceHealthy(unhealthyInstanceId))
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    void shouldCleanupExpiredInstances() {
        // Given
        String healthyInstanceId = "healthy-instance";
        String expiredInstanceId = "expired-instance";
        
        ClusterStateManager.InstanceInfo healthyInfo = createTestInstanceInfo(healthyInstanceId, 8080);
        ClusterStateManager.InstanceInfo expiredInfo = new ClusterStateManager.InstanceInfo(
                expiredInstanceId, "localhost", 8081,
                Instant.now().minus(Duration.ofHours(1)),
                Instant.now().minus(Duration.ofHours(1)),
                new HashMap<>()
        );

        // When & Then
        StepVerifier.create(
                clusterStateManager.registerInstance(healthyInstanceId, healthyInfo)
                        .then(clusterStateManager.registerInstance(expiredInstanceId, expiredInfo))
                        .then(clusterStateManager.getActiveInstances())
        )
        .expectNextMatches(instances -> instances.size() == 2)
        .verifyComplete();

        // 清理过期实例
        StepVerifier.create(
                clusterStateManager.cleanupExpiredInstances()
                        .then(clusterStateManager.getActiveInstances())
        )
        .expectNextMatches(instances -> 
                instances.size() == 1 && 
                instances.contains(healthyInstanceId))
        .verifyComplete();
    }

    @Test
    void shouldGetAllInstancesInfo() {
        // Given
        String instanceId1 = "instance-1";
        String instanceId2 = "instance-2";
        
        ClusterStateManager.InstanceInfo info1 = createTestInstanceInfo(instanceId1, 8080);
        ClusterStateManager.InstanceInfo info2 = createTestInstanceInfo(instanceId2, 8081);

        // When & Then
        StepVerifier.create(
                clusterStateManager.registerInstance(instanceId1, info1)
                        .then(clusterStateManager.registerInstance(instanceId2, info2))
                        .then(clusterStateManager.getAllInstancesInfo())
        )
        .expectNextMatches(instancesInfo -> 
                instancesInfo.size() == 2 &&
                instancesInfo.containsKey(instanceId1) &&
                instancesInfo.containsKey(instanceId2) &&
                instancesInfo.get(instanceId1).port() == 8080 &&
                instancesInfo.get(instanceId2).port() == 8081)
        .verifyComplete();
    }

    @Test
    void shouldUnregisterInstance() {
        // Given
        String instanceId = "test-instance";
        ClusterStateManager.InstanceInfo info = createTestInstanceInfo(instanceId, 8080);

        // When & Then
        StepVerifier.create(
                clusterStateManager.registerInstance(instanceId, info)
                        .then(clusterStateManager.getActiveInstances())
        )
        .expectNextMatches(instances -> instances.contains(instanceId))
        .verifyComplete();

        StepVerifier.create(
                clusterStateManager.unregisterInstance(instanceId)
                        .then(clusterStateManager.getActiveInstances())
        )
        .expectNextMatches(instances -> !instances.contains(instanceId))
        .verifyComplete();
    }

    private ClusterStateManager.InstanceInfo createTestInstanceInfo(String instanceId, int port) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("version", "1.0.0");
        metadata.put("environment", "test");
        metadata.put("startTime", Instant.now().toString());
        
        return new ClusterStateManager.InstanceInfo(
                instanceId,
                "localhost",
                port,
                Instant.now().minus(Duration.ofMinutes(1)),
                Instant.now(),
                metadata
        );
    }
}