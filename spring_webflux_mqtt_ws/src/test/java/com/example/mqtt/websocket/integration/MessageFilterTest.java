package com.example.mqtt.websocket.integration;

import com.example.mqtt.websocket.model.proto.BaseMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;

import java.time.Instant;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 消息过滤器测试类
 */
@ExtendWith(MockitoExtension.class)
class MessageFilterTest {

    private MessageFilter messageFilter;
    
    private byte[] testMessage;
    private String testTopic;
    private BaseMessage testBaseMessage;

    @BeforeEach
    void setUp() {
        messageFilter = new MessageFilter();
        
        testMessage = "Test message content".getBytes();
        testTopic = "test/topic";
        
        testBaseMessage = BaseMessage.newBuilder()
                .setId(UUID.randomUUID().toString())
                .setTimestamp(Instant.now().toEpochMilli())
                .setType("test")
                .setSource("mqtt")
                .setPayload(com.google.protobuf.ByteString.copyFrom(testMessage))
                .setEncrypted(false)
                .build();
    }

    @Test
    void shouldPassValidMessage() {
        // When
        boolean result = messageFilter.filterByTopic(testMessage, testTopic);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    void shouldFilterNullMessage() {
        // When
        boolean result = messageFilter.filterByTopic(null, testTopic);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterEmptyMessage() {
        // Given
        byte[] emptyMessage = new byte[0];
        
        // When
        boolean result = messageFilter.filterByTopic(emptyMessage, testTopic);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterNullTopic() {
        // When
        boolean result = messageFilter.filterByTopic(testMessage, null);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterEmptyTopic() {
        // When
        boolean result = messageFilter.filterByTopic(testMessage, "");
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterBlacklistedSystemTopic() {
        // Given
        String systemTopic = "$SYS/broker/version";
        
        // When
        boolean result = messageFilter.filterByTopic(testMessage, systemTopic);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterBlacklistedTestTopic() {
        // Given
        String testTopicBlacklisted = "device/test/sensor";
        
        // When
        boolean result = messageFilter.filterByTopic(testMessage, testTopicBlacklisted);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterBlacklistedDebugTopic() {
        // Given
        String debugTopic = "app/debug/logs";
        
        // When
        boolean result = messageFilter.filterByTopic(testMessage, debugTopic);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterOversizedMessage() {
        // Given
        byte[] largeMessage = new byte[11 * 1024 * 1024]; // 11MB - 超过10MB限制
        
        // When
        boolean result = messageFilter.filterByTopic(largeMessage, testTopic);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldPassNormalSizedMessage() {
        // Given
        byte[] normalMessage = new byte[1024]; // 1KB
        
        // When
        boolean result = messageFilter.filterByTopic(normalMessage, testTopic);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    void shouldPassContentFilter() {
        // When
        boolean result = messageFilter.filterByContent(testBaseMessage);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    void shouldFilterDuplicateMessage() {
        // Given
        String messageId = "duplicate-test-123";
        BaseMessage firstMessage = testBaseMessage.toBuilder()
                .setId(messageId)
                .build();
        BaseMessage duplicateMessage = testBaseMessage.toBuilder()
                .setId(messageId)
                .build();
        
        // When
        boolean firstResult = messageFilter.filterByContent(firstMessage);
        boolean duplicateResult = messageFilter.filterByContent(duplicateMessage);
        
        // Then
        assertThat(firstResult).isTrue();
        assertThat(duplicateResult).isFalse();
    }

    @Test
    void shouldFilterExpiredMessage() {
        // Given
        long expiredTimestamp = Instant.now().toEpochMilli() - 700000; // 超过10分钟前
        BaseMessage expiredMessage = testBaseMessage.toBuilder()
                .setTimestamp(expiredTimestamp)
                .build();
        
        // When
        boolean result = messageFilter.filterByContent(expiredMessage);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterSpamMessageType() {
        // Given
        BaseMessage spamMessage = testBaseMessage.toBuilder()
                .setType("spam")
                .build();
        
        // When
        boolean result = messageFilter.filterByContent(spamMessage);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterAdvertisementMessageType() {
        // Given
        BaseMessage adMessage = testBaseMessage.toBuilder()
                .setType("advertisement")
                .build();
        
        // When
        boolean result = messageFilter.filterByContent(adMessage);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterBlockedSource() {
        // Given
        BaseMessage blockedMessage = testBaseMessage.toBuilder()
                .setSource("blocked-source")
                .build();
        
        // When
        boolean result = messageFilter.filterByContent(blockedMessage);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterMessageWithSpamKeyword() {
        // Given
        BaseMessage spamMessage = testBaseMessage.toBuilder()
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8("This is spam content"))
                .build();
        
        // When
        boolean result = messageFilter.filterByContent(spamMessage);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldFilterMessageWithMalwareKeyword() {
        // Given
        BaseMessage malwareMessage = testBaseMessage.toBuilder()
                .setPayload(com.google.protobuf.ByteString.copyFromUtf8("Download this malware"))
                .build();
        
        // When
        boolean result = messageFilter.filterByContent(malwareMessage);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldRouteHighPriorityToWebSocket() {
        // Given
        String urgentTopic = "urgent/alert";
        Message<BaseMessage> message = MessageBuilder
                .withPayload(testBaseMessage)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, urgentTopic)
                .build();
        
        // When
        String result = messageFilter.conditionalRouting(message);
        
        // Then
        assertThat(result).isEqualTo("webSocketChannel");
    }

    @Test
    void shouldRouteHighPriorityHeaderToWebSocket() {
        // Given
        BaseMessage highPriorityMessage = testBaseMessage.toBuilder()
                .putHeaders("priority", "high")
                .build();
        Message<BaseMessage> message = MessageBuilder
                .withPayload(highPriorityMessage)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, testTopic)
                .build();
        
        // When
        String result = messageFilter.conditionalRouting(message);
        
        // Then
        assertThat(result).isEqualTo("webSocketChannel");
    }

    @Test
    void shouldRouteSystemMessageToSystemChannel() {
        // Given
        String systemTopic = "system/config/update";
        Message<BaseMessage> message = MessageBuilder
                .withPayload(testBaseMessage)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, systemTopic)
                .build();
        
        // When
        String result = messageFilter.conditionalRouting(message);
        
        // Then
        assertThat(result).isEqualTo("systemChannel");
    }

    @Test
    void shouldRouteApiMessageToHttpChannel() {
        // Given
        String apiTopic = "api/users/create";
        Message<BaseMessage> message = MessageBuilder
                .withPayload(testBaseMessage)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, apiTopic)
                .build();
        
        // When
        String result = messageFilter.conditionalRouting(message);
        
        // Then
        assertThat(result).isEqualTo("httpApiChannel");
    }

    @Test
    void shouldRouteBatchMessageToDefaultChannel() {
        // Given
        String batchTopic = "batch/data/import";
        Message<BaseMessage> message = MessageBuilder
                .withPayload(testBaseMessage)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, batchTopic)
                .build();
        
        // When
        String result = messageFilter.conditionalRouting(message);
        
        // Then
        assertThat(result).isEqualTo("defaultProcessingChannel");
    }

    @Test
    void shouldRouteDefaultMessage() {
        // Given
        Message<BaseMessage> message = MessageBuilder
                .withPayload(testBaseMessage)
                .setHeader(MqttHeaders.RECEIVED_TOPIC, testTopic)
                .build();
        
        // When
        String result = messageFilter.conditionalRouting(message);
        
        // Then
        assertThat(result).isEqualTo("mqttRoutingChannel");
    }

    @Test
    void shouldAddAndRemoveBlacklistPattern() {
        // Given
        String pattern = "blocked/.*";
        String blockedTopic = "blocked/messages";
        
        // When - 添加黑名单模式
        messageFilter.addBlacklistedTopicPattern(pattern);
        boolean filteredResult = messageFilter.filterByTopic(testMessage, blockedTopic);
        
        // Then
        assertThat(filteredResult).isFalse();
        
        // When - 移除黑名单模式
        messageFilter.removeBlacklistedTopicPattern(pattern);
        boolean allowedResult = messageFilter.filterByTopic(testMessage, blockedTopic);
        
        // Then
        assertThat(allowedResult).isTrue();
    }

    @Test
    void shouldAddAndRemoveWhitelistPattern() {
        // Given
        String pattern = "allowed/.*";
        String allowedTopic = "allowed/messages";
        String blockedTopic = "other/messages";
        
        // When - 添加白名单模式
        messageFilter.addWhitelistedTopicPattern(pattern);
        boolean allowedResult = messageFilter.filterByTopic(testMessage, allowedTopic);
        boolean blockedResult = messageFilter.filterByTopic(testMessage, blockedTopic);
        
        // Then
        assertThat(allowedResult).isTrue();
        assertThat(blockedResult).isFalse();
        
        // When - 移除白名单模式
        messageFilter.removeWhitelistedTopicPattern(pattern);
        boolean normalResult = messageFilter.filterByTopic(testMessage, blockedTopic);
        
        // Then
        assertThat(normalResult).isTrue(); // 没有白名单限制了
    }

    @Test
    void shouldHandleMessageWithoutId() {
        // Given
        BaseMessage messageWithoutId = testBaseMessage.toBuilder()
                .clearId()
                .build();
        
        // When
        boolean result = messageFilter.filterByContent(messageWithoutId);
        
        // Then
        assertThat(result).isTrue(); // 没有ID的消息不进行去重检查
    }

    @Test
    void shouldHandleMessageWithoutTimestamp() {
        // Given
        BaseMessage messageWithoutTimestamp = testBaseMessage.toBuilder()
                .setTimestamp(0)
                .build();
        
        // When
        boolean result = messageFilter.filterByContent(messageWithoutTimestamp);
        
        // Then
        assertThat(result).isTrue(); // 没有时间戳的消息不检查过期
    }

    @Test
    void shouldHandleRoutingError() {
        // Given
        Message<BaseMessage> messageWithoutTopic = MessageBuilder
                .withPayload(testBaseMessage)
                .build(); // 没有设置 topic header
        
        // When
        String result = messageFilter.conditionalRouting(messageWithoutTopic);
        
        // Then
        assertThat(result).isEqualTo("mqttRoutingChannel"); // 默认路由
    }
}