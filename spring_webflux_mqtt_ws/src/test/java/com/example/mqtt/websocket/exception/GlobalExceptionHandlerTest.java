package com.example.mqtt.websocket.exception;

import com.example.mqtt.websocket.service.AlertService;
import com.example.mqtt.websocket.service.EncryptionException;
import com.example.mqtt.websocket.service.ExceptionStatisticsService;
import com.example.mqtt.websocket.service.WebSocketSessionManager;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GlobalExceptionHandler 单元测试
 */
@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {
    
    @Mock
    private WebSocketSessionManager sessionManager;
    
    @Mock
    private MeterRegistry meterRegistry;
    
    @Mock
    private AlertService alertService;
    
    @Mock
    private ExceptionStatisticsService statisticsService;
    
    @Mock
    private Counter mockCounter;
    
    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;
    
    @BeforeEach
    void setUp() {
        // Mock counter creation
        when(meterRegistry.counter(anyString())).thenReturn(mockCounter);
        when(meterRegistry.counter(anyString(), anyString(), anyString())).thenReturn(mockCounter);
        
        // Initialize the handler
        globalExceptionHandler.initMetrics();
    }
    
    @Test
    void shouldHandleMqttConnectionException() {
        // Given
        String brokerUrl = "tcp://localhost:1883";
        String clientId = "test-client";
        String errorMessage = "Connection refused";
        MqttConnectionException exception = new MqttConnectionException(brokerUrl, clientId, errorMessage);
        
        // When
        globalExceptionHandler.handleMqttConnectionException(exception);
        
        // Then
        verify(mockCounter, times(1)).increment();
        verify(statisticsService).recordException(eq("MqttConnectionException"), eq(errorMessage), eq(exception));
        verify(alertService).sendAlert(
            eq(AlertService.AlertLevel.HIGH),
            eq("MQTT Connection Failed"),
            contains("MQTT connection failed for client test-client to broker tcp://localhost:1883"),
            eq("MQTT")
        );
    }
    
    @Test
    void shouldHandleWebSocketException() {
        // Given
        String sessionId = "session-123";
        String userId = "user-456";
        String errorMessage = "Connection lost";
        WebSocketException exception = new WebSocketException(sessionId, userId, errorMessage);
        
        when(sessionManager.isSessionActive(sessionId)).thenReturn(false);
        
        // When
        globalExceptionHandler.handleWebSocketException(exception);
        
        // Then
        verify(mockCounter, times(1)).increment();
        verify(statisticsService).recordException(eq("WebSocketException"), eq(errorMessage), eq(exception));
        verify(sessionManager).removeSession(sessionId);
        verify(alertService).sendAlert(
            eq(AlertService.AlertLevel.MEDIUM),
            eq("WebSocket Connection Error"),
            contains("WebSocket error for session session-123 (user user-456)"),
            eq("WEBSOCKET")
        );
    }
    
    @Test
    void shouldHandleWebSocketExceptionWithActiveSession() {
        // Given
        String sessionId = "session-123";
        String errorMessage = "Connection error";
        WebSocketException exception = new WebSocketException(sessionId, errorMessage);
        
        when(sessionManager.isSessionActive(sessionId)).thenReturn(true);
        
        // When
        globalExceptionHandler.handleWebSocketException(exception);
        
        // Then
        verify(sessionManager).removeSession(sessionId);
        verify(sessionManager).sendSystemMessage(sessionId, "Connection error occurred. Please reconnect.");
        verify(statisticsService).recordException(eq("WebSocketException"), eq(errorMessage), eq(exception));
    }
    
    @Test
    void shouldHandleEncryptionException() {
        // Given
        String errorMessage = "Decryption failed";
        EncryptionException exception = new EncryptionException(errorMessage);
        
        // When
        globalExceptionHandler.handleEncryptionException(exception);
        
        // Then
        verify(mockCounter, times(1)).increment();
        verify(statisticsService).recordException(eq("EncryptionException"), eq(errorMessage), eq(exception));
        verify(alertService).sendAlert(
            eq(AlertService.AlertLevel.CRITICAL),
            eq("Encryption Failure"),
            eq("Encryption/Decryption operation failed: " + errorMessage),
            eq("SECURITY")
        );
    }
    
    @Test
    void shouldHandleMessageProcessingException() {
        // Given
        String messageId = "msg-123";
        String messageType = "MQTT";
        String errorMessage = "Processing failed";
        MessageProcessingException exception = new MessageProcessingException(messageId, messageType, errorMessage);
        
        // When
        globalExceptionHandler.handleMessageProcessingException(exception);
        
        // Then
        verify(mockCounter, times(1)).increment();
        verify(statisticsService).recordException(eq("MessageProcessingException"), eq(errorMessage), eq(exception));
        verify(alertService).sendAlert(
            eq(AlertService.AlertLevel.MEDIUM),
            eq("Message Processing Failed"),
            contains("Failed to process MQTT message msg-123"),
            eq("MESSAGE_PROCESSING")
        );
    }
    
    @Test
    void shouldHandleGenericException() {
        // Given
        String errorMessage = "Unexpected error";
        RuntimeException exception = new RuntimeException(errorMessage);
        
        // When
        globalExceptionHandler.handleGenericException(exception);
        
        // Then
        verify(statisticsService).recordException(eq("GenericException"), eq(errorMessage), eq(exception));
        verify(alertService).sendAlert(
            eq(AlertService.AlertLevel.MEDIUM),
            eq("Unhandled Exception"),
            eq("An unhandled exception occurred: " + errorMessage),
            eq("GENERIC")
        );
    }
    
    @Test
    void shouldGetExceptionStatistics() {
        // Given
        GlobalExceptionHandler.ExceptionStatistics expectedStats = 
            new GlobalExceptionHandler.ExceptionStatistics(10, 5, 3, 2);
        
        // When
        GlobalExceptionHandler.ExceptionStatistics actualStats = globalExceptionHandler.getExceptionStatistics();
        
        // Then
        // Since we're using AtomicLong internally, we just verify the method doesn't throw
        // In a real scenario, we would need to trigger some exceptions first
        assert actualStats != null;
    }
    
    @Test
    void shouldHandleWebSocketExceptionCleanupFailure() {
        // Given
        String sessionId = "session-123";
        WebSocketException exception = new WebSocketException(sessionId, "Connection error");
        
        doThrow(new RuntimeException("Cleanup failed")).when(sessionManager).removeSession(sessionId);
        
        // When
        globalExceptionHandler.handleWebSocketException(exception);
        
        // Then
        verify(sessionManager).removeSession(sessionId);
        verify(statisticsService).recordException(eq("WebSocketException"), anyString(), eq(exception));
        // Should not throw exception even if cleanup fails
    }
}