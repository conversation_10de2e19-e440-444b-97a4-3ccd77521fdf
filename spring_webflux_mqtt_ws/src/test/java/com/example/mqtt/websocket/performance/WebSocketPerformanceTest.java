package com.example.mqtt.websocket.performance;

import com.example.mqtt.websocket.service.WebSocketConnectionManager;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.reactive.socket.WebSocketMessage;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * WebSocket 性能测试
 * 测试 WebSocket 连接管理和消息处理性能
 */
@SpringBootTest
@ActiveProfiles("test")
public class WebSocketPerformanceTest {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketPerformanceTest.class);

    @Mock
    private WebSocketSession mockSession;

    /**
     * WebSocket 连接管理性能测试
     */
    @Test
    public void testWebSocketConnectionManagerPerformance() throws Exception {
        logger.info("Starting WebSocket connection manager performance test");
        
        WebSocketConnectionManager connectionManager = new WebSocketConnectionManager();
        AtomicInteger successfulConnections = new AtomicInteger(0);
        AtomicInteger failedConnections = new AtomicInteger(0);
        AtomicLong totalConnectionTime = new AtomicLong(0);
        
        int concurrentConnections = 500;
        ExecutorService executor = Executors.newFixedThreadPool(50);
        
        try {
            CompletableFuture<?>[] futures = new CompletableFuture[concurrentConnections];
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < concurrentConnections; i++) {
                final int connectionId = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    long connStartTime = System.currentTimeMillis();
                    
                    try {
                        // 创建模拟 WebSocket 会话
                        WebSocketSession session = createMockWebSocketSession("session-" + connectionId);
                        
                        if (connectionManager.addConnection(session)) {
                            successfulConnections.incrementAndGet();
                            
                            // 模拟会话活动
                            connectionManager.updateActivity(session.getId());
                            
                            // 模拟消息发送
                            connectionManager.sendToSession(session.getId(), "test message").subscribe();
                            
                        } else {
                            failedConnections.incrementAndGet();
                        }
                        
                        long connTime = System.currentTimeMillis() - connStartTime;
                        totalConnectionTime.addAndGet(connTime);
                        
                    } catch (Exception e) {
                        failedConnections.incrementAndGet();
                        logger.warn("Connection failed for session {}: {}", connectionId, e.getMessage());
                    }
                }, executor);
            }
            
            CompletableFuture.allOf(futures).get(60, TimeUnit.SECONDS);
            long totalTime = System.currentTimeMillis() - startTime;
            
            // 计算性能指标
            int successful = successfulConnections.get();
            int failed = failedConnections.get();
            double successRate = (double) successful / (successful + failed) * 100;
            double avgConnectionTime = successful > 0 ? (double) totalConnectionTime.get() / successful : 0;
            double connectionsPerSecond = (double) successful / (totalTime / 1000.0);
            
            logger.info("WebSocket Connection Manager Performance Results:");
            logger.info("  Concurrent Connections Attempted: {}", concurrentConnections);
            logger.info("  Successful Connections: {}", successful);
            logger.info("  Failed Connections: {}", failed);
            logger.info("  Success Rate: {:.2f}%", successRate);
            logger.info("  Average Connection Time: {:.2f} ms", avgConnectionTime);
            logger.info("  Connections Per Second: {:.2f}", connectionsPerSecond);
            logger.info("  Total Test Time: {} ms", totalTime);
            logger.info("  Current Connection Count: {}", connectionManager.getConnectionCount());
            
            // 性能断言
            assert successRate > 90 : "Connection success rate should be greater than 90%";
            assert avgConnectionTime < 100 : "Average connection time should be less than 100ms";
            assert connectionsPerSecond > 10 : "Should handle at least 10 connections per second";
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
            connectionManager.shutdown();
        }
    }

    /**
     * WebSocket 消息广播性能测试
     */
    @Test
    public void testWebSocketBroadcastPerformance() throws Exception {
        logger.info("Starting WebSocket broadcast performance test");
        
        WebSocketConnectionManager connectionManager = new WebSocketConnectionManager();
        int connectionCount = 100;
        int messageCount = 1000;
        
        // 创建多个连接
        for (int i = 0; i < connectionCount; i++) {
            WebSocketSession session = createMockWebSocketSession("broadcast-session-" + i);
            connectionManager.addConnection(session);
        }
        
        AtomicLong totalBroadcastTime = new AtomicLong(0);
        AtomicInteger completedBroadcasts = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        try {
            CompletableFuture<?>[] futures = new CompletableFuture[messageCount];
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < messageCount; i++) {
                final int messageId = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    long broadcastStartTime = System.currentTimeMillis();
                    
                    try {
                        connectionManager.broadcastToAll("Broadcast message " + messageId)
                            .timeout(Duration.ofSeconds(5))
                            .doOnSuccess(v -> {
                                long broadcastTime = System.currentTimeMillis() - broadcastStartTime;
                                totalBroadcastTime.addAndGet(broadcastTime);
                                completedBroadcasts.incrementAndGet();
                            })
                            .doOnError(e -> logger.warn("Broadcast failed for message {}: {}", 
                                messageId, e.getMessage()))
                            .subscribe();
                        
                    } catch (Exception e) {
                        logger.warn("Error broadcasting message {}: {}", messageId, e.getMessage());
                    }
                }, executor);
            }
            
            CompletableFuture.allOf(futures).get(60, TimeUnit.SECONDS);
            long totalTime = System.currentTimeMillis() - startTime;
            
            // 等待所有广播完成
            Thread.sleep(1000);
            
            int completed = completedBroadcasts.get();
            double avgBroadcastTime = completed > 0 ? (double) totalBroadcastTime.get() / completed : 0;
            double broadcastsPerSecond = (double) completed / (totalTime / 1000.0);
            double messagesPerSecond = (double) (completed * connectionCount) / (totalTime / 1000.0);
            
            logger.info("WebSocket Broadcast Performance Results:");
            logger.info("  Connection Count: {}", connectionCount);
            logger.info("  Messages Sent: {}", messageCount);
            logger.info("  Completed Broadcasts: {}", completed);
            logger.info("  Average Broadcast Time: {:.2f} ms", avgBroadcastTime);
            logger.info("  Broadcasts Per Second: {:.2f}", broadcastsPerSecond);
            logger.info("  Total Messages Per Second: {:.2f}", messagesPerSecond);
            logger.info("  Total Test Time: {} ms", totalTime);
            
            // 性能断言
            double completionRate = (double) completed / messageCount * 100;
            assert completionRate > 95 : "Broadcast completion rate should be greater than 95%";
            assert avgBroadcastTime < 50 : "Average broadcast time should be less than 50ms";
            assert broadcastsPerSecond > 20 : "Should handle at least 20 broadcasts per second";
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
            connectionManager.shutdown();
        }
    }

    /**
     * WebSocket 连接清理性能测试
     */
    @Test
    public void testWebSocketConnectionCleanupPerformance() throws Exception {
        logger.info("Starting WebSocket connection cleanup performance test");
        
        WebSocketConnectionManager connectionManager = new WebSocketConnectionManager();
        int connectionCount = 200;
        
        // 创建连接
        for (int i = 0; i < connectionCount; i++) {
            WebSocketSession session = createMockWebSocketSession("cleanup-session-" + i);
            connectionManager.addConnection(session);
        }
        
        logger.info("Created {} connections", connectionCount);
        assert connectionManager.getConnectionCount() == connectionCount;
        
        // 模拟一些连接变为不活跃
        long startTime = System.currentTimeMillis();
        
        // 等待清理周期
        Thread.sleep(2000);
        
        // 手动触发清理（通过关闭连接管理器）
        connectionManager.shutdown();
        
        long cleanupTime = System.currentTimeMillis() - startTime;
        
        logger.info("WebSocket Connection Cleanup Performance Results:");
        logger.info("  Initial Connections: {}", connectionCount);
        logger.info("  Final Connections: {}", connectionManager.getConnectionCount());
        logger.info("  Cleanup Time: {} ms", cleanupTime);
        logger.info("  Cleanup Rate: {:.2f} connections/ms", (double) connectionCount / cleanupTime);
        
        // 性能断言
        assert cleanupTime < 5000 : "Cleanup should complete within 5 seconds";
        assert connectionManager.getConnectionCount() == 0 : "All connections should be cleaned up";
    }

    /**
     * WebSocket 内存使用测试
     */
    @Test
    public void testWebSocketMemoryUsage() throws Exception {
        logger.info("Starting WebSocket memory usage test");
        
        Runtime runtime = Runtime.getRuntime();
        System.gc();
        Thread.sleep(1000);
        
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        WebSocketConnectionManager connectionManager = new WebSocketConnectionManager();
        int connectionCount = 1000;
        
        // 创建大量连接
        for (int i = 0; i < connectionCount; i++) {
            WebSocketSession session = createMockWebSocketSession("memory-session-" + i);
            connectionManager.addConnection(session);
            
            // 模拟会话活动
            connectionManager.updateActivity(session.getId());
        }
        
        long memoryAfterConnections = runtime.totalMemory() - runtime.freeMemory();
        long memoryPerConnection = (memoryAfterConnections - initialMemory) / connectionCount;
        
        // 发送消息测试内存使用
        for (int i = 0; i < 100; i++) {
            connectionManager.broadcastToAll("Memory test message " + i).subscribe();
        }
        
        long memoryAfterMessages = runtime.totalMemory() - runtime.freeMemory();
        
        // 清理连接
        connectionManager.shutdown();
        System.gc();
        Thread.sleep(1000);
        
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryLeakage = finalMemory - initialMemory;
        
        logger.info("WebSocket Memory Usage Test Results:");
        logger.info("  Initial Memory: {} MB", initialMemory / 1024 / 1024);
        logger.info("  Memory After Connections: {} MB", memoryAfterConnections / 1024 / 1024);
        logger.info("  Memory After Messages: {} MB", memoryAfterMessages / 1024 / 1024);
        logger.info("  Final Memory: {} MB", finalMemory / 1024 / 1024);
        logger.info("  Memory Per Connection: {} KB", memoryPerConnection / 1024);
        logger.info("  Memory Leakage: {} KB", memoryLeakage / 1024);
        
        // 内存使用断言
        assert memoryPerConnection < 10 * 1024 : "Memory per connection should be less than 10KB";
        assert memoryLeakage < 1024 * 1024 : "Memory leakage should be less than 1MB";
    }

    /**
     * 创建模拟 WebSocket 会话
     */
    private WebSocketSession createMockWebSocketSession(String sessionId) {
        WebSocketSession session = mock(WebSocketSession.class);
        when(session.getId()).thenReturn(sessionId);
        when(session.isOpen()).thenReturn(true);
        when(session.textMessage(anyString())).thenAnswer(invocation -> {
            WebSocketMessage message = mock(WebSocketMessage.class);
            when(message.getPayloadAsText()).thenReturn(invocation.getArgument(0));
            return message;
        });
        when(session.send(any(Mono.class))).thenReturn(Mono.empty());
        when(session.close()).thenReturn(Mono.empty());
        
        return session;
    }
}