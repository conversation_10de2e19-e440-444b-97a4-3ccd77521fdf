package com.example.mqtt.websocket.service.impl;

import com.example.mqtt.websocket.config.properties.EncryptionProperties;
import com.example.mqtt.websocket.service.KeyDeletionException;
import com.example.mqtt.websocket.service.KeyGenerationException;
import com.example.mqtt.websocket.service.KeyNotFoundException;
import com.example.mqtt.websocket.service.KeyRotationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.crypto.SecretKey;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RedisKeyManagerTest {
    
    @Mock
    private ReactiveRedisTemplate<String, String> redisTemplate;
    
    @Mock
    private ReactiveValueOperations<String, String> valueOperations;
    
    private EncryptionProperties encryptionProperties;
    private RedisKeyManager keyManager;
    
    @BeforeEach
    void setUp() {
        encryptionProperties = new EncryptionProperties();
        encryptionProperties.setAlgorithm("AES");
        encryptionProperties.setKeyLength(256);
        encryptionProperties.setKeyRotationInterval(86400000L); // 24 hours
        
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        
        keyManager = new RedisKeyManager(redisTemplate, encryptionProperties);
    }
    
    @Test
    void shouldGenerateNewKey() throws KeyGenerationException {
        // Given
        String keyId = "test-key";
        when(valueOperations.set(anyString(), anyString())).thenReturn(Mono.just(true));
        
        // When
        SecretKey result = keyManager.generateKey(keyId);
        
        // Then
        assertNotNull(result);
        assertEquals("AES", result.getAlgorithm());
        verify(valueOperations, times(2)).set(anyString(), anyString());
    }
    
    @Test
    void shouldRetrieveExistingKey() throws KeyNotFoundException {
        // Given
        String keyId = "test-key";
        String encodedKey = "dGVzdC1rZXktZGF0YQ=="; // base64 encoded test data
        when(valueOperations.get("encryption:keys:" + keyId))
            .thenReturn(Mono.just(encodedKey));
        
        // When
        SecretKey result = keyManager.getKey(keyId);
        
        // Then
        assertNotNull(result);
        assertEquals("AES", result.getAlgorithm());
    }
    
    @Test
    void shouldThrowExceptionWhenKeyNotFound() {
        // Given
        String keyId = "non-existent-key";
        when(valueOperations.get("encryption:keys:" + keyId))
            .thenReturn(Mono.empty());
        
        // When & Then
        assertThrows(KeyNotFoundException.class, () -> keyManager.getKey(keyId));
    }
    
    @Test
    void shouldRotateKey() throws Exception {
        // Given
        String keyId = "test-key";
        when(redisTemplate.hasKey(anyString())).thenReturn(Mono.just(true));
        when(redisTemplate.delete(anyString())).thenReturn(Mono.just(1L));
        when(valueOperations.set(anyString(), anyString())).thenReturn(Mono.just(true));
        
        // When
        SecretKey result = keyManager.rotateKey(keyId);
        
        // Then
        assertNotNull(result);
        verify(redisTemplate, times(2)).delete(anyString()); // key and metadata
        verify(valueOperations, times(2)).set(anyString(), anyString()); // new key and metadata
    }
    
    @Test
    void shouldDeleteKey() throws KeyDeletionException {
        // Given
        String keyId = "test-key";
        when(redisTemplate.delete(anyString())).thenReturn(Mono.just(1L));
        
        // When
        keyManager.deleteKey(keyId);
        
        // Then
        verify(redisTemplate, times(2)).delete(anyString());
    }
    
    @Test
    void shouldCheckKeyExists() {
        // Given
        String keyId = "test-key";
        when(redisTemplate.hasKey("encryption:keys:" + keyId))
            .thenReturn(Mono.just(true));
        
        // When
        boolean result = keyManager.keyExists(keyId);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void shouldReturnFalseWhenKeyDoesNotExist() {
        // Given
        String keyId = "non-existent-key";
        when(redisTemplate.hasKey("encryption:keys:" + keyId))
            .thenReturn(Mono.just(false));
        
        // When
        boolean result = keyManager.keyExists(keyId);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void shouldGetAllKeyIds() {
        // Given
        when(redisTemplate.keys("encryption:keys:*"))
            .thenReturn(Flux.just("encryption:keys:key1", "encryption:keys:key2"));
        
        // When
        Set<String> result = keyManager.getAllKeyIds();
        
        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("key1"));
        assertTrue(result.contains("key2"));
    }
    
    @Test
    void shouldGetKeyCreationTime() throws KeyNotFoundException {
        // Given
        String keyId = "test-key";
        String timestamp = LocalDateTime.now().toString();
        when(valueOperations.get("encryption:metadata:" + keyId))
            .thenReturn(Mono.just(timestamp));
        
        // When
        LocalDateTime result = keyManager.getKeyCreationTime(keyId);
        
        // Then
        assertNotNull(result);
    }
    
    @Test
    void shouldDetermineIfKeyNeedsRotation() throws KeyNotFoundException {
        // Given
        String keyId = "test-key";
        LocalDateTime oldTime = LocalDateTime.now().minusDays(2); // 2 days ago
        when(valueOperations.get("encryption:metadata:" + keyId))
            .thenReturn(Mono.just(oldTime.toString()));
        
        // When
        boolean result = keyManager.shouldRotateKey(keyId);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void shouldNotRotateRecentKey() throws KeyNotFoundException {
        // Given
        String keyId = "test-key";
        LocalDateTime recentTime = LocalDateTime.now().minusHours(1); // 1 hour ago
        when(valueOperations.get("encryption:metadata:" + keyId))
            .thenReturn(Mono.just(recentTime.toString()));
        
        // When
        boolean result = keyManager.shouldRotateKey(keyId);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void shouldCleanupExpiredKeys() {
        // Given
        LocalDateTime oldTime = LocalDateTime.now().minusDays(2);
        LocalDateTime recentTime = LocalDateTime.now().minusHours(1);
        
        when(redisTemplate.keys("encryption:keys:*"))
            .thenReturn(Flux.just("encryption:keys:oldKey", "encryption:keys:recentKey"));
        when(valueOperations.get("encryption:metadata:oldKey"))
            .thenReturn(Mono.just(oldTime.toString()));
        when(valueOperations.get("encryption:metadata:recentKey"))
            .thenReturn(Mono.just(recentTime.toString()));
        when(redisTemplate.delete(anyString())).thenReturn(Mono.just(1L));
        
        // When
        keyManager.cleanupExpiredKeys();
        
        // Then
        verify(redisTemplate, times(2)).delete(anyString()); // Only old key should be deleted
    }
}