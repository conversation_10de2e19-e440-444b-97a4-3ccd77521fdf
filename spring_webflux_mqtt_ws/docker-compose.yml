version: '3.8'

services:
  # Application instances
  app1:
    build: .
    container_name: mqtt-websocket-app1
    hostname: app1
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - SPRING_PROFILES_ACTIVE=docker,cluster
      - SERVER_PORT=8080
      - MANAGEMENT_SERVER_PORT=9090
      - INSTANCE_ID=app1
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MQTT_BROKER_URL=tcp://mosquitto:1883
      - CLUSTER_ENABLED=true
      - LOGGING_LEVEL_ROOT=INFO
      - LOGGING_LEVEL_COM_EXAMPLE=DEBUG
    depends_on:
      redis:
        condition: service_healthy
      mosquitto:
        condition: service_started
    volumes:
      - ./logs/app1:/app/logs
      - ./config:/app/config:ro
    networks:
      - mqtt-websocket-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  app2:
    build: .
    container_name: mqtt-websocket-app2
    hostname: app2
    ports:
      - "8081:8080"
      - "9091:9090"
    environment:
      - SPRING_PROFILES_ACTIVE=docker,cluster
      - SERVER_PORT=8080
      - MANAGEMENT_SERVER_PORT=9090
      - INSTANCE_ID=app2
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MQTT_BROKER_URL=tcp://mosquitto:1883
      - CLUSTER_ENABLED=true
      - LOGGING_LEVEL_ROOT=INFO
      - LOGGING_LEVEL_COM_EXAMPLE=DEBUG
    depends_on:
      redis:
        condition: service_healthy
      mosquitto:
        condition: service_started
    volumes:
      - ./logs/app2:/app/logs
      - ./config:/app/config:ro
    networks:
      - mqtt-websocket-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Redis for cluster state management
  redis:
    image: redis:7.2-alpine
    container_name: mqtt-websocket-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - mqtt-websocket-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # MQTT Broker
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: mqtt-websocket-mosquitto
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./config/mosquitto.conf:/mosquitto/config/mosquitto.conf:ro
      - mosquitto-data:/mosquitto/data
      - mosquitto-logs:/mosquitto/log
    networks:
      - mqtt-websocket-network
    restart: unless-stopped

  # Load Balancer
  nginx:
    image: nginx:alpine
    container_name: mqtt-websocket-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
    depends_on:
      - app1
      - app2
    networks:
      - mqtt-websocket-network
    restart: unless-stopped

  # Monitoring and Logging
  prometheus:
    image: prom/prometheus:latest
    container_name: mqtt-websocket-prometheus
    ports:
      - "9092:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - mqtt-websocket-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: mqtt-websocket-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - mqtt-websocket-network
    restart: unless-stopped

  # Log aggregation
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: mqtt-websocket-fluentd
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ./config/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/var/log/containers
    networks:
      - mqtt-websocket-network
    restart: unless-stopped

volumes:
  redis-data:
  mosquitto-data:
  mosquitto-logs:
  prometheus-data:
  grafana-data:

networks:
  mqtt-websocket-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16