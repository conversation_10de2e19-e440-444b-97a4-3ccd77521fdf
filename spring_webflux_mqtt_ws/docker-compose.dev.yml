# Development environment overrides
version: '3.8'

services:
  app1:
    environment:
      - SPRING_PROFILES_ACTIVE=docker,dev
      - LOGGING_LEVEL_ROOT=DEBUG
      - LOGGING_LEVEL_COM_EXAMPLE=TRACE
      - SPRING_DEVTOOLS_RESTART_ENABLED=true
    ports:
      - "5005:5005"  # Debug port
    command: >
      sh -c "exec java $$JAVA_OPTS 
             -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
             -jar app.jar"
    volumes:
      - ./src:/app/src:ro
      - ./target:/app/target
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'

  app2:
    environment:
      - SPRING_PROFILES_ACTIVE=docker,dev
      - LOGGING_LEVEL_ROOT=DEBUG
      - LOGGING_LEVEL_COM_EXAMPLE=TRACE
      - SPRING_DEVTOOLS_RESTART_ENABLED=true
    ports:
      - "5006:5005"  # Debug port
    command: >
      sh -c "exec java $$JAVA_OPTS 
             -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
             -jar app.jar"
    volumes:
      - ./src:/app/src:ro
      - ./target:/app/target
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'

  # Development tools
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: mqtt-websocket-redis-commander
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - mqtt-websocket-network

  # MQTT client for testing
  mqtt-client:
    image: eclipse-mosquitto:2.0
    container_name: mqtt-websocket-client
    depends_on:
      - mosquitto
    networks:
      - mqtt-websocket-network
    command: >
      sh -c "sleep 10 && 
             mosquitto_pub -h mosquitto -t 'sensor/test/data' -m '{\"temperature\":25.5,\"humidity\":60.0}' -r &&
             mosquitto_sub -h mosquitto -t 'response/+/data' -v"