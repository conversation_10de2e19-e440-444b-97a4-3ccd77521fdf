# Spring Integration MQTT WebSocket

综合消息处理平台，整合 MQTT、WebSocket、Protobuf 和加密功能，支持集群部署。

## 技术栈

- **Spring Boot 3.2.0** - 应用框架
- **Spring WebFlux** - 响应式 Web 框架  
- **Spring Integration** - 企业集成模式
- **Spring WebSocket** - WebSocket 支持
- **Protocol Buffers** - 消息序列化
- **Redis** - 集群状态共享和消息缓存
- **Eclipse Paho MQTT** - MQTT 客户端

## 项目结构

```
src/
├── main/
│   ├── java/com/example/mqtt/websocket/
│   │   ├── config/           # 配置类
│   │   │   └── properties/   # 配置属性类
│   │   ├── controller/       # WebFlux 控制器
│   │   ├── handler/          # 消息处理器
│   │   ├── integration/      # Spring Integration 组件
│   │   ├── model/            # 数据模型
│   │   └── service/          # 业务服务
│   ├── proto/                # Protobuf 定义文件
│   └── resources/            # 配置文件
└── test/                     # 测试代码
```

## 配置文件

- `application.yml` - 主配置文件
- `application-dev.yml` - 开发环境配置
- `application-prod.yml` - 生产环境配置  
- `application-test.yml` - 测试环境配置

## 构建和运行

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动应用程序
mvn spring-boot:run

# 打包应用程序
mvn clean package
```

## 环境变量

生产环境需要设置以下环境变量：

- `REDIS_HOST` - Redis 主机地址
- `REDIS_PASSWORD` - Redis 密码
- `MQTT_BROKER_URL` - MQTT 代理地址
- `MQTT_USERNAME` - MQTT 用户名
- `MQTT_PASSWORD` - MQTT 密码
- `ENCRYPTION_MASTER_KEY` - 加密主密钥
- `WEBSOCKET_ALLOWED_ORIGINS` - WebSocket 允许的源