// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: messages.proto

// Protobuf Java Version: 3.25.1
package com.example.mqtt.websocket.model.proto;

public interface WebSocketMessageOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.example.mqtt.websocket.proto.WebSocketMessage)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string session_id = 1;</code>
   * @return The sessionId.
   */
  java.lang.String getSessionId();
  /**
   * <code>string session_id = 1;</code>
   * @return The bytes for sessionId.
   */
  com.google.protobuf.ByteString
      getSessionIdBytes();

  /**
   * <code>string user_id = 2;</code>
   * @return The userId.
   */
  java.lang.String getUserId();
  /**
   * <code>string user_id = 2;</code>
   * @return The bytes for userId.
   */
  com.google.protobuf.ByteString
      getUserIdBytes();

  /**
   * <code>.com.example.mqtt.websocket.proto.WebSocketMessage.MessageType type = 3;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.example.mqtt.websocket.proto.WebSocketMessage.MessageType type = 3;</code>
   * @return The type.
   */
  com.example.mqtt.websocket.model.proto.WebSocketMessage.MessageType getType();

  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   * @return Whether the message field is set.
   */
  boolean hasMessage();
  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   * @return The message.
   */
  com.example.mqtt.websocket.model.proto.BaseMessage getMessage();
  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   */
  com.example.mqtt.websocket.model.proto.BaseMessageOrBuilder getMessageOrBuilder();
}
