// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: messages.proto

// Protobuf Java Version: 3.25.1
package com.example.mqtt.websocket.model.proto;

public interface BaseMessageOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.example.mqtt.websocket.proto.BaseMessage)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>int64 timestamp = 2;</code>
   * @return The timestamp.
   */
  long getTimestamp();

  /**
   * <code>string type = 3;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <code>string type = 3;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <code>string source = 4;</code>
   * @return The source.
   */
  java.lang.String getSource();
  /**
   * <code>string source = 4;</code>
   * @return The bytes for source.
   */
  com.google.protobuf.ByteString
      getSourceBytes();

  /**
   * <code>map&lt;string, string&gt; headers = 5;</code>
   */
  int getHeadersCount();
  /**
   * <code>map&lt;string, string&gt; headers = 5;</code>
   */
  boolean containsHeaders(
      java.lang.String key);
  /**
   * Use {@link #getHeadersMap()} instead.
   */
  @java.lang.Deprecated
  java.util.Map<java.lang.String, java.lang.String>
  getHeaders();
  /**
   * <code>map&lt;string, string&gt; headers = 5;</code>
   */
  java.util.Map<java.lang.String, java.lang.String>
  getHeadersMap();
  /**
   * <code>map&lt;string, string&gt; headers = 5;</code>
   */
  /* nullable */
java.lang.String getHeadersOrDefault(
      java.lang.String key,
      /* nullable */
java.lang.String defaultValue);
  /**
   * <code>map&lt;string, string&gt; headers = 5;</code>
   */
  java.lang.String getHeadersOrThrow(
      java.lang.String key);

  /**
   * <code>bytes payload = 6;</code>
   * @return The payload.
   */
  com.google.protobuf.ByteString getPayload();

  /**
   * <code>bool encrypted = 7;</code>
   * @return The encrypted.
   */
  boolean getEncrypted();

  /**
   * <code>string signature = 8;</code>
   * @return The signature.
   */
  java.lang.String getSignature();
  /**
   * <code>string signature = 8;</code>
   * @return The bytes for signature.
   */
  com.google.protobuf.ByteString
      getSignatureBytes();
}
