// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: messages.proto

// Protobuf Java Version: 3.25.1
package com.example.mqtt.websocket.model.proto;

public interface MqttMessageOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.example.mqtt.websocket.proto.MqttMessage)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string topic = 1;</code>
   * @return The topic.
   */
  java.lang.String getTopic();
  /**
   * <code>string topic = 1;</code>
   * @return The bytes for topic.
   */
  com.google.protobuf.ByteString
      getTopicBytes();

  /**
   * <code>int32 qos = 2;</code>
   * @return The qos.
   */
  int getQos();

  /**
   * <code>bool retained = 3;</code>
   * @return The retained.
   */
  boolean getRetained();

  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   * @return Whether the message field is set.
   */
  boolean hasMessage();
  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   * @return The message.
   */
  com.example.mqtt.websocket.model.proto.BaseMessage getMessage();
  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   */
  com.example.mqtt.websocket.model.proto.BaseMessageOrBuilder getMessageOrBuilder();
}
