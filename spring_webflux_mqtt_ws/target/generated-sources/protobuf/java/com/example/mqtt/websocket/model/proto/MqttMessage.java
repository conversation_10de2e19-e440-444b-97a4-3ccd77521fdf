// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: messages.proto

// Protobuf Java Version: 3.25.1
package com.example.mqtt.websocket.model.proto;

/**
 * <pre>
 * MQTT 消息结构
 * </pre>
 *
 * Protobuf type {@code com.example.mqtt.websocket.proto.MqttMessage}
 */
public final class MqttMessage extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.example.mqtt.websocket.proto.MqttMessage)
    MqttMessageOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MqttMessage.newBuilder() to construct.
  private MqttMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MqttMessage() {
    topic_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MqttMessage();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.example.mqtt.websocket.model.proto.Messages.internal_static_com_example_mqtt_websocket_proto_MqttMessage_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.example.mqtt.websocket.model.proto.Messages.internal_static_com_example_mqtt_websocket_proto_MqttMessage_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.example.mqtt.websocket.model.proto.MqttMessage.class, com.example.mqtt.websocket.model.proto.MqttMessage.Builder.class);
  }

  private int bitField0_;
  public static final int TOPIC_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object topic_ = "";
  /**
   * <code>string topic = 1;</code>
   * @return The topic.
   */
  @java.lang.Override
  public java.lang.String getTopic() {
    java.lang.Object ref = topic_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      topic_ = s;
      return s;
    }
  }
  /**
   * <code>string topic = 1;</code>
   * @return The bytes for topic.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTopicBytes() {
    java.lang.Object ref = topic_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      topic_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int QOS_FIELD_NUMBER = 2;
  private int qos_ = 0;
  /**
   * <code>int32 qos = 2;</code>
   * @return The qos.
   */
  @java.lang.Override
  public int getQos() {
    return qos_;
  }

  public static final int RETAINED_FIELD_NUMBER = 3;
  private boolean retained_ = false;
  /**
   * <code>bool retained = 3;</code>
   * @return The retained.
   */
  @java.lang.Override
  public boolean getRetained() {
    return retained_;
  }

  public static final int MESSAGE_FIELD_NUMBER = 4;
  private com.example.mqtt.websocket.model.proto.BaseMessage message_;
  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   * @return Whether the message field is set.
   */
  @java.lang.Override
  public boolean hasMessage() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   * @return The message.
   */
  @java.lang.Override
  public com.example.mqtt.websocket.model.proto.BaseMessage getMessage() {
    return message_ == null ? com.example.mqtt.websocket.model.proto.BaseMessage.getDefaultInstance() : message_;
  }
  /**
   * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
   */
  @java.lang.Override
  public com.example.mqtt.websocket.model.proto.BaseMessageOrBuilder getMessageOrBuilder() {
    return message_ == null ? com.example.mqtt.websocket.model.proto.BaseMessage.getDefaultInstance() : message_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(topic_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, topic_);
    }
    if (qos_ != 0) {
      output.writeInt32(2, qos_);
    }
    if (retained_ != false) {
      output.writeBool(3, retained_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(4, getMessage());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(topic_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, topic_);
    }
    if (qos_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, qos_);
    }
    if (retained_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, retained_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getMessage());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.example.mqtt.websocket.model.proto.MqttMessage)) {
      return super.equals(obj);
    }
    com.example.mqtt.websocket.model.proto.MqttMessage other = (com.example.mqtt.websocket.model.proto.MqttMessage) obj;

    if (!getTopic()
        .equals(other.getTopic())) return false;
    if (getQos()
        != other.getQos()) return false;
    if (getRetained()
        != other.getRetained()) return false;
    if (hasMessage() != other.hasMessage()) return false;
    if (hasMessage()) {
      if (!getMessage()
          .equals(other.getMessage())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TOPIC_FIELD_NUMBER;
    hash = (53 * hash) + getTopic().hashCode();
    hash = (37 * hash) + QOS_FIELD_NUMBER;
    hash = (53 * hash) + getQos();
    hash = (37 * hash) + RETAINED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getRetained());
    if (hasMessage()) {
      hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
      hash = (53 * hash) + getMessage().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.example.mqtt.websocket.model.proto.MqttMessage parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.example.mqtt.websocket.model.proto.MqttMessage parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.example.mqtt.websocket.model.proto.MqttMessage parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.example.mqtt.websocket.model.proto.MqttMessage prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * MQTT 消息结构
   * </pre>
   *
   * Protobuf type {@code com.example.mqtt.websocket.proto.MqttMessage}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.example.mqtt.websocket.proto.MqttMessage)
      com.example.mqtt.websocket.model.proto.MqttMessageOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.example.mqtt.websocket.model.proto.Messages.internal_static_com_example_mqtt_websocket_proto_MqttMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.example.mqtt.websocket.model.proto.Messages.internal_static_com_example_mqtt_websocket_proto_MqttMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.example.mqtt.websocket.model.proto.MqttMessage.class, com.example.mqtt.websocket.model.proto.MqttMessage.Builder.class);
    }

    // Construct using com.example.mqtt.websocket.model.proto.MqttMessage.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getMessageFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      topic_ = "";
      qos_ = 0;
      retained_ = false;
      message_ = null;
      if (messageBuilder_ != null) {
        messageBuilder_.dispose();
        messageBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.example.mqtt.websocket.model.proto.Messages.internal_static_com_example_mqtt_websocket_proto_MqttMessage_descriptor;
    }

    @java.lang.Override
    public com.example.mqtt.websocket.model.proto.MqttMessage getDefaultInstanceForType() {
      return com.example.mqtt.websocket.model.proto.MqttMessage.getDefaultInstance();
    }

    @java.lang.Override
    public com.example.mqtt.websocket.model.proto.MqttMessage build() {
      com.example.mqtt.websocket.model.proto.MqttMessage result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.example.mqtt.websocket.model.proto.MqttMessage buildPartial() {
      com.example.mqtt.websocket.model.proto.MqttMessage result = new com.example.mqtt.websocket.model.proto.MqttMessage(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.example.mqtt.websocket.model.proto.MqttMessage result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.topic_ = topic_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.qos_ = qos_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.retained_ = retained_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.message_ = messageBuilder_ == null
            ? message_
            : messageBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.example.mqtt.websocket.model.proto.MqttMessage) {
        return mergeFrom((com.example.mqtt.websocket.model.proto.MqttMessage)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.example.mqtt.websocket.model.proto.MqttMessage other) {
      if (other == com.example.mqtt.websocket.model.proto.MqttMessage.getDefaultInstance()) return this;
      if (!other.getTopic().isEmpty()) {
        topic_ = other.topic_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.getQos() != 0) {
        setQos(other.getQos());
      }
      if (other.getRetained() != false) {
        setRetained(other.getRetained());
      }
      if (other.hasMessage()) {
        mergeMessage(other.getMessage());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              topic_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              qos_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              retained_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              input.readMessage(
                  getMessageFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object topic_ = "";
    /**
     * <code>string topic = 1;</code>
     * @return The topic.
     */
    public java.lang.String getTopic() {
      java.lang.Object ref = topic_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        topic_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string topic = 1;</code>
     * @return The bytes for topic.
     */
    public com.google.protobuf.ByteString
        getTopicBytes() {
      java.lang.Object ref = topic_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        topic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string topic = 1;</code>
     * @param value The topic to set.
     * @return This builder for chaining.
     */
    public Builder setTopic(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      topic_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string topic = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTopic() {
      topic_ = getDefaultInstance().getTopic();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string topic = 1;</code>
     * @param value The bytes for topic to set.
     * @return This builder for chaining.
     */
    public Builder setTopicBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      topic_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int qos_ ;
    /**
     * <code>int32 qos = 2;</code>
     * @return The qos.
     */
    @java.lang.Override
    public int getQos() {
      return qos_;
    }
    /**
     * <code>int32 qos = 2;</code>
     * @param value The qos to set.
     * @return This builder for chaining.
     */
    public Builder setQos(int value) {

      qos_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>int32 qos = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearQos() {
      bitField0_ = (bitField0_ & ~0x00000002);
      qos_ = 0;
      onChanged();
      return this;
    }

    private boolean retained_ ;
    /**
     * <code>bool retained = 3;</code>
     * @return The retained.
     */
    @java.lang.Override
    public boolean getRetained() {
      return retained_;
    }
    /**
     * <code>bool retained = 3;</code>
     * @param value The retained to set.
     * @return This builder for chaining.
     */
    public Builder setRetained(boolean value) {

      retained_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>bool retained = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRetained() {
      bitField0_ = (bitField0_ & ~0x00000004);
      retained_ = false;
      onChanged();
      return this;
    }

    private com.example.mqtt.websocket.model.proto.BaseMessage message_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.example.mqtt.websocket.model.proto.BaseMessage, com.example.mqtt.websocket.model.proto.BaseMessage.Builder, com.example.mqtt.websocket.model.proto.BaseMessageOrBuilder> messageBuilder_;
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     * @return Whether the message field is set.
     */
    public boolean hasMessage() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     * @return The message.
     */
    public com.example.mqtt.websocket.model.proto.BaseMessage getMessage() {
      if (messageBuilder_ == null) {
        return message_ == null ? com.example.mqtt.websocket.model.proto.BaseMessage.getDefaultInstance() : message_;
      } else {
        return messageBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     */
    public Builder setMessage(com.example.mqtt.websocket.model.proto.BaseMessage value) {
      if (messageBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        message_ = value;
      } else {
        messageBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     */
    public Builder setMessage(
        com.example.mqtt.websocket.model.proto.BaseMessage.Builder builderForValue) {
      if (messageBuilder_ == null) {
        message_ = builderForValue.build();
      } else {
        messageBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     */
    public Builder mergeMessage(com.example.mqtt.websocket.model.proto.BaseMessage value) {
      if (messageBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          message_ != null &&
          message_ != com.example.mqtt.websocket.model.proto.BaseMessage.getDefaultInstance()) {
          getMessageBuilder().mergeFrom(value);
        } else {
          message_ = value;
        }
      } else {
        messageBuilder_.mergeFrom(value);
      }
      if (message_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     */
    public Builder clearMessage() {
      bitField0_ = (bitField0_ & ~0x00000008);
      message_ = null;
      if (messageBuilder_ != null) {
        messageBuilder_.dispose();
        messageBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     */
    public com.example.mqtt.websocket.model.proto.BaseMessage.Builder getMessageBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return getMessageFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     */
    public com.example.mqtt.websocket.model.proto.BaseMessageOrBuilder getMessageOrBuilder() {
      if (messageBuilder_ != null) {
        return messageBuilder_.getMessageOrBuilder();
      } else {
        return message_ == null ?
            com.example.mqtt.websocket.model.proto.BaseMessage.getDefaultInstance() : message_;
      }
    }
    /**
     * <code>.com.example.mqtt.websocket.proto.BaseMessage message = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.example.mqtt.websocket.model.proto.BaseMessage, com.example.mqtt.websocket.model.proto.BaseMessage.Builder, com.example.mqtt.websocket.model.proto.BaseMessageOrBuilder> 
        getMessageFieldBuilder() {
      if (messageBuilder_ == null) {
        messageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.example.mqtt.websocket.model.proto.BaseMessage, com.example.mqtt.websocket.model.proto.BaseMessage.Builder, com.example.mqtt.websocket.model.proto.BaseMessageOrBuilder>(
                getMessage(),
                getParentForChildren(),
                isClean());
        message_ = null;
      }
      return messageBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.example.mqtt.websocket.proto.MqttMessage)
  }

  // @@protoc_insertion_point(class_scope:com.example.mqtt.websocket.proto.MqttMessage)
  private static final com.example.mqtt.websocket.model.proto.MqttMessage DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.example.mqtt.websocket.model.proto.MqttMessage();
  }

  public static com.example.mqtt.websocket.model.proto.MqttMessage getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MqttMessage>
      PARSER = new com.google.protobuf.AbstractParser<MqttMessage>() {
    @java.lang.Override
    public MqttMessage parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MqttMessage> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MqttMessage> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.example.mqtt.websocket.model.proto.MqttMessage getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

