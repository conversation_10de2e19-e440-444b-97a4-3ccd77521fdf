// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: messages.proto

// Protobuf Java Version: 3.25.1
package com.example.mqtt.websocket.model.proto;

public final class Messages {
  private Messages() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_example_mqtt_websocket_proto_BaseMessage_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_example_mqtt_websocket_proto_BaseMessage_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_example_mqtt_websocket_proto_BaseMessage_HeadersEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_example_mqtt_websocket_proto_BaseMessage_HeadersEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_example_mqtt_websocket_proto_MqttMessage_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_example_mqtt_websocket_proto_MqttMessage_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_example_mqtt_websocket_proto_WebSocketMessage_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_example_mqtt_websocket_proto_WebSocketMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016messages.proto\022 com.example.mqtt.webso" +
      "cket.proto\"\376\001\n\013BaseMessage\022\n\n\002id\030\001 \001(\t\022\021" +
      "\n\ttimestamp\030\002 \001(\003\022\014\n\004type\030\003 \001(\t\022\016\n\006sourc" +
      "e\030\004 \001(\t\022K\n\007headers\030\005 \003(\0132:.com.example.m" +
      "qtt.websocket.proto.BaseMessage.HeadersE" +
      "ntry\022\017\n\007payload\030\006 \001(\014\022\021\n\tencrypted\030\007 \001(\010" +
      "\022\021\n\tsignature\030\010 \001(\t\032.\n\014HeadersEntry\022\013\n\003k" +
      "ey\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"{\n\013MqttMessa" +
      "ge\022\r\n\005topic\030\001 \001(\t\022\013\n\003qos\030\002 \001(\005\022\020\n\010retain" +
      "ed\030\003 \001(\010\022>\n\007message\030\004 \001(\0132-.com.example." +
      "mqtt.websocket.proto.BaseMessage\"\374\001\n\020Web" +
      "SocketMessage\022\022\n\nsession_id\030\001 \001(\t\022\017\n\007use" +
      "r_id\030\002 \001(\t\022L\n\004type\030\003 \001(\0162>.com.example.m" +
      "qtt.websocket.proto.WebSocketMessage.Mes" +
      "sageType\022>\n\007message\030\004 \001(\0132-.com.example." +
      "mqtt.websocket.proto.BaseMessage\"5\n\013Mess" +
      "ageType\022\r\n\tBROADCAST\020\000\022\013\n\007UNICAST\020\001\022\n\n\006S" +
      "YSTEM\020\002B4\n&com.example.mqtt.websocket.mo" +
      "del.protoB\010MessagesP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_example_mqtt_websocket_proto_BaseMessage_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_example_mqtt_websocket_proto_BaseMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_example_mqtt_websocket_proto_BaseMessage_descriptor,
        new java.lang.String[] { "Id", "Timestamp", "Type", "Source", "Headers", "Payload", "Encrypted", "Signature", });
    internal_static_com_example_mqtt_websocket_proto_BaseMessage_HeadersEntry_descriptor =
      internal_static_com_example_mqtt_websocket_proto_BaseMessage_descriptor.getNestedTypes().get(0);
    internal_static_com_example_mqtt_websocket_proto_BaseMessage_HeadersEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_example_mqtt_websocket_proto_BaseMessage_HeadersEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_example_mqtt_websocket_proto_MqttMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_example_mqtt_websocket_proto_MqttMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_example_mqtt_websocket_proto_MqttMessage_descriptor,
        new java.lang.String[] { "Topic", "Qos", "Retained", "Message", });
    internal_static_com_example_mqtt_websocket_proto_WebSocketMessage_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_example_mqtt_websocket_proto_WebSocketMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_example_mqtt_websocket_proto_WebSocketMessage_descriptor,
        new java.lang.String[] { "SessionId", "UserId", "Type", "Message", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
