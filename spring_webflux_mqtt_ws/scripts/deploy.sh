#!/bin/bash

# Deployment script for MQTT-WebSocket application
# Usage: ./scripts/deploy.sh [dev|test|prod] [action]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DEFAULT_ENV="dev"
DEFAULT_ACTION="up"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARN:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Help function
show_help() {
    cat << EOF
MQTT-WebSocket Application Deployment Script

Usage: $0 [ENVIRONMENT] [ACTION]

ENVIRONMENTS:
    dev     - Development environment (default)
    test    - Test environment
    prod    - Production environment

ACTIONS:
    up      - Start services (default)
    down    - Stop services
    restart - Restart services
    build   - Build images
    logs    - Show logs
    status  - Show service status
    clean   - Clean up containers and volumes
    health  - Run health checks

Examples:
    $0                    # Start dev environment
    $0 prod up           # Start production environment
    $0 dev logs          # Show development logs
    $0 prod health       # Run production health checks

EOF
}

# Validate environment
validate_environment() {
    local env=$1
    case $env in
        dev|test|prod)
            return 0
            ;;
        *)
            error "Invalid environment: $env"
            error "Valid environments: dev, test, prod"
            return 1
            ;;
    esac
}

# Get Docker Compose files for environment
get_compose_files() {
    local env=$1
    local base_file="docker-compose.yml"
    local env_file="docker-compose.${env}.yml"
    
    if [ -f "$PROJECT_DIR/$env_file" ]; then
        echo "-f $base_file -f $env_file"
    else
        echo "-f $base_file"
    fi
}

# Prepare environment
prepare_environment() {
    local env=$1
    
    log "Preparing $env environment..."
    
    # Create necessary directories
    mkdir -p "$PROJECT_DIR/logs/app1" "$PROJECT_DIR/logs/app2" 
    mkdir -p "$PROJECT_DIR/logs/aggregated" "$PROJECT_DIR/logs/buffer"
    mkdir -p "$PROJECT_DIR/config/ssl" 
    mkdir -p "$PROJECT_DIR/config/grafana/dashboards" 
    mkdir -p "$PROJECT_DIR/config/grafana/datasources"
    
    # Set environment variables
    export COMPOSE_PROJECT_NAME="mqtt-websocket-${env}"
    export ENVIRONMENT=$env
    
    # Load environment-specific variables
    if [ -f "$PROJECT_DIR/.env.$env" ]; then
        log "Loading environment variables from .env.$env"
        set -a
        source "$PROJECT_DIR/.env.$env"
        set +a
    elif [ -f "$PROJECT_DIR/.env" ]; then
        log "Loading environment variables from .env"
        set -a
        source "$PROJECT_DIR/.env"
        set +a
    fi
}

# Build images
build_images() {
    local env=$1
    local compose_files=$(get_compose_files $env)
    
    log "Building images for $env environment..."
    
    cd "$PROJECT_DIR"
    docker-compose $compose_files build --no-cache
    
    log "Images built successfully"
}

# Start services
start_services() {
    local env=$1
    local compose_files=$(get_compose_files $env)
    
    log "Starting services for $env environment..."
    
    cd "$PROJECT_DIR"
    docker-compose $compose_files up -d
    
    log "Services started successfully"
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30
    
    # Show service status
    docker-compose $compose_files ps
}

# Stop services
stop_services() {
    local env=$1
    local compose_files=$(get_compose_files $env)
    
    log "Stopping services for $env environment..."
    
    cd "$PROJECT_DIR"
    docker-compose $compose_files down
    
    log "Services stopped successfully"
}

# Restart services
restart_services() {
    local env=$1
    
    log "Restarting services for $env environment..."
    
    stop_services $env
    sleep 5
    start_services $env
    
    log "Services restarted successfully"
}

# Show logs
show_logs() {
    local env=$1
    local compose_files=$(get_compose_files $env)
    
    log "Showing logs for $env environment..."
    
    cd "$PROJECT_DIR"
    docker-compose $compose_files logs -f --tail=100
}

# Show service status
show_status() {
    local env=$1
    local compose_files=$(get_compose_files $env)
    
    log "Service status for $env environment:"
    
    cd "$PROJECT_DIR"
    docker-compose $compose_files ps
    
    echo
    log "Container resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Clean up
cleanup() {
    local env=$1
    local compose_files=$(get_compose_files $env)
    
    warn "This will remove all containers, networks, and volumes for $env environment"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Cleaning up $env environment..."
        
        cd "$PROJECT_DIR"
        docker-compose $compose_files down -v --remove-orphans
        docker system prune -f
        
        log "Cleanup completed"
    else
        log "Cleanup cancelled"
    fi
}

# Run health checks
run_health_checks() {
    local env=$1
    
    log "Running health checks for $env environment..."
    
    # Check if health check script exists
    if [ -f "$PROJECT_DIR/scripts/health-check.sh" ]; then
        # Set environment-specific variables for health check
        case $env in
            dev)
                export APP_HOST=localhost
                export APP_PORT=8080
                export MANAGEMENT_PORT=9090
                ;;
            prod)
                export APP_HOST=localhost
                export APP_PORT=80
                export MANAGEMENT_PORT=9090
                ;;
        esac
        
        "$PROJECT_DIR/scripts/health-check.sh"
    else
        warn "Health check script not found, performing basic checks..."
        
        # Basic health checks
        local health_url="http://localhost:9090/actuator/health"
        if curl -f -s "$health_url" > /dev/null; then
            log "✓ Application health check passed"
        else
            error "✗ Application health check failed"
            return 1
        fi
    fi
}

# Main function
main() {
    local env=${1:-$DEFAULT_ENV}
    local action=${2:-$DEFAULT_ACTION}
    
    # Show help if requested
    if [[ "$env" == "-h" || "$env" == "--help" || "$env" == "help" ]]; then
        show_help
        exit 0
    fi
    
    # Validate environment
    if ! validate_environment "$env"; then
        exit 1
    fi
    
    # Prepare environment
    prepare_environment "$env"
    
    # Execute action
    case $action in
        up|start)
            start_services "$env"
            ;;
        down|stop)
            stop_services "$env"
            ;;
        restart)
            restart_services "$env"
            ;;
        build)
            build_images "$env"
            ;;
        logs)
            show_logs "$env"
            ;;
        status|ps)
            show_status "$env"
            ;;
        clean|cleanup)
            cleanup "$env"
            ;;
        health|check)
            run_health_checks "$env"
            ;;
        *)
            error "Invalid action: $action"
            error "Valid actions: up, down, restart, build, logs, status, clean, health"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"