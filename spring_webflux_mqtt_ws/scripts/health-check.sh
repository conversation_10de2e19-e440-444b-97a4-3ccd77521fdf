#!/bin/bash

# Health check script for MQTT-WebSocket application
# This script performs comprehensive health checks

set -e

# Configuration
APP_HOST=${APP_HOST:-localhost}
APP_PORT=${APP_PORT:-8080}
MANAGEMENT_PORT=${MANAGEMENT_PORT:-9090}
TIMEOUT=${TIMEOUT:-10}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARN:${NC} $1"
}

# Check if service is responding
check_http_endpoint() {
    local url=$1
    local expected_status=${2:-200}
    local description=$3
    
    log "Checking $description: $url"
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/health_response --connect-timeout $TIMEOUT "$url"); then
        if [ "$response" = "$expected_status" ]; then
            log "✓ $description is healthy"
            return 0
        else
            error "✗ $description returned status $response (expected $expected_status)"
            return 1
        fi
    else
        error "✗ $description is unreachable"
        return 1
    fi
}

# Check application health endpoint
check_application_health() {
    log "=== Application Health Check ==="
    
    local health_url="http://$APP_HOST:$MANAGEMENT_PORT/actuator/health"
    
    if check_http_endpoint "$health_url" 200 "Application Health"; then
        # Parse health response for detailed status
        if command -v jq >/dev/null 2>&1; then
            local status=$(cat /tmp/health_response | jq -r '.status // "UNKNOWN"')
            local components=$(cat /tmp/health_response | jq -r '.components | keys[]' 2>/dev/null || echo "")
            
            log "Overall Status: $status"
            
            if [ -n "$components" ]; then
                log "Component Status:"
                for component in $components; do
                    local comp_status=$(cat /tmp/health_response | jq -r ".components.$component.status // \"UNKNOWN\"")
                    if [ "$comp_status" = "UP" ]; then
                        log "  ✓ $component: $comp_status"
                    else
                        warn "  ✗ $component: $comp_status"
                    fi
                done
            fi
        fi
        return 0
    else
        return 1
    fi
}

# Check readiness endpoint
check_readiness() {
    log "=== Readiness Check ==="
    check_http_endpoint "http://$APP_HOST:$MANAGEMENT_PORT/actuator/health/readiness" 200 "Application Readiness"
}

# Check liveness endpoint
check_liveness() {
    log "=== Liveness Check ==="
    check_http_endpoint "http://$APP_HOST:$MANAGEMENT_PORT/actuator/health/liveness" 200 "Application Liveness"
}

# Check metrics endpoint
check_metrics() {
    log "=== Metrics Check ==="
    check_http_endpoint "http://$APP_HOST:$MANAGEMENT_PORT/actuator/metrics" 200 "Metrics Endpoint"
}

# Check WebSocket endpoint
check_websocket() {
    log "=== WebSocket Check ==="
    
    # Simple WebSocket connection test using curl (if available)
    if command -v websocat >/dev/null 2>&1; then
        log "Testing WebSocket connection..."
        if timeout 5 websocat "ws://$APP_HOST:$APP_PORT/websocket" <<< '{"type":"ping"}' >/dev/null 2>&1; then
            log "✓ WebSocket connection successful"
            return 0
        else
            warn "✗ WebSocket connection failed"
            return 1
        fi
    else
        warn "websocat not available, skipping WebSocket test"
        return 0
    fi
}

# Check dependencies
check_dependencies() {
    log "=== Dependencies Check ==="
    
    # Check Redis connectivity (if configured)
    if [ -n "$REDIS_HOST" ]; then
        log "Checking Redis connectivity..."
        if command -v redis-cli >/dev/null 2>&1; then
            if redis-cli -h "${REDIS_HOST:-redis}" -p "${REDIS_PORT:-6379}" ping >/dev/null 2>&1; then
                log "✓ Redis is accessible"
            else
                error "✗ Redis is not accessible"
                return 1
            fi
        else
            warn "redis-cli not available, skipping Redis check"
        fi
    fi
    
    # Check MQTT broker connectivity (if configured)
    if [ -n "$MQTT_BROKER_URL" ]; then
        log "Checking MQTT broker connectivity..."
        local mqtt_host=$(echo "$MQTT_BROKER_URL" | sed 's|tcp://||' | cut -d: -f1)
        local mqtt_port=$(echo "$MQTT_BROKER_URL" | sed 's|tcp://||' | cut -d: -f2)
        
        if command -v nc >/dev/null 2>&1; then
            if nc -z "$mqtt_host" "${mqtt_port:-1883}" >/dev/null 2>&1; then
                log "✓ MQTT broker is accessible"
            else
                error "✗ MQTT broker is not accessible"
                return 1
            fi
        else
            warn "nc not available, skipping MQTT check"
        fi
    fi
    
    return 0
}

# Main health check function
main() {
    log "Starting comprehensive health check for MQTT-WebSocket application"
    log "Target: $APP_HOST:$APP_PORT (Management: $MANAGEMENT_PORT)"
    
    local exit_code=0
    
    # Run all health checks
    check_application_health || exit_code=1
    check_readiness || exit_code=1
    check_liveness || exit_code=1
    check_metrics || exit_code=1
    check_websocket || exit_code=1
    check_dependencies || exit_code=1
    
    # Summary
    echo
    if [ $exit_code -eq 0 ]; then
        log "=== Health Check PASSED ==="
        log "All checks completed successfully"
    else
        error "=== Health Check FAILED ==="
        error "One or more checks failed"
    fi
    
    # Cleanup
    rm -f /tmp/health_response
    
    exit $exit_code
}

# Handle script arguments
case "${1:-check}" in
    "check")
        main
        ;;
    "app")
        check_application_health
        ;;
    "ready")
        check_readiness
        ;;
    "live")
        check_liveness
        ;;
    "deps")
        check_dependencies
        ;;
    *)
        echo "Usage: $0 [check|app|ready|live|deps]"
        echo "  check - Run all health checks (default)"
        echo "  app   - Check application health only"
        echo "  ready - Check readiness only"
        echo "  live  - Check liveness only"
        echo "  deps  - Check dependencies only"
        exit 1
        ;;
esac