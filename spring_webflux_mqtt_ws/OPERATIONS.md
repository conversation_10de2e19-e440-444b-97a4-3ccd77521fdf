# MQTT-WebSocket 应用运维指南

## 概述

本文档提供 MQTT-WebSocket 应用的日常运维操作指南，包括监控、故障排除、性能调优和维护程序。

## 快速操作命令

### 基本操作

```bash
# 启动开发环境
./scripts/deploy.sh dev up

# 启动生产环境
./scripts/deploy.sh prod up

# 查看服务状态
./scripts/deploy.sh prod status

# 查看日志
./scripts/deploy.sh prod logs

# 健康检查
./scripts/deploy.sh prod health

# 重启服务
./scripts/deploy.sh prod restart

# 停止服务
./scripts/deploy.sh prod down
```

### 监控命令

```bash
# 查看容器资源使用
docker stats

# 查看应用健康状态
curl http://localhost:9090/actuator/health

# 查看应用指标
curl http://localhost:9090/actuator/metrics

# 查看 Prometheus 指标
curl http://localhost:9090/actuator/prometheus

# 查看 Redis 状态
docker-compose exec redis redis-cli info

# 查看 MQTT 连接状态
docker-compose exec mosquitto mosquitto_sub -h localhost -t '$SYS/broker/clients/connected' -C 1
```

## 监控和告警

### 关键指标监控

#### 应用层指标

| 指标名称 | 描述 | 正常范围 | 告警阈值 |
|----------|------|----------|----------|
| `http_server_requests_seconds` | HTTP 请求响应时间 | < 100ms | > 500ms |
| `websocket_connections_active` | 活跃 WebSocket 连接数 | 0-10000 | > 8000 |
| `mqtt_messages_processed_total` | MQTT 消息处理总数 | 递增 | 停止增长 |
| `redis_connections_active` | Redis 活跃连接数 | 5-20 | > 50 |
| `jvm_memory_used_bytes` | JVM 内存使用量 | < 80% | > 90% |
| `jvm_gc_pause_seconds` | GC 暂停时间 | < 100ms | > 500ms |

#### 系统层指标

| 指标名称 | 描述 | 正常范围 | 告警阈值 |
|----------|------|----------|----------|
| `node_cpu_seconds_total` | CPU 使用率 | < 70% | > 85% |
| `node_memory_MemAvailable_bytes` | 可用内存 | > 1GB | < 500MB |
| `node_filesystem_avail_bytes` | 可用磁盘空间 | > 5GB | < 1GB |
| `node_network_receive_bytes_total` | 网络接收字节数 | 正常波动 | 异常峰值 |

### Grafana 仪表板

访问 Grafana: http://localhost:3000 (admin/admin123)

推荐仪表板：
1. **应用概览** - 整体健康状态和关键指标
2. **WebSocket 监控** - 连接数、消息吞吐量
3. **MQTT 监控** - 消息处理、连接状态
4. **JVM 监控** - 内存、GC、线程
5. **系统监控** - CPU、内存、磁盘、网络

### 告警规则配置

创建 `config/alert_rules.yml`:

```yaml
groups:
  - name: mqtt-websocket-alerts
    rules:
      - alert: ApplicationDown
        expr: up{job="mqtt-websocket-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Application instance is down"
          description: "Application instance {{ $labels.instance }} has been down for more than 1 minute."

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, http_server_requests_seconds_bucket) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: HighMemoryUsage
        expr: (jvm_memory_used_bytes / jvm_memory_max_bytes) > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High JVM memory usage"
          description: "JVM memory usage is {{ $value | humanizePercentage }}"

      - alert: WebSocketConnectionsHigh
        expr: websocket_connections_active > 8000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High WebSocket connections"
          description: "WebSocket connections: {{ $value }}"
```

## 故障排除

### 常见问题诊断

#### 1. 应用启动失败

**症状**: 容器启动后立即退出

**诊断步骤**:
```bash
# 查看容器日志
docker-compose logs app1

# 检查配置文件
docker-compose config

# 验证依赖服务
docker-compose ps
```

**常见原因**:
- 配置文件错误
- 依赖服务未就绪
- 端口冲突
- 内存不足

#### 2. WebSocket 连接失败

**症状**: 客户端无法建立 WebSocket 连接

**诊断步骤**:
```bash
# 测试 WebSocket 连接
echo '{"type":"ping"}' | websocat ws://localhost:8080/websocket

# 检查 Nginx 配置
docker-compose exec nginx nginx -t

# 查看 Nginx 日志
docker-compose logs nginx

# 检查防火墙
netstat -tlnp | grep :80
```

**解决方案**:
- 检查 Nginx WebSocket 代理配置
- 验证防火墙规则
- 确认应用 WebSocket 端点正常

#### 3. MQTT 消息处理异常

**症状**: MQTT 消息无法正常处理

**诊断步骤**:
```bash
# 测试 MQTT 连接
docker-compose exec mosquitto mosquitto_pub -h localhost -t test -m "hello"

# 查看 MQTT 日志
docker-compose logs mosquitto

# 检查应用 MQTT 处理
docker-compose logs app1 | grep -i mqtt
```

**解决方案**:
- 验证 MQTT 代理配置
- 检查主题订阅设置
- 确认消息格式正确

#### 4. Redis 连接问题

**症状**: 应用无法连接到 Redis

**诊断步骤**:
```bash
# 测试 Redis 连接
docker-compose exec redis redis-cli ping

# 检查 Redis 配置
docker-compose exec redis redis-cli config get "*"

# 查看连接数
docker-compose exec redis redis-cli info clients
```

**解决方案**:
- 检查 Redis 连接配置
- 验证网络连通性
- 调整连接池设置

### 性能问题诊断

#### 1. 高 CPU 使用率

**诊断**:
```bash
# 查看容器 CPU 使用
docker stats --no-stream

# 查看 JVM 线程状态
docker-compose exec app1 jstack 1

# 分析 GC 日志
docker-compose logs app1 | grep -i gc
```

**优化**:
- 调整 JVM 参数
- 优化业务逻辑
- 增加实例数量

#### 2. 内存泄漏

**诊断**:
```bash
# 查看内存使用趋势
curl http://localhost:9090/actuator/metrics/jvm.memory.used

# 生成堆转储
docker-compose exec app1 jcmd 1 GC.run_finalization
docker-compose exec app1 jcmd 1 VM.gc
```

**优化**:
- 分析堆转储文件
- 检查对象生命周期
- 调整 GC 策略

#### 3. 网络延迟

**诊断**:
```bash
# 测试网络延迟
ping redis
ping mosquitto

# 查看网络统计
docker-compose exec app1 netstat -i
```

**优化**:
- 优化网络配置
- 使用连接池
- 调整超时设置

## 维护程序

### 日常维护

#### 1. 日志管理

```bash
# 日志轮转脚本
#!/bin/bash
LOG_DIR="./logs"
RETENTION_DAYS=30

# 压缩旧日志
find $LOG_DIR -name "*.log" -mtime +1 -exec gzip {} \;

# 删除过期日志
find $LOG_DIR -name "*.gz" -mtime +$RETENTION_DAYS -delete

# 清理容器日志
docker system prune -f --filter "until=24h"
```

#### 2. 数据备份

```bash
# 备份脚本
#!/bin/bash
BACKUP_DIR="./backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份 Redis 数据
docker-compose exec redis redis-cli BGSAVE
docker cp mqtt-websocket-redis:/data/dump.rdb $BACKUP_DIR/

# 备份配置文件
tar -czf $BACKUP_DIR/config.tar.gz config/

# 备份应用日志
tar -czf $BACKUP_DIR/logs.tar.gz logs/

echo "Backup completed: $BACKUP_DIR"
```

#### 3. 健康检查

```bash
# 定时健康检查脚本
#!/bin/bash
HEALTH_LOG="./logs/health-check.log"

{
    echo "=== Health Check $(date) ==="
    ./scripts/health-check.sh
    echo "=== End Health Check ==="
    echo
} >> $HEALTH_LOG

# 如果健康检查失败，发送告警
if [ $? -ne 0 ]; then
    echo "Health check failed at $(date)" | mail -s "MQTT-WebSocket Health Alert" <EMAIL>
fi
```

### 定期维护

#### 1. 系统更新

```bash
# 月度更新脚本
#!/bin/bash

# 1. 备份当前版本
./scripts/deploy.sh prod down
cp -r . ../backup-$(date +%Y%m%d)

# 2. 更新系统包
docker-compose pull

# 3. 重建镜像
./scripts/deploy.sh prod build

# 4. 启动服务
./scripts/deploy.sh prod up

# 5. 验证部署
sleep 60
./scripts/deploy.sh prod health
```

#### 2. 性能调优

```bash
# 性能调优检查清单

# JVM 调优
echo "检查 JVM 参数..."
docker-compose exec app1 java -XX:+PrintFlagsFinal -version | grep -i gc

# 连接池调优
echo "检查连接池状态..."
curl -s http://localhost:9090/actuator/metrics/hikaricp.connections.active

# 缓存命中率
echo "检查 Redis 缓存命中率..."
docker-compose exec redis redis-cli info stats | grep keyspace
```

#### 3. 安全审计

```bash
# 安全审计脚本
#!/bin/bash

echo "=== Security Audit $(date) ==="

# 检查容器安全
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image mqtt-websocket-app1

# 检查网络安全
nmap -sS -O localhost

# 检查文件权限
find . -type f -perm -002 -ls

# 检查密钥轮换
echo "Last key rotation: $(grep 'key rotation' logs/app*/application.log | tail -1)"
```

### 容量规划

#### 1. 资源使用趋势

```bash
# 资源使用统计脚本
#!/bin/bash

# CPU 使用趋势
echo "CPU Usage Trend:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}" | grep mqtt-websocket

# 内存使用趋势
echo "Memory Usage Trend:"
docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" | grep mqtt-websocket

# 网络流量趋势
echo "Network I/O Trend:"
docker stats --no-stream --format "table {{.Container}}\t{{.NetIO}}" | grep mqtt-websocket
```

#### 2. 扩容决策

**扩容指标**:
- CPU 使用率持续 > 70%
- 内存使用率持续 > 80%
- WebSocket 连接数 > 8000
- 响应时间 > 200ms

**扩容方案**:
```bash
# 水平扩容
docker-compose up -d --scale app1=3 --scale app2=3

# 垂直扩容 (修改 docker-compose.yml)
# 增加 memory 和 cpus 限制
```

## 应急响应

### 紧急情况处理

#### 1. 服务完全宕机

```bash
# 紧急恢复步骤
1. 立即回滚到上一个稳定版本
   git checkout HEAD~1
   ./scripts/deploy.sh prod up

2. 如果回滚失败，使用备份
   rm -rf current
   cp -r ../backup-YYYYMMDD current
   cd current && ./scripts/deploy.sh prod up

3. 通知相关人员
   echo "Service restored at $(date)" | mail -s "Emergency Recovery" <EMAIL>
```

#### 2. 数据库连接失败

```bash
# Redis 紧急恢复
1. 重启 Redis 服务
   docker-compose restart redis

2. 如果数据损坏，恢复备份
   docker-compose stop redis
   docker cp backup/dump.rdb mqtt-websocket-redis:/data/
   docker-compose start redis

3. 验证数据完整性
   docker-compose exec redis redis-cli dbsize
```

#### 3. 内存溢出

```bash
# 内存溢出处理
1. 立即重启受影响的实例
   docker-compose restart app1

2. 调整 JVM 内存参数
   # 在 docker-compose.yml 中修改 JAVA_OPTS
   JAVA_OPTS: "-Xmx4g -Xms2g"

3. 监控内存使用
   watch -n 5 'docker stats --no-stream'
```

### 联系信息

**紧急联系人**:
- 主要负责人: +86-xxx-xxxx-xxxx
- 备用负责人: +86-xxx-xxxx-xxxx
- 运维团队: <EMAIL>

**升级路径**:
1. 应用开发团队
2. 系统架构师
3. 技术总监
4. CTO

## 文档更新

本文档应定期更新，记录新的运维经验和最佳实践。

**更新记录**:
- v1.0 - 初始版本
- v1.1 - 添加性能调优指南
- v1.2 - 增加应急响应程序