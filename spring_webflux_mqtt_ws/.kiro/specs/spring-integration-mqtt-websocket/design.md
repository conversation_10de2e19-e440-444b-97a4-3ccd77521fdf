# Design Document

## Overview

本设计文档描述了一个基于 Spring Boot + Spring Integration 的综合消息处理平台架构。系统采用响应式编程模型，整合 MQTT、WebSocket、Protobuf 序列化和加密功能，支持集群部署和高并发处理。

### 核心技术栈
- **Spring Boot 3.x** - 应用框架
- **Spring WebFlux** - 响应式 Web 框架
- **Spring Integration** - 企业集成模式
- **Spring WebSocket** - WebSocket 支持
- **Protocol Buffers** - 消息序列化
- **Redis** - 集群状态共享和消息缓存
- **Netty** - 底层网络通信

## Architecture

### 系统架构图

```mermaid
graph TB
    subgraph "Client Layer"
        WC[Web Client]
        IOT[IoT Devices]
        API[API Clients]
    end
    
    subgraph "Load Balancer"
        LB[Nginx/HAProxy]
    end
    
    subgraph "Application Cluster"
        subgraph "Instance 1"
            WS1[WebSocket Handler]
            WF1[WebFlux Controllers]
            SI1[Spring Integration]
            MQTT1[MQTT Gateway]
        end
        
        subgraph "Instance 2"
            WS2[WebSocket Handler]
            WF2[WebFlux Controllers]
            SI2[Spring Integration]
            MQTT2[MQTT Gateway]
        end
    end
    
    subgraph "Message Infrastructure"
        REDIS[(Redis Cluster)]
        MQTT_BROKER[MQTT Broker]
    end
    
    subgraph "Security Layer"
        CRYPTO[Encryption Service]
        PROTO[Protobuf Serializer]
    end
    
    WC --> LB
    API --> LB
    IOT --> MQTT_BROKER
    
    LB --> WS1
    LB --> WS2
    LB --> WF1
    LB --> WF2
    
    SI1 --> MQTT_BROKER
    SI2 --> MQTT_BROKER
    SI1 --> REDIS
    SI2 --> REDIS
    
    SI1 --> CRYPTO
    SI2 --> CRYPTO
    SI1 --> PROTO
    SI2 --> PROTO
```

### 分层架构

1. **Presentation Layer** - WebSocket 处理器和 WebFlux 控制器
2. **Integration Layer** - Spring Integration 消息流处理
3. **Service Layer** - 业务逻辑和消息转换
4. **Infrastructure Layer** - MQTT、Redis、加密服务

## Components and Interfaces

### 1. WebSocket 组件

```java
// WebSocket 配置接口
public interface WebSocketConfigurer {
    void configureWebSocketHandlers(WebSocketHandlerRegistry registry);
}

// 消息处理器接口
public interface ReactiveWebSocketHandler {
    Mono<Void> handle(WebSocketSession session);
}
```

**职责:**
- 管理 WebSocket 连接生命周期
- 处理客户端消息接收和发送
- 支持广播和点对点消息
- 集成 Spring Security 进行认证

### 2. MQTT Integration 组件

```java
// MQTT 网关接口
public interface MqttGateway {
    void sendToMqtt(String topic, Object payload);
    
    @Gateway(requestChannel = "mqttOutboundChannel")
    void publishMessage(String topic, byte[] payload);
}

// MQTT 消息处理器
public interface MqttMessageHandler {
    void handleMqttMessage(Message<byte[]> message);
}
```

**职责:**
- MQTT 连接管理和自动重连
- 消息发布和订阅
- 消息路由和转换
- 错误处理和重试机制

### 3. 消息序列化组件

```java
// Protobuf 序列化接口
public interface ProtobufSerializer {
    <T extends GeneratedMessageV3> byte[] serialize(T message);
    <T extends GeneratedMessageV3> T deserialize(byte[] data, Class<T> clazz);
}

// 消息转换器接口
public interface MessageConverter {
    Object convert(Object source, Class<?> targetType);
}
```

**职责:**
- Protobuf 消息序列化和反序列化
- JSON 到 Protobuf 转换
- 消息格式验证
- 版本兼容性处理

### 4. 加密服务组件

```java
// 加密服务接口
public interface EncryptionService {
    byte[] encrypt(byte[] plaintext, String keyId);
    byte[] decrypt(byte[] ciphertext, String keyId);
    String generateKeyId();
}

// 密钥管理接口
public interface KeyManager {
    SecretKey getKey(String keyId);
    void rotateKey(String keyId);
}
```

**职责:**
- AES-256 消息加密/解密
- 密钥管理和轮换
- 数字签名验证
- 安全审计日志

### 5. 集群管理组件

```java
// 集群状态管理接口
public interface ClusterStateManager {
    void registerInstance(String instanceId);
    Set<String> getActiveInstances();
    void broadcastToCluster(Object message);
}

// 负载均衡接口
public interface LoadBalancer {
    String selectInstance(String key);
    void updateInstanceHealth(String instanceId, boolean healthy);
}
```

**职责:**
- 实例注册和发现
- 健康检查和故障转移
- 集群间消息广播
- 负载均衡策略

## Data Models

### 1. 消息模型

```protobuf
syntax = "proto3";

message BaseMessage {
    string id = 1;
    int64 timestamp = 2;
    string type = 3;
    string source = 4;
    map<string, string> headers = 5;
    bytes payload = 6;
    bool encrypted = 7;
    string signature = 8;
}

message MqttMessage {
    string topic = 1;
    int32 qos = 2;
    bool retained = 3;
    BaseMessage message = 4;
}

message WebSocketMessage {
    string sessionId = 1;
    string userId = 2;
    MessageType type = 3;
    BaseMessage message = 4;
    
    enum MessageType {
        BROADCAST = 0;
        UNICAST = 1;
        SYSTEM = 2;
    }
}
```

### 2. 配置模型

```java
@ConfigurationProperties(prefix = "app.mqtt")
public class MqttProperties {
    private String brokerUrl;
    private String clientId;
    private String username;
    private String password;
    private int connectionTimeout;
    private int keepAliveInterval;
    private List<String> subscribeTopics;
}

@ConfigurationProperties(prefix = "app.websocket")
public class WebSocketProperties {
    private String endpoint;
    private String allowedOrigins;
    private int maxSessionIdleTimeout;
    private int maxTextMessageBufferSize;
    private int maxBinaryMessageBufferSize;
}
```

## Error Handling

### 1. 异常处理策略

```java
@Component
public class GlobalExceptionHandler {
    
    // MQTT 连接异常处理
    @EventListener
    public void handleMqttConnectionException(MqttConnectionException ex) {
        // 记录错误日志
        // 触发重连机制
        // 发送告警通知
    }
    
    // WebSocket 异常处理
    @EventListener
    public void handleWebSocketException(WebSocketException ex) {
        // 清理会话资源
        // 通知客户端重连
        // 记录异常统计
    }
    
    // 加密解密异常处理
    @EventListener
    public void handleEncryptionException(EncryptionException ex) {
        // 记录安全事件
        // 拒绝处理消息
        // 触发安全审计
    }
}
```

### 2. 重试机制

```java
@Configuration
public class RetryConfiguration {
    
    @Bean
    public RetryTemplate mqttRetryTemplate() {
        return RetryTemplate.builder()
            .maxAttempts(3)
            .exponentialBackoff(1000, 2, 10000)
            .retryOn(MqttException.class)
            .build();
    }
    
    @Bean
    public RetryTemplate encryptionRetryTemplate() {
        return RetryTemplate.builder()
            .maxAttempts(2)
            .fixedBackoff(500)
            .retryOn(TemporaryEncryptionException.class)
            .build();
    }
}
```

### 3. 死信队列处理

```java
@ServiceActivator(inputChannel = "errorChannel")
public class DeadLetterHandler {
    
    public void handleDeadLetter(Message<?> failedMessage) {
        // 记录失败消息详情
        // 存储到死信队列
        // 发送告警通知
        // 提供手动重处理机制
    }
}
```

## Testing Strategy

### 1. 单元测试

```java
@ExtendWith(MockitoExtension.class)
class MqttMessageHandlerTest {
    
    @Mock
    private ProtobufSerializer serializer;
    
    @Mock
    private EncryptionService encryptionService;
    
    @InjectMocks
    private MqttMessageHandler handler;
    
    @Test
    void shouldHandleValidMqttMessage() {
        // 测试正常消息处理流程
    }
    
    @Test
    void shouldHandleEncryptedMessage() {
        // 测试加密消息处理
    }
    
    @Test
    void shouldHandleInvalidMessage() {
        // 测试异常消息处理
    }
}
```

### 2. 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "app.mqtt.broker-url=tcp://localhost:1883",
    "app.redis.host=localhost"
})
class IntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private MqttGateway mqttGateway;
    
    @Test
    void shouldProcessMqttToWebSocketFlow() {
        // 测试 MQTT 消息到 WebSocket 的完整流程
    }
}
```

### 3. 性能测试

```java
@Component
public class PerformanceTestRunner {
    
    public void runWebSocketLoadTest() {
        // 使用 JMeter 或 Gatling 进行 WebSocket 负载测试
        // 测试并发连接数和消息吞吐量
    }
    
    public void runMqttThroughputTest() {
        // 测试 MQTT 消息处理吞吐量
        // 验证集群扩展性能
    }
}
```

### 4. 安全测试

```java
@TestConfiguration
public class SecurityTestConfiguration {
    
    @Test
    void shouldRejectUnencryptedSensitiveData() {
        // 测试敏感数据必须加密传输
    }
    
    @Test
    void shouldValidateMessageSignature() {
        // 测试消息签名验证
    }
    
    @Test
    void shouldHandleKeyRotation() {
        // 测试密钥轮换机制
    }
}
```

## 部署和运维考虑

### 1. 容器化部署

```dockerfile
FROM openjdk:21-jre-slim
COPY target/spring-integration-mqtt-websocket.jar app.jar
EXPOSE 8080 8081
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 2. 监控指标

- WebSocket 连接数和消息吞吐量
- MQTT 消息处理延迟和错误率
- 加密解密操作性能
- JVM 内存和 GC 指标
- Redis 连接池状态

### 3. 日志策略

- 结构化日志输出 (JSON 格式)
- 分级日志记录 (ERROR, WARN, INFO, DEBUG)
- 安全事件专门记录
- 性能指标定期输出